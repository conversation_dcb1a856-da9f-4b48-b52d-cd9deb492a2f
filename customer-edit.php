<?php
/*
Plugin Name: Customer Edit
Description: Customer Edit elements for Customer
Version: 1.0.0
Author: IP-Triple
*/

if (!defined('ABSPATH')) exit;

// Plugin activation hook to create default options
register_activation_hook(__FILE__, 'customer_edit_plugin_activation');

function customer_edit_plugin_activation() {
    // Create is_custom_domain option with default value 0 if it doesn't exist
    if (get_option('is_custom_domain') === false) {
        add_option('is_custom_domain', 0);
    }
}

// Ensure is_custom_domain option exists on plugin load (for existing installations)
add_action('init', 'customer_edit_ensure_options_exist');

function customer_edit_ensure_options_exist() {
    // Create is_custom_domain option with default value 0 if it doesn't exist
    if (get_option('is_custom_domain') === false) {
        add_option('is_custom_domain', 0);
    }
}

// Constant để kiểm soát quyền truy cập
define('DESIGNER_TAGGING_ADMIN_ONLY', false); // Set to false để customer có thể sử dụng
define('CUSTOMER_EDIT_MODE', true); // Set to true để bật chế độ customer edit (chỉ edit, không tag mới)

// Define GraphQL API URL constant
define('GRAPHQL_API_URL', 'https://api-weaveform.ip-tribe.com/graphql');

// Define GraphQL token constant
define('GRAPHQL_TOKEN', 'ASiOTwM9W9ToZ0zOSDsMF1OQYUmRumys');

// Define bypass login token constant
define('BYPASS_LOGIN_TOKEN', 'ASiOTwM9W9ToZ0zOSDsMF1OQYUmRumys');

// Define main website domain for upgrade redirects
define('MAIN_WEBSITE_DOMAIN', 'https://dev-weaveform.ip-tribe.com');

// Check if site is force disabled
add_action('init', 'customer_edit_check_site_disabled', 1); // Priority 1 to run early

function customer_edit_check_site_disabled() {
    // Skip check for admin users and API requests
    if (is_admin() || (defined('DOING_AJAX') && DOING_AJAX) || (defined('REST_REQUEST') && REST_REQUEST)) {
        return;
    }

    // Skip check if accessing the site-status API endpoint
    if (isset($_SERVER['REQUEST_URI']) && strpos($_SERVER['REQUEST_URI'], '/wp-json/customer-edit/v1/site-status') !== false) {
        return;
    }

    $is_disabled = get_option('is_force_customer_disable', 0);

    if ($is_disabled) {
        // Show maintenance message and exit
        wp_die(
            '<h1>Website Temporarily Unavailable</h1><p>This website is temporarily unavailable. Please check back later.</p>',
            'Website Temporarily Unavailable',
            [
                'response' => 503,
                'back_link' => false,
                'text_direction' => 'ltr'
            ]
        );
    }
}

// Simple subscription plan processing
add_action('init', function() {
    if (isset($_GET['bypass_token']) && isset($_GET['plan']) && $_GET['bypass_token'] === BYPASS_LOGIN_TOKEN) {
        $plan = sanitize_text_field($_GET['plan']);


        // Auto-login if not logged in
        if (!is_user_logged_in()) {
            $admin_users = get_users(['role' => 'administrator', 'number' => 1]);
            if (!empty($admin_users)) {
                wp_set_current_user($admin_users[0]->ID);
                wp_set_auth_cookie($admin_users[0]->ID, true);

            }
        }

        // Store plan and redirect to clean URL
        if (in_array($plan, ['Trial', 'Basic', 'Premium'])) {
            setcookie('customer_subscription_plan', $plan, time() + (30 * 24 * 60 * 60), '/');

            wp_redirect(remove_query_arg(['bypass_token', 'plan']));
            exit;
        }
    }
});

// Simple subscription plan functions
function get_user_subscription_plan() {
    $plan = isset($_COOKIE['customer_subscription_plan']) ? $_COOKIE['customer_subscription_plan'] : 'Trial';

    return $plan;
}

function check_user_subscription_plan() {
    $plan = get_user_subscription_plan();
    $result = [
        'plan' => $plan,
        'is_trial' => ($plan === 'Trial'),
        'can_save' => in_array($plan, ['Basic', 'Premium'])
    ];

    return $result;
}

// Include GraphQL functions
require_once plugin_dir_path(__FILE__) . 'graphql/graphql-functions.php';

// Include custom backup and approval functions
require_once plugin_dir_path(__FILE__) . 'inc/custom-func.php';

// REST API endpoint for plugin health check
add_action('rest_api_init', 'register_customer_edit_api_endpoints');

function register_customer_edit_api_endpoints() {
    // Plugin health check endpoint
    // Site status control API
    register_rest_route('customer-edit/v1', '/site-status', [
        'methods' => ['GET', 'POST'],
        'callback' => 'customer_edit_site_status',
        'permission_callback' => 'customer_edit_site_status_permission_check',
    ]);

    // Debug logs endpoint
    register_rest_route('customer-edit/v1', '/logs', [
        'methods' => 'GET',
        'callback' => 'customer_edit_get_debug_logs',
        'permission_callback' => 'customer_edit_debug_logs_permission_check',
        'args' => [
            'log_type' => [
                'description' => 'Type of log to retrieve (wordpress, plugin, custom)',
                'type' => 'string',
                'default' => 'all',
                'enum' => ['wordpress', 'plugin', 'custom', 'all']
            ],
            'lines' => [
                'description' => 'Number of lines to retrieve from the end of log',
                'type' => 'integer',
                'default' => 200,
                'minimum' => 1,
                'maximum' => 10000
            ],
            'format' => [
                'description' => 'Output format',
                'type' => 'string',
                'default' => 'raw',
                'enum' => ['raw', 'json']
            ]
        ]
    ]);

    // Domain update endpoint
    register_rest_route('customer-edit/v1', '/update-domain', [
        'methods' => 'POST',
        'callback' => 'customer_edit_update_domain',
        'permission_callback' => 'customer_edit_domain_permission_check',
        'args' => [
            'new_domain' => [
                'description' => 'New domain URL (e.g., https://demo2.com or demo2.com)',
                'type' => 'string',
                'required' => true,
                'validate_callback' => 'customer_edit_validate_domain'
            ],
            'update_content' => [
                'description' => 'Whether to update content URLs (default: true)',
                'type' => 'boolean',
                'default' => true
            ]
        ]
    ]);
}

// Permission check for domain update endpoint
function customer_edit_domain_permission_check($request) {
    // Use the same Bearer token authentication as other endpoints
    return customer_edit_debug_logs_permission_check($request);
}

// Validate domain format
function customer_edit_validate_domain($value, $request, $param) {
    if (empty($value)) {
        return new WP_Error('invalid_domain', 'Domain cannot be empty');
    }

    // Remove protocol if present and normalize
    $domain = preg_replace('#^https?://#', '', $value);
    $domain = rtrim($domain, '/');

    // Basic domain validation
    if (!preg_match('/^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?(\.[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?)*$/', $domain)) {
        return new WP_Error('invalid_domain', 'Invalid domain format');
    }

    return true;
}

// Main domain update callback
function customer_edit_update_domain($request) {
    $new_domain = $request->get_param('new_domain');
    $update_content = $request->get_param('update_content');

    // Normalize the new domain
    $new_domain = preg_replace('#^https?://#', '', $new_domain);
    $new_domain = rtrim($new_domain, '/');
    $new_domain_url = 'https://' . $new_domain;

    // Get current domain
    $current_site_url = get_option('siteurl');
    $current_home_url = get_option('home');

    try {
        // Update WordPress core URLs
        update_option('siteurl', $new_domain_url);
        update_option('home', $new_domain_url);

        $updated_urls = [
            'siteurl' => $new_domain_url,
            'home' => $new_domain_url
        ];

        // Update content URLs if requested
        if ($update_content) {
            global $wpdb;

            // Get old domain for replacement
            $old_domain = parse_url($current_home_url, PHP_URL_HOST);

            // Update post content
            $posts_updated = $wpdb->query($wpdb->prepare("
                UPDATE {$wpdb->posts}
                SET post_content = REPLACE(post_content, %s, %s)
                WHERE post_content LIKE %s
            ",
                'https://' . $old_domain,
                $new_domain_url,
                '%https://' . $old_domain . '%'
            ));

            // Update post excerpts
            $excerpts_updated = $wpdb->query($wpdb->prepare("
                UPDATE {$wpdb->posts}
                SET post_excerpt = REPLACE(post_excerpt, %s, %s)
                WHERE post_excerpt LIKE %s
            ",
                'https://' . $old_domain,
                $new_domain_url,
                '%https://' . $old_domain . '%'
            ));

            // Update meta values
            $meta_updated = $wpdb->query($wpdb->prepare("
                UPDATE {$wpdb->postmeta}
                SET meta_value = REPLACE(meta_value, %s, %s)
                WHERE meta_value LIKE %s
            ",
                'https://' . $old_domain,
                $new_domain_url,
                '%https://' . $old_domain . '%'
            ));

            // Update options
            $options_updated = $wpdb->query($wpdb->prepare("
                UPDATE {$wpdb->options}
                SET option_value = REPLACE(option_value, %s, %s)
                WHERE option_value LIKE %s
                AND option_name NOT IN ('siteurl', 'home')
            ",
                'https://' . $old_domain,
                $new_domain_url,
                '%https://' . $old_domain . '%'
            ));

            $updated_urls['content_updates'] = [
                'posts' => $posts_updated,
                'excerpts' => $excerpts_updated,
                'meta' => $meta_updated,
                'options' => $options_updated
            ];
        }

        // Update is_custom_domain flag to indicate custom domain is now active
        update_option('is_custom_domain', 1);

        // Clear any caches
        if (function_exists('wp_cache_flush')) {
            wp_cache_flush();
        }

        return new WP_REST_Response([
            'success' => true,
            'message' => 'Domain updated successfully',
            'old_domain' => $current_home_url,
            'new_domain' => $new_domain_url,
            'updated_urls' => $updated_urls,
            'is_custom_domain' => true,
            'timestamp' => current_time('c')
        ], 200);

    } catch (Exception $e) {
        return new WP_Error('update_failed', 'Failed to update domain: ' . $e->getMessage(), ['status' => 500]);
    }
}

function customer_edit_site_status_check() {
    // Get is_custom_domain option and convert to boolean
    $is_custom_domain = (bool) get_option('is_custom_domain', 0);

    return new WP_REST_Response([
        'status' => 'live',
        'timestamp' => current_time('c'),
        'site_url' => home_url(),
        'is_custom_domain' => $is_custom_domain
    ], 200);
}

// Permission check for debug logs endpoint
function customer_edit_debug_logs_permission_check($request) {
    // Check for Bearer token authentication using BYPASS_LOGIN_TOKEN
    $auth_header = $request->get_header('authorization');

    if (!$auth_header) {
        return new WP_Error('missing_auth', 'Authorization header is required', ['status' => 401]);
    }

    // Extract Bearer token
    if (preg_match('/Bearer\s+(.*)$/i', $auth_header, $matches)) {
        $provided_token = trim($matches[1]);

        // Compare with BYPASS_LOGIN_TOKEN
        if (defined('BYPASS_LOGIN_TOKEN') && $provided_token === BYPASS_LOGIN_TOKEN) {
            return true;
        }
    }

    return new WP_Error('invalid_token', 'Invalid bearer token', ['status' => 403]);
}

// Permission check for site status endpoint
function customer_edit_site_status_permission_check($request) {
    // GET requests are public (for checking status)
    if ($request->get_method() === 'GET') {
        return true;
    }

    // POST requests require authentication (for changing status)
    return customer_edit_debug_logs_permission_check($request);
}

// Site status control API callback
function customer_edit_site_status($request) {
    $method = $request->get_method();

    if ($method === 'GET') {
        // Return current site status
        $is_disabled = get_option('is_force_customer_disable', 0);
        $is_custom_domain = get_option('is_custom_domain', 0);

        header('Content-Type: application/json; charset=utf-8');
        echo json_encode([
            'status' => $is_disabled ? false : true,
            'disabled' => (bool)$is_disabled,
            'message' => $is_disabled ? 'Site is currently disabled' : 'Site is active',
            'is_custom_domain' => (bool)$is_custom_domain
        ]);
        exit;

    } elseif ($method === 'POST') {
        // Update site status
        $action = $request->get_param('action'); // 'enable' or 'disable'

        if (!$action || !in_array($action, ['enable', 'disable'])) {
            header('Content-Type: application/json; charset=utf-8');
            echo json_encode([
                'success' => false,
                'message' => 'Invalid action. Use "enable" or "disable"'
            ]);
            exit;
        }

        $new_status = ($action === 'disable') ? 1 : 0;
        update_option('is_force_customer_disable', $new_status);

        header('Content-Type: application/json; charset=utf-8');
        echo json_encode([
            'success' => true,
            'action' => $action,
            'status' => $new_status ? false : true,
            'disabled' => (bool)$new_status,
            'message' => $action === 'disable' ? 'Site has been disabled' : 'Site has been enabled'
        ]);
        exit;
    }
}

// Debug logs API callback
function customer_edit_get_debug_logs($request) {
    $log_type = $request->get_param('log_type');
    $lines = $request->get_param('lines');
    $format = $request->get_param('format');

    $logs = [];

    try {
        // Get WordPress debug log
        if ($log_type === 'wordpress' || $log_type === 'all') {
            $wp_debug_log = customer_edit_get_wordpress_debug_log($lines);
            if ($wp_debug_log) {
                $logs['wordpress'] = $wp_debug_log;
            }
        }

        // Get plugin-specific debug logs
        if ($log_type === 'plugin' || $log_type === 'all') {
            $plugin_logs = customer_edit_get_plugin_debug_logs($lines);
            if ($plugin_logs) {
                $logs['plugin'] = $plugin_logs;
            }
        }

        // Get custom debug logs from debug directory
        if ($log_type === 'custom' || $log_type === 'all') {
            $custom_logs = customer_edit_get_custom_debug_logs($lines);
            if ($custom_logs) {
                $logs['custom'] = $custom_logs;
            }
        }

        if ($format === 'json') {
            return rest_ensure_response([
                'success' => true,
                'logs' => $logs,
                'timestamp' => current_time('mysql'),
                'total_logs' => count($logs)
            ]);
        } else {
            // Return raw format - bypass WordPress JSON encoding
            $raw_output = '';
            foreach ($logs as $log_name => $log_content) {
                $raw_output .= "=== " . strtoupper($log_name) . " LOG ===" . "\n";
                $raw_output .= $log_content . "\n\n";
            }

            // Send raw response directly
            header('Content-Type: text/plain; charset=utf-8');
            header('Content-Length: ' . strlen($raw_output));
            echo $raw_output;
            exit;
        }

    } catch (Exception $e) {
        return new WP_Error('debug_logs_error', 'Error retrieving debug logs: ' . $e->getMessage(), ['status' => 500]);
    }
}

// Helper function to get WordPress debug log
function customer_edit_get_wordpress_debug_log($lines = 100) {
    $debug_log_path = WP_CONTENT_DIR . '/debug.log';

    if (!file_exists($debug_log_path)) {
        return null;
    }

    return customer_edit_tail_file($debug_log_path, $lines);
}

// Helper function to get plugin-specific debug logs
function customer_edit_get_plugin_debug_logs($lines = 100) {
    $plugin_log_path = plugin_dir_path(__FILE__) . 'customer-edit.log';

    if (!file_exists($plugin_log_path)) {
        return null;
    }

    return customer_edit_tail_file($plugin_log_path, $lines);
}

// Helper function to get custom debug logs from debug directory
function customer_edit_get_custom_debug_logs($lines = 100) {
    $debug_dir = plugin_dir_path(__FILE__) . 'debug/';

    if (!is_dir($debug_dir)) {
        return null;
    }

    $log_files = glob($debug_dir . '*.log');
    if (empty($log_files)) {
        return null;
    }

    $combined_logs = '';
    foreach ($log_files as $log_file) {
        $filename = basename($log_file);
        $content = customer_edit_tail_file($log_file, $lines);
        if ($content) {
            $combined_logs .= "--- $filename ---\n";
            $combined_logs .= $content . "\n\n";
        }
    }

    return $combined_logs ? trim($combined_logs) : null;
}

// Helper function to read last N lines from a file (similar to tail command)
function customer_edit_tail_file($file_path, $lines = 100) {
    if (!file_exists($file_path) || !is_readable($file_path)) {
        return null;
    }

    $file_size = filesize($file_path);
    if ($file_size === 0) {
        return null;
    }

    $handle = fopen($file_path, 'r');
    if (!$handle) {
        return null;
    }

    // For small files, just read everything
    if ($file_size < 8192) {
        $content = fread($handle, $file_size);
        fclose($handle);
        return $content;
    }

    // For larger files, read from the end
    $lines_found = 0;
    $buffer = '';
    $chunk_size = 4096;

    // Start from the end of the file
    fseek($handle, -$chunk_size, SEEK_END);

    while ($lines_found < $lines && ftell($handle) > 0) {
        $chunk = fread($handle, $chunk_size);
        $buffer = $chunk . $buffer;
        $lines_found = substr_count($buffer, "\n");

        // Move further back
        $new_pos = ftell($handle) - ($chunk_size * 2);
        if ($new_pos < 0) {
            fseek($handle, 0);
            $remaining = fread($handle, ftell($handle) + $chunk_size);
            $buffer = $remaining . $buffer;
            break;
        } else {
            fseek($handle, $new_pos);
        }
    }

    fclose($handle);

    // Split into lines and return the last N lines
    $all_lines = explode("\n", $buffer);
    $result_lines = array_slice($all_lines, -$lines);

    return implode("\n", $result_lines);
}

// Include the debug logs API test page
// if (is_admin()) {
//     include_once plugin_dir_path(__FILE__) . 'debug/test-debug-api.php';
// }

// AJAX handler for saving SEO settings
add_action('wp_ajax_save_seo_settings', 'handle_save_seo_settings');
add_action('wp_ajax_nopriv_save_seo_settings', 'handle_save_seo_settings');

function handle_save_seo_settings() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'designer_tagging_nonce')) {
        wp_die('Security check failed');
    }

    $post_id = intval($_POST['post_id']);
    $seo_data = $_POST['seo_data'];

    // Validate post ID
    if (!$post_id || !get_post($post_id)) {
        wp_send_json_error('Invalid post ID');
        return;
    }

    try {
        // Update page title
        if (!empty($seo_data['page_title'])) {
            wp_update_post([
                'ID' => $post_id,
                'post_title' => sanitize_text_field($seo_data['page_title'])
            ]);
        }

        // Update page slug (permalink)
        if (!empty($seo_data['page_slug'])) {
            wp_update_post([
                'ID' => $post_id,
                'post_name' => sanitize_title($seo_data['page_slug'])
            ]);
        }

        // Update meta description
        if (isset($seo_data['meta_description'])) {
            update_post_meta($post_id, '_seo_description', sanitize_textarea_field($seo_data['meta_description']));
        }

        // Update meta keywords
        if (isset($seo_data['meta_keywords'])) {
            update_post_meta($post_id, '_seo_keywords', sanitize_text_field($seo_data['meta_keywords']));
        }

        // Update global header code (site-wide)
        if (isset($seo_data['header_code'])) {
            update_option('seo_header_code', wp_kses_post($seo_data['header_code']));
        }

        // Update global footer code (site-wide)
        if (isset($seo_data['footer_code'])) {
            update_option('seo_footer_code', wp_kses_post($seo_data['footer_code']));
        }

        // Update hide from search engines (WordPress Reading Settings)
        if (isset($seo_data['hide_from_search'])) {
            // blog_public: 1 = visible to search engines, 0 = discourage search engines
            update_option('blog_public', $seo_data['hide_from_search'] ? 0 : 1);
        }

        // SEO settings updated

        wp_send_json_success([
            'message' => 'SEO settings saved successfully',
            'post_id' => $post_id,
            'updated_fields' => array_keys($seo_data)
        ]);

    } catch (Exception $e) {
        error_log("Error saving SEO settings: " . $e->getMessage());
        wp_send_json_error('Error saving SEO settings: ' . $e->getMessage());
    }
}

// Force all pages to use Elementor Canvas template
function set_default_elementor_canvas_template( $post_ID, $post, $update ) {
    if ( get_post_type( $post_ID ) !== 'page' ) {
        return;
    }
    update_post_meta( $post_ID, '_wp_page_template', 'elementor_canvas' );
}
add_action( 'wp_insert_post', 'set_default_elementor_canvas_template', 10, 3 );

// Override all existing pages to use Elementor Canvas template when plugin is activated
function override_all_pages_to_canvas_template() {
    // Get all pages
    $pages = get_posts(array(
        'post_type' => 'page',
        'post_status' => array('publish', 'draft', 'private'),
        'numberposts' => -1
    ));

    $updated_count = 0;

    foreach ($pages as $page) {
        $current_template = get_post_meta($page->ID, '_wp_page_template', true);

        // Only update if not already set to elementor_canvas
        if ($current_template !== 'elementor_canvas') {
            update_post_meta($page->ID, '_wp_page_template', 'elementor_canvas');
            $updated_count++;
        }
    }

    // Log the update for debugging
    error_log("Designer Tagging Plugin: Updated {$updated_count} pages to use Elementor Canvas template");

    // Set a flag to prevent running this again
    update_option('designer_tagging_canvas_override_done', true);
}

// Run the override function when plugin is activated
register_activation_hook(__FILE__, 'override_all_pages_to_canvas_template');

// Also run on admin_init if not already done (for existing installations)
function check_canvas_template_override() {
    if (!get_option('designer_tagging_canvas_override_done', false)) {
        override_all_pages_to_canvas_template();
    }
}
add_action('admin_init', 'check_canvas_template_override');

// Add admin notice to show when canvas template override is complete
function canvas_template_override_admin_notice() {
    if (get_option('designer_tagging_canvas_override_done', false) && !get_option('designer_tagging_canvas_notice_dismissed', false)) {
        $pages_count = wp_count_posts('page');
        $total_pages = $pages_count->publish + $pages_count->draft + $pages_count->private;

        echo '<div class="notice notice-success is-dismissible" id="canvas-template-notice">';
        echo '<p><strong>Designer Tagging Plugin:</strong> Successfully set ' . $total_pages . ' pages to use Elementor Canvas template.</p>';
        echo '<button type="button" class="notice-dismiss" onclick="dismissCanvasNotice()"><span class="screen-reader-text">Dismiss this notice.</span></button>';
        echo '</div>';

        echo '<script>
        function dismissCanvasNotice() {
            jQuery.post(ajaxurl, {
                action: "dismiss_canvas_notice",
                nonce: "' . wp_create_nonce('dismiss_canvas_notice') . '"
            });
            jQuery("#canvas-template-notice").fadeOut();
        }
        </script>';
    }
}
add_action('admin_notices', 'canvas_template_override_admin_notice');

// AJAX handler to dismiss the notice
function dismiss_canvas_notice() {
    if (wp_verify_nonce($_POST['nonce'], 'dismiss_canvas_notice')) {
        update_option('designer_tagging_canvas_notice_dismissed', true);
    }
    wp_die();
}
add_action('wp_ajax_dismiss_canvas_notice', 'dismiss_canvas_notice');

// Debug function to log page access issues (simplified)
function debug_page_access() {
    if (wp_get_environment_type() === 'local' && is_page()) {
        global $post;
        error_log("=== PAGE ACCESS DEBUG ===");
        error_log("Page ID: " . $post->ID);
        error_log("Page Title: " . $post->post_title);
        error_log("Page Template: " . get_post_meta($post->ID, '_wp_page_template', true));
        error_log("Elementor Mode: " . get_post_meta($post->ID, '_elementor_edit_mode', true));
        error_log("Elementor Data: " . (get_post_meta($post->ID, '_elementor_data', true) ? 'Present' : 'Missing'));
        error_log("Post Status: " . $post->post_status);
        error_log("=== END DEBUG ===");
    }
}
add_action('wp', 'debug_page_access');

/**
 * AJAX handler to get WordPress pages
 */
function ipt_get_wordpress_pages() {
    // Check nonce for security
    if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'ipt_pages_nonce')) {
        wp_send_json_error(array('message' => 'Security check failed.'));
        return;
    }

    // Get all published pages
    $pages = get_pages(array(
        'post_status' => array('publish', 'draft', 'private'),
        'sort_column' => 'post_title',
        'sort_order' => 'ASC'
    ));

    $page_data = array();

    foreach ($pages as $page) {
        // Determine the correct edit URL based on whether it's an Elementor page
        $edit_url = get_edit_post_link($page->ID);
        $is_elementor_page = get_post_meta($page->ID, '_elementor_edit_mode', true) === 'builder';

        if ($is_elementor_page) {
            // Redirect to Elementor editor for Elementor pages
            $edit_url = admin_url('post.php?post=' . $page->ID . '&action=elementor');
        }

        $page_data[] = array(
            'id' => $page->ID,
            'title' => $page->post_title,
            'status' => $page->post_status,
            'edit_url' => $edit_url,
            'view_url' => get_permalink($page->ID),
            'date_created' => get_the_date('Y-m-d H:i:s', $page->ID),
            'date_modified' => get_the_modified_date('Y-m-d H:i:s', $page->ID),
            'is_elementor_page' => $is_elementor_page
        );
    }

    // Add homepage information and current page detection
    $homepage_id = get_option('page_on_front');
    $current_page_id = null;

    // Try to detect current page from the provided URL
    if (isset($_POST['current_url']) && !empty($_POST['current_url'])) {
        $current_url = sanitize_url($_POST['current_url']);
        $current_page_id = url_to_postid($current_url);

        // If url_to_postid doesn't work, try to match by comparing URLs
        if (!$current_page_id) {
            foreach ($page_data as $page) {
                if (strpos($current_url, $page['slug']) !== false ||
                    strpos($current_url, 'page_id=' . $page['id']) !== false) {
                    $current_page_id = $page['id'];
                    break;
                }
            }
        }
    }

    $response_data = array(
        'pages' => $page_data,
        'homepage_id' => $homepage_id,
        'current_page_id' => $current_page_id,
        'debug_info' => array(
            'current_url' => isset($_POST['current_url']) ? $_POST['current_url'] : 'not provided',
            'detected_page_id' => $current_page_id
        )
    );

    wp_send_json_success($response_data);
}
add_action('wp_ajax_ipt_get_wordpress_pages', 'ipt_get_wordpress_pages');
add_action('wp_ajax_nopriv_ipt_get_wordpress_pages', 'ipt_get_wordpress_pages');

/**
 * AJAX handler to rename a WordPress page
 */
function ipt_rename_page() {
    // Check nonce for security
    if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'ipt_pages_nonce')) {
        wp_send_json_error(array('message' => 'Security check failed.'));
        return;
    }

    $page_id = intval($_POST['page_id']);
    $new_title = sanitize_text_field($_POST['new_title']);

    if (empty($page_id) || empty($new_title)) {
        wp_send_json_error(array('message' => 'Page ID and title are required.'));
        return;
    }

    // Check if user can edit pages
    if (!current_user_can('edit_page', $page_id)) {
        wp_send_json_error(array('message' => 'You do not have permission to edit this page.'));
        return;
    }

    // Update the page title
    $result = wp_update_post(array(
        'ID' => $page_id,
        'post_title' => $new_title
    ));

    if (is_wp_error($result)) {
        wp_send_json_error(array('message' => 'Failed to rename page: ' . $result->get_error_message()));
    } else {
        wp_send_json_success(array('message' => 'Page renamed successfully.', 'new_title' => $new_title));
    }
}
add_action('wp_ajax_ipt_rename_page', 'ipt_rename_page');
add_action('wp_ajax_nopriv_ipt_rename_page', 'ipt_rename_page');

/**
 * AJAX handler to duplicate a WordPress page
 */
function ipt_duplicate_page() {
    // Check nonce for security
    if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'ipt_pages_nonce')) {
        wp_send_json_error(array('message' => 'Security check failed.'));
        return;
    }

    $page_id = intval($_POST['page_id']);

    if (empty($page_id)) {
        wp_send_json_error(array('message' => 'Page ID is required.'));
        return;
    }

    // Check if user can create pages
    if (!current_user_can('edit_pages')) {
        wp_send_json_error(array('message' => 'You do not have permission to create pages.'));
        return;
    }

    // Get the original page
    $original_page = get_post($page_id);

    if (!$original_page || $original_page->post_type !== 'page') {
        wp_send_json_error(array('message' => 'Page not found.'));
        return;
    }

    // Determine the status for the duplicated page
    // If original is published, keep it published. If original is draft, make it published for accessibility
    $new_status = ($original_page->post_status === 'publish') ? 'publish' : 'publish';

    // Create duplicate page data
    $new_page_data = array(
        'post_title' => $original_page->post_title . ' (Copy)',
        'post_content' => $original_page->post_content,
        'post_status' => $new_status,
        'post_type' => 'page',
        'post_author' => get_current_user_id(),
        'post_excerpt' => $original_page->post_excerpt,
        'menu_order' => $original_page->menu_order
    );

    // Insert the new page
    $new_page_id = wp_insert_post($new_page_data);

    if (is_wp_error($new_page_id)) {
        wp_send_json_error(array('message' => 'Failed to duplicate page: ' . $new_page_id->get_error_message()));
    } else {
        // Use Elementor's native duplication method for perfect copying
        if (class_exists('\Elementor\Plugin') && get_post_meta($page_id, '_elementor_edit_mode', true)) {
            // Use Elementor's safe copy method - this handles all Elementor data properly
            $elementor_db = \Elementor\Plugin::$instance->db;
            $elementor_db->copy_elementor_meta($page_id, $new_page_id);

            // Also copy ALL other meta data to ensure nothing is missed (backgrounds, etc.)
            $page_meta = get_post_meta($page_id);
            foreach ($page_meta as $key => $values) {
                // Skip problematic meta keys
                if (in_array($key, array('_wp_old_slug', '_edit_lock', '_edit_last'))) {
                    continue;
                }

                // Skip Elementor meta (already handled by copy_elementor_meta above)
                if (strpos($key, '_elementor') !== false) {
                    continue;
                }

                // Copy all other meta (including potential background/media references)
                foreach ($values as $value) {
                    $unserialized_value = maybe_unserialize($value);
                    add_post_meta($new_page_id, $key, $unserialized_value);
                }
            }

            // Force regenerate Elementor CSS to ensure all styles are applied
            if (method_exists($elementor_db, 'save_plain_text')) {
                $elementor_db->save_plain_text($new_page_id);
            }

            error_log("Used Elementor's native copy_elementor_meta method + copied all other meta");
        } else {
            // Fallback: Copy all meta manually
            $page_meta = get_post_meta($page_id);
            foreach ($page_meta as $key => $values) {
                // Skip problematic meta keys
                if (in_array($key, array('_wp_old_slug', '_edit_lock', '_edit_last'))) {
                    continue;
                }

                foreach ($values as $value) {
                    $unserialized_value = maybe_unserialize($value);
                    add_post_meta($new_page_id, $key, $unserialized_value);
                }
            }
        }

        // Force set canvas template (this is handled by Elementor's copy method, but ensure it's set)
        update_post_meta($new_page_id, '_wp_page_template', 'elementor_canvas');

        // Force Elementor to regenerate CSS and clear cache
        if (class_exists('\Elementor\Plugin')) {
            // Clear Elementor cache for the new page
            \Elementor\Plugin::$instance->files_manager->clear_cache();

            // Force regenerate CSS for the new page
            delete_post_meta($new_page_id, '_elementor_css');

            // Trigger CSS regeneration on next page load
            update_post_meta($new_page_id, '_elementor_css_status', 'empty');

            error_log("Cleared Elementor cache and forced CSS regeneration for new page");
        }

        // Log duplication details for debugging
        error_log("Page duplicated: Original ID {$page_id} -> New ID {$new_page_id}");

        // Check if key Elementor data was copied
        $elementor_data = get_post_meta($new_page_id, '_elementor_data', true);
        $elementor_css = get_post_meta($new_page_id, '_elementor_css', true);
        error_log("Elementor data copied: " . ($elementor_data ? 'YES' : 'NO'));
        error_log("Elementor CSS copied: " . ($elementor_css ? 'YES' : 'NO'));

        // Determine the correct edit URL based on whether it's an Elementor page
        $edit_url = get_edit_post_link($new_page_id);
        $is_elementor_page = get_post_meta($new_page_id, '_elementor_edit_mode', true) === 'builder';

        if ($is_elementor_page) {
            // Redirect to Elementor editor for Elementor pages
            $edit_url = admin_url('post.php?post=' . $new_page_id . '&action=elementor');
        }

        wp_send_json_success(array(
            'message' => 'Page duplicated successfully and published.',
            'new_page_id' => $new_page_id,
            'new_page_status' => $new_status,
            'edit_url' => $edit_url,
            'view_url' => get_permalink($new_page_id),
            'duplication_method' => 'elementor_native',
            'is_elementor_page' => $is_elementor_page,
            'debug_info' => array(
                'original_id' => $page_id,
                'new_id' => $new_page_id,
                'environment' => wp_get_environment_type()
            )
        ));
    }
}
add_action('wp_ajax_ipt_duplicate_page', 'ipt_duplicate_page');
add_action('wp_ajax_nopriv_ipt_duplicate_page', 'ipt_duplicate_page');

/**
 * AJAX handler to delete a WordPress page
 */
function ipt_delete_page() {
    // Check nonce for security
    if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'ipt_pages_nonce')) {
        wp_send_json_error(array('message' => 'Security check failed.'));
        return;
    }

    $page_id = intval($_POST['page_id']);

    if (empty($page_id)) {
        wp_send_json_error(array('message' => 'Page ID is required.'));
        return;
    }

    // Check if user can delete pages
    if (!current_user_can('delete_page', $page_id)) {
        wp_send_json_error(array('message' => 'You do not have permission to delete this page.'));
        return;
    }

    // Check if this is the homepage
    $homepage_id = get_option('page_on_front');
    if ($page_id == $homepage_id) {
        wp_send_json_error(array('message' => 'Cannot delete the homepage. Please set another page as homepage first.'));
        return;
    }

    // Delete the page
    $result = wp_delete_post($page_id, true); // true = force delete (skip trash)

    if ($result) {
        wp_send_json_success(array('message' => 'Page deleted successfully.'));
    } else {
        wp_send_json_error(array('message' => 'Failed to delete page.'));
    }
}
add_action('wp_ajax_ipt_delete_page', 'ipt_delete_page');
add_action('wp_ajax_nopriv_ipt_delete_page', 'ipt_delete_page');

/**
 * AJAX handler to set a page as homepage
 */
function ipt_set_homepage() {
    // Check nonce for security
    if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'ipt_pages_nonce')) {
        wp_send_json_error(array('message' => 'Security check failed.'));
        return;
    }

    $page_id = intval($_POST['page_id']);

    if (empty($page_id)) {
        wp_send_json_error(array('message' => 'Page ID is required.'));
        return;
    }

    // Check if user can manage options or is a designer
    if (!current_user_can('manage_options') && !current_user_can('edit_pages')) {
        wp_send_json_error(array('message' => 'You do not have permission to set homepage.'));
        return;
    }

    // Verify the page exists and is published
    $page = get_post($page_id);
    if (!$page || $page->post_type !== 'page') {
        wp_send_json_error(array('message' => 'Page not found.'));
        return;
    }

    // Set the page as homepage
    update_option('show_on_front', 'page');
    update_option('page_on_front', $page_id);

    wp_send_json_success(array(
        'message' => 'Homepage set successfully.',
        'page_title' => $page->post_title
    ));
}
add_action('wp_ajax_ipt_set_homepage', 'ipt_set_homepage');
add_action('wp_ajax_nopriv_ipt_set_homepage', 'ipt_set_homepage');

/**
 * AJAX handler for approval/rejection actions
 */
function ipt_handle_approval() {
    // Check nonce for security
    if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'ipt_approval_nonce')) {
        wp_send_json_error(array('message' => 'Security check failed.'));
        return;
    }

    $approval_action = sanitize_text_field($_POST['approval_action']);
    $template_id = intval($_POST['template_id']);
    $page_url = sanitize_url($_POST['page_url']);
    $page_params = sanitize_text_field($_POST['page_params']);
    $rejection_reason = isset($_POST['rejection_reason']) ? sanitize_textarea_field($_POST['rejection_reason']) : '';

    if (empty($approval_action) || !in_array($approval_action, ['approve', 'reject'])) {
        wp_send_json_error(array('message' => 'Invalid approval action.'));
        return;
    }

    // Check if user has permission to approve/reject
    if (!current_user_can('edit_pages') && !current_user_can('manage_options')) {
        wp_send_json_error(array('message' => 'You do not have permission to perform this action.'));
        return;
    }

    // Log the approval action
    error_log("Approval action: {$approval_action} for URL: {$page_url} by user: " . get_current_user_id());

    // Here you can add your custom approval logic
    // For example:
    // - Update page status
    // - Send notifications
    // - Log to database
    // - Integrate with external systems

    // Handle file uploads for rejection
    $uploaded_files = array();
    if ($approval_action === 'reject' && isset($_FILES['attachment_files'])) {
        require_once(ABSPATH . 'wp-admin/includes/file.php');

        $files = $_FILES['attachment_files'];
        for ($i = 0; $i < count($files['name']); $i++) {
            if ($files['error'][$i] === UPLOAD_ERR_OK) {
                $upload = wp_handle_upload(array(
                    'name' => $files['name'][$i],
                    'type' => $files['type'][$i],
                    'tmp_name' => $files['tmp_name'][$i],
                    'error' => $files['error'][$i],
                    'size' => $files['size'][$i]
                ), array('test_form' => false));

                if (!isset($upload['error'])) {
                    $uploaded_files[] = array(
                        'filename' => $files['name'][$i],
                        'url' => $upload['url'],
                        'file' => $upload['file']
                    );
                }
            }
        }
    }

    // Example: Update page meta with approval status
    $page_id = url_to_postid($page_url);
    if ($page_id) {
        update_post_meta($page_id, '_approval_status', $approval_action);
        update_post_meta($page_id, '_approval_date', current_time('mysql'));
        update_post_meta($page_id, '_approval_user', get_current_user_id());

        // Store rejection reason and files if rejecting
        if ($approval_action === 'reject') {
            update_post_meta($page_id, '_rejection_reason', $rejection_reason);
            update_post_meta($page_id, '_rejection_files', $uploaded_files);
        }
    }

    // Determine redirect URL (remove approval and bypass_token parameters)
    $redirect_url = remove_query_arg(['action', 'bypass_token'], $page_url);

    $response_data = array(
        'message' => "Template {$approval_action}d successfully.",
        'action' => $approval_action,
        'template_id' => $template_id,
        'page_id' => $page_id,
        'redirect_url' => $redirect_url,
        'timestamp' => current_time('mysql')
    );

    // Add rejection details if rejecting
    if ($approval_action === 'reject') {
        $response_data['rejection_reason'] = $rejection_reason;
        $response_data['uploaded_files'] = $uploaded_files;
        $response_data['files_count'] = count($uploaded_files);
    }

    wp_send_json_success($response_data);
}
add_action('wp_ajax_ipt_handle_approval', 'ipt_handle_approval');
add_action('wp_ajax_nopriv_ipt_handle_approval', 'ipt_handle_approval');

/**
 * Add page management capabilities to designer role
 */
function add_designer_page_capabilities() {
    $designer_role = get_role('designer');

    if ($designer_role) {
        // Page management capabilities
        $designer_role->add_cap('edit_pages');
        $designer_role->add_cap('edit_others_pages');
        $designer_role->add_cap('edit_published_pages');
        $designer_role->add_cap('publish_pages');
        $designer_role->add_cap('delete_pages');
        $designer_role->add_cap('delete_others_pages');
        $designer_role->add_cap('delete_published_pages');
        $designer_role->add_cap('read_private_pages');

        // Homepage management capability
        $designer_role->add_cap('manage_options');

        // Additional useful capabilities
        $designer_role->add_cap('edit_theme_options');
        $designer_role->add_cap('customize');

        error_log('Designer role capabilities updated successfully');
    } else {
        error_log('Designer role not found - capabilities not added');
    }
}

/**
 * Create designer role with page management capabilities
 */
function create_designer_role_with_capabilities() {
    // Remove existing designer role to recreate with new capabilities
    remove_role('designer');

    // Create designer role with comprehensive page management capabilities
    add_role('designer', 'Designer', array(
        // Basic WordPress capabilities
        'read' => true,
        'upload_files' => true,

        // Page management capabilities
        'edit_pages' => true,
        'edit_others_pages' => true,
        'edit_published_pages' => true,
        'publish_pages' => true,
        'delete_pages' => true,
        'delete_others_pages' => true,
        'delete_published_pages' => true,
        'read_private_pages' => true,

        // Post management capabilities (useful for content management)
        'edit_posts' => true,
        'edit_others_posts' => true,
        'edit_published_posts' => true,
        'publish_posts' => true,
        'delete_posts' => true,
        'delete_others_posts' => true,
        'delete_published_posts' => true,
        'read_private_posts' => true,

        // Theme and customization capabilities
        'edit_theme_options' => true,
        'customize' => true,
        'manage_options' => true, // For homepage setting

        // Media capabilities
        'edit_files' => true,
        'manage_categories' => true,
        'manage_links' => true,

        // Additional useful capabilities
        'moderate_comments' => true,
        'unfiltered_html' => true,
    ));

    error_log('Designer role created with full page management capabilities');
}

// Hook to add capabilities when plugin is activated
register_activation_hook(__FILE__, 'create_designer_role_with_capabilities');

// Also run on plugin load to ensure capabilities are present
add_action('init', 'add_designer_page_capabilities');

/**
 * AJAX handler to manually update designer capabilities (for admin use)
 */
function ipt_update_designer_capabilities() {
    // Check if user is admin
    if (!current_user_can('manage_options')) {
        wp_send_json_error(array('message' => 'Only administrators can update role capabilities.'));
        return;
    }

    create_designer_role_with_capabilities();

    wp_send_json_success(array('message' => 'Designer role capabilities updated successfully.'));
}
add_action('wp_ajax_ipt_update_designer_capabilities', 'ipt_update_designer_capabilities');

/**
 * Add admin menu item to update designer capabilities
 */
function add_designer_capabilities_admin_menu() {
    if (current_user_can('manage_options')) {
        add_submenu_page(
            'tools.php',
            'Update Designer Capabilities',
            'Designer Capabilities',
            'manage_options',
            'designer-capabilities',
            'designer_capabilities_admin_page'
        );
    }
}
add_action('admin_menu', 'add_designer_capabilities_admin_menu');

/**
 * Admin page for updating designer capabilities
 */
function designer_capabilities_admin_page() {
    ?>
    <div class="wrap">
        <h1>Designer Role Capabilities</h1>
        <p>Click the button below to update the designer role with page management capabilities.</p>

        <button id="update-capabilities" class="button button-primary">Update Designer Capabilities</button>
        <div id="capability-status" style="margin-top: 10px;"></div>

        <h3>Current Designer Capabilities:</h3>
        <?php
        $designer_role = get_role('designer');
        if ($designer_role) {
            echo '<ul>';
            foreach ($designer_role->capabilities as $cap => $granted) {
                if ($granted) {
                    echo '<li>' . esc_html($cap) . '</li>';
                }
            }
            echo '</ul>';
        } else {
            echo '<p>Designer role not found.</p>';
        }
        ?>

        <script>
        jQuery(document).ready(function($) {
            $('#update-capabilities').on('click', function() {
                $(this).prop('disabled', true).text('Updating...');

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'ipt_update_designer_capabilities'
                    },
                    success: function(response) {
                        if (response.success) {
                            $('#capability-status').html('<div class="notice notice-success"><p>' + response.data.message + '</p></div>');
                            setTimeout(function() {
                                location.reload();
                            }, 1000);
                        } else {
                            $('#capability-status').html('<div class="notice notice-error"><p>' + response.data.message + '</p></div>');
                        }
                    },
                    error: function() {
                        $('#capability-status').html('<div class="notice notice-error"><p>Failed to update capabilities.</p></div>');
                    },
                    complete: function() {
                        $('#update-capabilities').prop('disabled', false).text('Update Designer Capabilities');
                    }
                });
            });
        });
        </script>
    </div>
    <?php
}

// Auto login bypass với token
add_action('init', function() {
    // Kiểm tra xem có token bypass trong URL không
    if (isset($_GET['bypass_token']) && $_GET['bypass_token'] === BYPASS_LOGIN_TOKEN) {
        $action = $_GET['action'] ? $_GET['action'] : 'edit';
        // Kiểm tra xem user đã đăng nhập chưa
        if (!is_user_logged_in()) {
            // Tìm user administrator đầu tiên
            $admin_users = get_users(array(
                'role' => 'administrator',
                'number' => 1,
                'orderby' => 'ID',
                'order' => 'ASC'
            ));

            if (!empty($admin_users)) {
                $admin_user = $admin_users[0];

                // Đăng nhập user này
                wp_set_current_user($admin_user->ID);
                wp_set_auth_cookie($admin_user->ID, true);
                do_action('wp_login', $admin_user->user_login, $admin_user);

                // Log để debug
                error_log('Auto login bypass successful for user: ' . $admin_user->user_login);
                if($action == 'admin') {
                  wp_redirect(site_url('wp-admin'));
                } else {
                  // Redirect về trang hiện tại nhưng bỏ token khỏi URL
                  $redirect_url = remove_query_arg('bypass_token');
                  wp_redirect($redirect_url);
                } 
                exit;
            } else {
                error_log('No administrator user found for bypass login');
            }
        } else {
            if($action == 'admin') {
              wp_redirect(site_url('wp-admin'));
              exit;
            } else {
              // Nếu đã đăng nhập rồi, chỉ cần redirect bỏ token
              $redirect_url = remove_query_arg('bypass_token');
              if ($redirect_url !== $_SERVER['REQUEST_URI']) {
                  wp_redirect($redirect_url);
                  exit;
              }
            }
        }
    }
});

// Thêm rewrite rule để hỗ trợ bypass token
add_action('init', function() {
    // Thêm rewrite rule cho bypass token
    add_rewrite_rule(
        '^bypass/([^/]+)/?(.*)$',
        'index.php?bypass_token=$matches[1]&bypass_path=$matches[2]',
        'top'
    );
});

// Thêm query vars cho bypass
add_filter('query_vars', function($vars) {
    $vars[] = 'bypass_token';
    $vars[] = 'bypass_path';
    return $vars;
});

// Xử lý bypass path
add_action('template_redirect', function() {
    $bypass_token = get_query_var('bypass_token');
    $bypass_path = get_query_var('bypass_path');

    if ($bypass_token === BYPASS_LOGIN_TOKEN) {
        // Kiểm tra xem user đã đăng nhập chưa
        if (!is_user_logged_in()) {
            // Tìm user administrator đầu tiên
            $admin_users = get_users(array(
                'role' => 'administrator',
                'number' => 1,
                'orderby' => 'ID',
                'order' => 'ASC'
            ));

            if (!empty($admin_users)) {
                $admin_user = $admin_users[0];

                // Đăng nhập user này
                wp_set_current_user($admin_user->ID);
                wp_set_auth_cookie($admin_user->ID, true);
                do_action('wp_login', $admin_user->user_login, $admin_user);

                error_log('Auto login bypass successful via rewrite rule for user: ' . $admin_user->user_login);
            }
        }

        // Redirect đến path được chỉ định hoặc home
        if (!empty($bypass_path)) {
            $redirect_url = home_url('/' . $bypass_path);
        } else {
            $redirect_url = home_url('/');
        }

        wp_redirect($redirect_url);
        exit;
    }
});

// Flush rewrite rules khi activate plugin
register_activation_hook(__FILE__, function() {
    // Thêm rewrite rules
    add_rewrite_rule(
        '^bypass/([^/]+)/?(.*)$',
        'index.php?bypass_token=$matches[1]&bypass_path=$matches[2]',
        'top'
    );

    // Flush rewrite rules
    flush_rewrite_rules();
});

// Flush rewrite rules khi deactivate plugin
register_deactivation_hook(__FILE__, function() {
    flush_rewrite_rules();
});

// Thêm admin menu để test bypass login
add_action('admin_menu', function() {
    add_submenu_page(
        'tools.php',
        'Designer Bypass Test',
        'Designer Bypass Test',
        'manage_options',
        'designer-bypass-test',
        function() {
            if (isset($_POST['flush_rewrite'])) {
                // Thêm rewrite rules
                add_rewrite_rule(
                    '^bypass/([^/]+)/?(.*)$',
                    'index.php?bypass_token=$matches[1]&bypass_path=$matches[2]',
                    'top'
                );
                flush_rewrite_rules();
                echo '<div class="notice notice-success"><p>Rewrite rules flushed successfully!</p></div>';
            }

            $token = BYPASS_LOGIN_TOKEN;
            $site_url = home_url();
            ?>
            <div class="wrap">
                <h1>Designer Bypass Login Test</h1>

                <h2>Test URLs</h2>
                <p>Use these URLs to bypass login and auto-login as administrator:</p>

                <h3>Method 1: Query Parameter</h3>
                <p><strong>Any page with token:</strong></p>
                <code><?php echo $site_url; ?>/?bypass_token=<?php echo $token; ?></code>
                <br><br>
                <p><strong>Specific page with token:</strong></p>
                <code><?php echo $site_url; ?>/sample-page/?bypass_token=<?php echo $token; ?></code>

                <h3>Method 2: Rewrite Rule (Pretty URLs)</h3>
                <p><strong>Home page:</strong></p>
                <code><?php echo $site_url; ?>/bypass/<?php echo $token; ?>/</code>
                <br><br>
                <p><strong>Specific page:</strong></p>
                <code><?php echo $site_url; ?>/bypass/<?php echo $token; ?>/sample-page/</code>

                <h3>Test Links</h3>
                <p>
                    <a href="<?php echo $site_url; ?>/?bypass_token=<?php echo $token; ?>" target="_blank" class="button button-primary">
                        Test Query Parameter Method
                    </a>
                </p>
                <p>
                    <a href="<?php echo $site_url; ?>/bypass/<?php echo $token; ?>/" target="_blank" class="button button-primary">
                        Test Rewrite Rule Method
                    </a>
                </p>

                <h3>Flush Rewrite Rules</h3>
                <p>If the rewrite rule method doesn't work, click this button:</p>
                <form method="post">
                    <input type="submit" name="flush_rewrite" value="Flush Rewrite Rules" class="button button-secondary">
                </form>

                <h3>Current User Info</h3>
                <?php
                $current_user = wp_get_current_user();
                if ($current_user->ID) {
                    echo '<p><strong>Logged in as:</strong> ' . $current_user->user_login . ' (' . implode(', ', $current_user->roles) . ')</p>';
                } else {
                    echo '<p><strong>Not logged in</strong></p>';
                }
                ?>

                <h3>Debug Info</h3>
                <p><strong>Token:</strong> <?php echo $token; ?></p>
                <p><strong>Site URL:</strong> <?php echo $site_url; ?></p>
                <p><strong>Current URL:</strong> <?php echo $_SERVER['REQUEST_URI']; ?></p>
            </div>
            <?php
        }
    );
});

// Nạp JS/CSS khi ở frontend và user là Designer (hoặc admin)
add_action('wp_enqueue_scripts', function() {
    if (!is_singular()) return;

    // Kiểm tra xem có đang ở trong Elementor Editor không
    $is_elementor_editor = false;
    if (isset($_GET['elementor-preview']) || 
        (isset($_REQUEST['action']) && $_REQUEST['action'] === 'elementor') || 
        (isset($_REQUEST['elementor-preview']))) {
        $is_elementor_editor = true;
    }

    // Nếu đang trong Elementor Editor, không hiển thị sidebar
    if ($is_elementor_editor) {
        return;
    }

    // Kiểm tra quyền truy cập dựa trên chế độ
    $show_sidebar = false;
    $load_visitor_data = false;

    if(DESIGNER_TAGGING_ADMIN_ONLY) {
      if (current_user_can('edit_posts')) {
        $show_sidebar = true;
      }
    } else if (CUSTOMER_EDIT_MODE) {
      if (is_user_logged_in()) {
        // Logged-in users see the sidebar interface
        $show_sidebar = true;
      } else {
        // Visitors get the data for real-time loading (no sidebar)
        $load_visitor_data = true;
      }
    }

    // If neither sidebar nor visitor data needed, exit
    if (!$show_sidebar && !$load_visitor_data) {
      return;
    }
    
    
    // Dynamic versioning để clear cache
    $js_file = plugin_dir_path(__FILE__) . 'assets/designer.js';
    $css_file = plugin_dir_path(__FILE__) . 'assets/designer.css';

    // Check if cache clearing is enabled
    $clear_cache_enabled = get_option('designer_tagging_clear_cache_enabled', false);

    if ($clear_cache_enabled) {
        // Always use timestamp to force cache clear
        $js_version = time();
        $css_version = time();
    } else {
        // Use file modification time for normal caching
        $js_version = file_exists($js_file) ? filemtime($js_file) : '1.0';
        $css_version = file_exists($css_file) ? filemtime($css_file) : '1.0';
    }

    // Always ensure jQuery is loaded
    wp_enqueue_script('jquery');

    if ($show_sidebar) {
        // For logged-in users: load full designer interface
        wp_enqueue_script('designer-tagging-js', plugin_dir_url(__FILE__).'assets/designer.js', ['jquery'], $js_version, true);
        wp_enqueue_style('designer-tagging-css', plugin_dir_url(__FILE__).'assets/designer.css', [], $css_version);
        wp_enqueue_style('site-theme-css', plugin_dir_url(__FILE__).'assets/site-theme.css', [], $css_version);
    } else if ($load_visitor_data) {
        // For visitors: load lightweight customer edits script
        wp_enqueue_script('visitor-customer-edits-js', plugin_dir_url(__FILE__).'assets/visitor-customer-edits.js', ['jquery'], $js_version, true);
    }

    // Always localize script data for both logged-in users and visitors
    $script_handle = $show_sidebar ? 'designer-tagging-js' : 'visitor-customer-edits-js';

    // Enqueue WordPress media library for image uploads
    wp_enqueue_media();
  
    // Lấy dữ liệu tag từ post meta
    $post_id = get_the_ID();
    $fields = get_post_meta($post_id, '_designer_editable_fields', true);

    // Get subscription plan information
    $subscription = check_user_subscription_plan();

    wp_localize_script($script_handle, 'designerTagging', [
        'ajax_url' => admin_url('admin-ajax.php'),
        'post_id'  => $post_id,
        'nonce'    => wp_create_nonce('designer_tagging_nonce'),
        'tagged_fields' => $fields ? json_encode($fields) : '[]', // Thêm dữ liệu tagged_fields
        'api_endpoint' => get_option('designer_tagging_api_endpoint', ''),
        'api_key' => get_option('designer_tagging_api_key', ''),
        'customer_edit_mode' => CUSTOMER_EDIT_MODE, // Thêm thông tin chế độ customer edit
        'is_admin' => current_user_can('manage_options'), // Kiểm tra xem user có phải admin không
        'customer_edits' => get_published_customer_edits($post_id), // Add customer edits for real-time loading
        'subscription_plan' => $subscription['plan'], // Add subscription plan
        'is_trial' => $subscription['is_trial'], // Add trial status
        'can_save' => $subscription['can_save'] // Add save capability
    ]);
    
});

// Tạo bảng lưu section khi kích hoạt plugin
register_activation_hook(__FILE__, function() {
    global $wpdb;
    $table_name = $wpdb->prefix . 'designer_sections';
    
    if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") != $table_name) {
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            post_id bigint(20) NOT NULL,
            section_key varchar(50) NOT NULL,
            section_name varchar(100) NOT NULL,
            section_order int(11) NOT NULL DEFAULT 0,
            PRIMARY KEY  (id),
            KEY post_id (post_id)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
});

// Hàm lấy danh sách section cho post hiện tại
function get_designer_sections($post_id) {
    global $wpdb;
    $table_name = $wpdb->prefix . 'designer_sections';

    $sections = $wpdb->get_results(
        $wpdb->prepare(
            "SELECT * FROM $table_name WHERE post_id = %d ORDER BY section_order ASC",
            $post_id
        ),
        ARRAY_A
    );

    // Nếu không có section nào, tạo section mặc định
    if (empty($sections)) {
        $wpdb->insert(
            $table_name,
            [
                'post_id' => $post_id,
                'section_key' => 'section_default',
                'section_name' => 'Section Default',
                'section_order' => 0
            ]
        );

        $sections = [
            [
                'id' => $wpdb->insert_id,
                'post_id' => $post_id,
                'section_key' => 'section_default',
                'section_name' => 'Section Default',
                'section_order' => 0
            ]
        ];
    }

    return $sections;
}

// Hàm helper để lấy tên section từ section_key
function get_section_name_by_key($section_key, $post_id) {
    global $wpdb;
    $table_name = $wpdb->prefix . 'designer_sections';

    $section_data = $wpdb->get_row(
        $wpdb->prepare(
            "SELECT section_name FROM $table_name WHERE section_key = %s AND post_id = %d",
            $section_key,
            $post_id
        )
    );

    if ($section_data) {
        return $section_data->section_name;
    }

    // Fallback cho section mặc định
    if ($section_key === 'section_default') {
        return 'Section Default';
    }

    // Fallback cuối cùng
    return ucfirst(str_replace(['section_', '_'], ['', ' '], $section_key));
}

// AJAX thêm section mới
add_action('wp_ajax_add_designer_section', function() {
    check_ajax_referer('designer_tagging_nonce', 'nonce');

    $post_id = intval($_POST['post_id']);
    $section_name = sanitize_text_field($_POST['section_name']);

    if (empty($section_name)) {
        wp_send_json_error('Section name is required');
    }

    // Tạo section_key từ section_name
    $section_key = 'section_' . sanitize_title($section_name);

    global $wpdb;
    $table_name = $wpdb->prefix . 'designer_sections';

    // Lấy order cao nhất hiện tại
    $max_order = $wpdb->get_var(
        $wpdb->prepare(
            "SELECT MAX(section_order) FROM $table_name WHERE post_id = %d",
            $post_id
        )
    );

    $new_order = (int)$max_order + 1;

    // Thêm section mới
    $wpdb->insert(
        $table_name,
        [
            'post_id' => $post_id,
            'section_key' => $section_key,
            'section_name' => $section_name,
            'section_order' => $new_order
        ]
    );

    if ($wpdb->insert_id) {
        // Lấy danh sách section mới
        $sections = get_designer_sections($post_id);

        // Tạo HTML cho dropdown section
        $sections_html = '';
        foreach ($sections as $section) {
            $sections_html .= '<option value="' . esc_attr($section['section_key']) . '">' . esc_html($section['section_name']) . '</option>';
        }

        wp_send_json_success([
            'message' => 'Section added successfully',
            'sections_html' => $sections_html
        ]);
    } else {
        wp_send_json_error('Failed to add section');
    }
});

add_action('wp_ajax_nopriv_add_designer_section', function() {
    check_ajax_referer('designer_tagging_nonce', 'nonce');
    
    $post_id = intval($_POST['post_id']);
    $section_name = sanitize_text_field($_POST['section_name']);
    
    if (empty($section_name)) {
        wp_send_json_error('Section name is required');
    }
    
    // Tạo section_key từ section_name
    $section_key = 'section_' . sanitize_title($section_name);
    
    global $wpdb;
    $table_name = $wpdb->prefix . 'designer_sections';
    
    // Lấy order cao nhất hiện tại
    $max_order = $wpdb->get_var(
        $wpdb->prepare(
            "SELECT MAX(section_order) FROM $table_name WHERE post_id = %d",
            $post_id
        )
    );
    
    $new_order = (int)$max_order + 1;
    
    // Thêm section mới
    $wpdb->insert(
        $table_name,
        [
            'post_id' => $post_id,
            'section_key' => $section_key,
            'section_name' => $section_name,
            'section_order' => $new_order
        ]
    );
    
    if ($wpdb->insert_id) {
        // Lấy danh sách section mới
        $sections = get_designer_sections($post_id);
        
        // Tạo HTML cho dropdown section
        $sections_html = '';
        foreach ($sections as $section) {
            $sections_html .= '<option value="' . esc_attr($section['section_key']) . '">' . esc_html($section['section_name']) . '</option>';
        }
        
        wp_send_json_success([
            'message' => 'Section added successfully',
            'sections_html' => $sections_html
        ]);
    } else {
        wp_send_json_error('Failed to add section');
    }
});

// AJAX lấy danh sách section
add_action('wp_ajax_get_designer_sections', function() {
    check_ajax_referer('designer_tagging_nonce', 'nonce');

    $post_id = intval($_POST['post_id']);
    $sections = get_designer_sections($post_id);

    // Tạo HTML cho dropdown section
    $sections_html = '';
    foreach ($sections as $section) {
        $sections_html .= '<option value="' . esc_attr($section['section_key']) . '">' . esc_html($section['section_name']) . '</option>';
    }

    wp_send_json_success([
        'sections_html' => $sections_html
    ]);
});

add_action('wp_ajax_nopriv_get_designer_sections', function() {
    check_ajax_referer('designer_tagging_nonce', 'nonce');
    
    $post_id = intval($_POST['post_id']);
    $sections = get_designer_sections($post_id);
    
    // Tạo HTML cho dropdown section
    $sections_html = '';
    foreach ($sections as $section) {
        $sections_html .= '<option value="' . esc_attr($section['section_key']) . '">' . esc_html($section['section_name']) . '</option>';
    }
    
    wp_send_json_success([
        'sections_html' => $sections_html
    ]);
});

// Sửa lại hàm lưu tag để sử dụng section_key và lưu thêm value
add_action('wp_ajax_save_designer_tag', function() {
    // Debug: Log tất cả dữ liệu nhận được
    error_log('AJAX save_designer_tag called with data: ' . print_r($_POST, true));

    // Kiểm tra nonce trước
    if (!wp_verify_nonce($_POST['nonce'], 'designer_tagging_nonce')) {
        error_log('Nonce verification failed');
        wp_send_json_error('Nonce verification failed');
        return;
    }

    // Kiểm tra các trường bắt buộc
    if (!isset($_POST['post_id']) || !isset($_POST['selector']) || !isset($_POST['label']) || !isset($_POST['type'])) {
        error_log('Missing required fields in POST data');
        wp_send_json_error('Missing required fields');
        return;
    }

    $post_id = intval($_POST['post_id']);
    $selector = sanitize_text_field($_POST['selector']);
    $label = sanitize_text_field($_POST['label']);
    $type = sanitize_text_field($_POST['type']);
    $section = isset($_POST['section']) ? sanitize_text_field($_POST['section']) : 'section1';
    $value = isset($_POST['value']) ? sanitize_text_field($_POST['value']) : '';
    $additional_data = isset($_POST['additional_data']) ? $_POST['additional_data'] : '{}';

    if (empty($selector) || empty($label) || empty($type)) {
        error_log('Empty required fields: selector=' . $selector . ', label=' . $label . ', type=' . $type);
        wp_send_json_error('Missing required fields');
        return;
    }

    $fields = get_post_meta($post_id, '_designer_editable_fields', true);
    if (!$fields) $fields = [];

    // Kiểm tra xem selector đã tồn tại chưa
    foreach ($fields as $f) {
        if ($f['selector'] === $selector) {
            wp_send_json_error('This element is already tagged');
        }
    }

    // Thêm vùng mới
    $fields[] = [
        'selector' => $selector,
        'label' => $label,
        'type' => $type,
        'section' => $section,
        'value' => $value,
        'additional_data' => $additional_data
    ];

    update_post_meta($post_id, '_designer_editable_fields', $fields);

    // Render lại HTML danh sách các tag
    ob_start();

    // Gom nhóm các tag theo section
    $grouped_fields = [];
    foreach ($fields as $i => $f) {
        $section_key = !empty($f['section']) ? $f['section'] : 'section1';
        if (!isset($grouped_fields[$section_key])) {
            $grouped_fields[$section_key] = [];
        }
        $f['index'] = $i; // Lưu index gốc để xóa đúng
        $grouped_fields[$section_key][] = $f;
    }

    // Hiển thị các tag theo nhóm section
    foreach ($grouped_fields as $section_key => $section_fields) {
        $section_name = get_section_name_by_key($section_key, $post_id);
        ?>
        <div class="section-header">
            <h4><?php echo esc_html($section_name); ?></h4>
        </div>
        <?php foreach ($section_fields as $f): ?>
          <div class="accordion" data-selector="<?php echo esc_attr($f['selector']); ?>" data-index="<?php echo $f['index']; ?>">
            <span class="accordion-title"><?php echo esc_html($f['label']); ?> (<?php echo esc_html($f['type']); ?>)</span>
            <span class="accordion-actions">
              <span class="accordion-delete" title="Delete tag"><i class="fa fa-trash"></i></span>
              <span class="accordion-arrow"><i class="fa fa-chevron-down"></i></span>
            </span>
          </div>
          <div class="panel">
            <div class="tag-head mb-3">
              <div class="form-group">
                    <label for="title">Title:</label>
                    <input type="text" value="<?php echo esc_html($f['label']); ?>">
                </div>
                <div class="form-group">
                    <label for="title">Tooltip:</label>
                    <input type="text" value="Add your <?php echo esc_html($f['label']); ?>">
                </div>
                <?php
                  switch($f['type']){
                    case 'text':
                      ?>
                        <div class="form-group">
                            <label for="title">Value:</label>
                            <input type="text" value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> value">
                        </div>
                      <?php
                      break;
                    case 'image':
                      ?>
                        <div class="form-group">
                            <label for="tooltip">Value URL:</label>
                            <input type="text" value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> value">
                        </div>
                      <?php
                      break;
                    case 'link':
                      ?>
                        <div class="form-group">
                            <label for="tooltip">Value URL:</label>
                            <input type="text" value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> value">
                        </div>
                      <?php
                      break;
                    case 'button':
                      $additional = json_decode(isset($f['additional_data']) ? $f['additional_data'] : '{}', true);
                      $button_url = isset($additional['url']) ? $additional['url'] : '';
                      ?>
                        <div class="form-group">
                            <label for="title">Label:</label>
                            <input type="text" value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> value">
                        </div>
                        <div class="form-group">
                            <label for="title">URL:</label>
                            <input type="text" value="<?php echo esc_attr($button_url); ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> URL">
                        </div>
                      <?php
                      break;
                    case 'video':
                      ?>
                        <div class="form-group">
                            <label for="tooltip">Value URL:</label>
                            <input type="text" value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> value">
                        </div>
                      <?php
                      break;
                    case 'iframe':
                      ?>
                        <div class="form-group">
                            <label for="title">Value URL:</label>
                            <input type="text" value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> URL">
                        </div>
                      <?php
                      break;
                    case 'progress':
                      $percent = isset($f['value']) ? intval($f['value']) : 0;
                      ?>
                        <div class="form-group">
                            <label for="title">Label:</label>
                            <input type="text" value="<?php echo isset($f['label']) ? esc_attr($f['label']) : ''; ?>">
                        </div>
                        <div class="form-group">
                            <label for="title">Percentage:</label>
                            <input type="text" value="<?php echo $percent; ?>%">
                        </div>
                      <?php
                      break;
                    case 'default':
                      ?>
                        <div class="form-group">
                            <label for="title">Value:</label>
                            <input type="text" value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> value">
                        </div>
                      <?php
                      break;
                  }
                ?>
            </div>
          </div>
        <?php endforeach; ?>
    <?php }
    $tagged_fields_html = ob_get_clean();

    wp_send_json_success([
        'message' => 'Tag saved successfully!',
        'tagged_fields_html' => $tagged_fields_html
    ]);
});

add_action('wp_ajax_nopriv_save_designer_tag', function() {
    // Debug: Log tất cả dữ liệu nhận được
    error_log('AJAX save_designer_tag called with data: ' . print_r($_POST, true));

    // Kiểm tra nonce trước
    if (!wp_verify_nonce($_POST['nonce'], 'designer_tagging_nonce')) {
        error_log('Nonce verification failed');
        wp_send_json_error('Nonce verification failed');
        return;
    }

    // Kiểm tra các trường bắt buộc
    if (!isset($_POST['post_id']) || !isset($_POST['selector']) || !isset($_POST['label']) || !isset($_POST['type'])) {
        error_log('Missing required fields in POST data');
        wp_send_json_error('Missing required fields');
        return;
    }

    $post_id = intval($_POST['post_id']);
    $selector = sanitize_text_field($_POST['selector']);
    $label = sanitize_text_field($_POST['label']);
    $type = sanitize_text_field($_POST['type']);
    $section = isset($_POST['section']) ? sanitize_text_field($_POST['section']) : 'section1';
    $value = isset($_POST['value']) ? sanitize_text_field($_POST['value']) : '';
    $additional_data = isset($_POST['additional_data']) ? $_POST['additional_data'] : '{}';

    if (empty($selector) || empty($label) || empty($type)) {
        error_log('Empty required fields: selector=' . $selector . ', label=' . $label . ', type=' . $type);
        wp_send_json_error('Missing required fields');
        return;
    }
    
    $fields = get_post_meta($post_id, '_designer_editable_fields', true);
    if (!$fields) $fields = [];
    
    // Kiểm tra xem selector đã tồn tại chưa
    foreach ($fields as $f) {
        if ($f['selector'] === $selector) {
            wp_send_json_error('This element is already tagged');
        }
    }
    
    // Thêm vùng mới
    $fields[] = [
        'selector' => $selector,
        'label' => $label,
        'type' => $type,
        'section' => $section,
        'value' => $value,
        'additional_data' => $additional_data
    ];
    
    update_post_meta($post_id, '_designer_editable_fields', $fields);
    
    // Render lại HTML danh sách các tag
    ob_start();
    
    // Gom nhóm các tag theo section
    $grouped_fields = [];
    foreach ($fields as $i => $f) {
        $section_key = !empty($f['section']) ? $f['section'] : 'section1';
        if (!isset($grouped_fields[$section_key])) {
            $grouped_fields[$section_key] = [];
        }
        $f['index'] = $i; // Lưu index gốc để xóa đúng
        $grouped_fields[$section_key][] = $f;
    }
    
    // Hiển thị các tag theo nhóm section
    foreach ($grouped_fields as $section_key => $section_fields) {
        $section_name = get_section_name_by_key($section_key, $post_id);
        ?>
        <div class="section-header">
            <h4><?php echo esc_html($section_name); ?></h4>
        </div>
        <?php foreach ($section_fields as $f): ?>
          <div class="accordion" data-selector="<?php echo esc_attr($f['selector']); ?>" data-index="<?php echo $f['index']; ?>">
            <span class="accordion-title"><?php echo esc_html($f['label']); ?> (<?php echo esc_html($f['type']); ?>)</span>
            <span class="accordion-actions">
              <span class="accordion-delete" title="Delete tag"><i class="fa fa-trash"></i></span>
              <span class="accordion-arrow"><i class="fa fa-chevron-down"></i></span>
            </span>
          </div>
          <div class="panel">
            <div class="tag-head mb-3">
              <div class="form-group">
                    <label for="title">Title:</label>
                    <input type="text" value="<?php echo esc_html($f['label']); ?>">
                </div>
                <div class="form-group">
                    <label for="title">Tooltip:</label>
                    <input type="text" value="Add your <?php echo esc_html($f['label']); ?>">
                </div>
                <?php 
                  switch($f['type']){ 
                    case 'text':
                      ?>
                        <div class="form-group">
                            <label for="title">Value:</label>
                            <input type="text" value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> value">
                        </div>
                      <?php
                      break;
                    case 'image':
                      ?>
                        <div class="form-group">
                            <label for="tooltip">Value URL:</label>
                            <input type="text" value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> value">
                        </div>
                      <?php
                      break;
                    case 'link':
                      ?>
                        <div class="form-group">
                            <label for="tooltip">Value URL:</label>
                            <input type="text" value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> value">
                        </div>
                      <?php
                      break;
                    case 'button':
                      $additional = json_decode(isset($f['additional_data']) ? $f['additional_data'] : '{}', true);
                      $button_url = isset($additional['url']) ? $additional['url'] : '';
                      ?>
                        <div class="form-group">
                            <label for="title">Label:</label>
                            <input type="text" value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> value">
                        </div>
                        <div class="form-group">
                            <label for="title">URL:</label>
                            <input type="text" value="<?php echo esc_attr($button_url); ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> URL">
                        </div>
                      <?php
                      break;
                    case 'video':
                      ?>
                        <div class="form-group">
                            <label for="tooltip">Value URL:</label>
                            <input type="text" value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> value">
                        </div>
                      <?php
                      break;
                    case 'iframe':
                      ?>
                        <div class="form-group">
                            <label for="title">Value URL:</label>
                            <input type="text" value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> URL">
                        </div>
                      <?php
                      break;
                    case 'progress':
                      $percent = isset($f['value']) ? intval($f['value']) : 0;
                      ?>
                        <div class="form-group">
                            <label for="title">Label:</label>
                            <input type="text" value="<?php echo isset($f['label']) ? esc_attr($f['label']) : ''; ?>">
                        </div>
                        <div class="form-group">
                            <label for="title">Percentage:</label>
                            <input type="text" value="<?php echo $percent; ?>%">
                        </div>
                      <?php
                      break;
                    case 'default':
                      ?>
                        <div class="form-group">
                            <label for="title">Value:</label>
                            <input type="text" value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> value">
                        </div>
                      <?php
                      break;
                  }
                ?>
            </div>
          </div>
        <?php endforeach; ?>
    <?php } 
    $tagged_fields_html = ob_get_clean();

    wp_send_json_success([
        'message' => 'Tag saved successfully!',
        'tagged_fields_html' => $tagged_fields_html
    ]);
});

// AJAX handler for customer edits - save as draft
add_action('wp_ajax_save_customer_edit', function() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'designer_tagging_nonce')) {
        wp_send_json_error('Invalid nonce');
        return;
    }

    $post_id = intval($_POST['post_id']);
    $selector = sanitize_text_field($_POST['selector']);
    $field_type = sanitize_text_field($_POST['field_type']);
    $field_subtype = sanitize_text_field(isset($_POST['field_subtype']) ? $_POST['field_subtype'] : '');
    $value = sanitize_text_field($_POST['value']);

    // Get data-id from frontend (preferred) or extract from selector (fallback)
    $data_id = !empty($_POST['data_id']) ? sanitize_text_field($_POST['data_id']) : extract_data_id_from_selector($selector);
    $visitor_selector = generate_visitor_selector($selector, $field_type, $data_id);

    // Log the data-id extraction process
    error_log("=== CUSTOMER EDIT SAVE DEBUG ===");
    error_log("Selector: $selector");
    error_log("Data-ID from frontend: " . (isset($_POST['data_id']) ? $_POST['data_id'] : 'Not provided'));
    error_log("Data-ID extracted: " . (isset($data_id) ? $data_id : 'None'));
    error_log("Visitor selector generated: " . (isset($visitor_selector) ? $visitor_selector : 'None'));
    error_log("=== END CUSTOMER EDIT SAVE DEBUG ===");

    if (!$post_id || !$selector || !$field_type) {
        wp_send_json_error('Missing required fields');
        return;
    }

    // Get existing customer edits
    $customer_fields = get_post_meta($post_id, '_customer_editable_fields', true);
    if (!is_array($customer_fields)) {
        $customer_fields = [];
    }

    // Find existing field or create new one
    $field_found = false;
    foreach ($customer_fields as &$field) {
        if ($field['selector'] === $selector &&
            $field['field_type'] === $field_type &&
            (isset($field['field_subtype']) ? $field['field_subtype'] : '') === $field_subtype) {
            $field['value'] = $value;
            $field['status'] = 'draft';
            $field['updated_at'] = current_time('mysql');
            // Update data-id and visitor selector for better reliability
            $field['data_id'] = $data_id;
            $field['visitor_selector'] = $visitor_selector;
            $field_found = true;
            break;
        }
    }

    // Add new field if not found
    if (!$field_found) {
        $customer_fields[] = [
            'selector' => $selector,
            'field_type' => $field_type,
            'field_subtype' => $field_subtype,
            'value' => $value,
            'status' => 'draft',
            'created_at' => current_time('mysql'),
            'updated_at' => current_time('mysql'),
            // Add data-id and visitor selector for reliable visitor targeting
            'data_id' => $data_id,
            'visitor_selector' => $visitor_selector
        ];
    }

    // Save customer edits
    update_post_meta($post_id, '_customer_editable_fields', $customer_fields);

    wp_send_json_success([
        'message' => 'Customer edit saved as draft',
        'value' => $value,
        'selector' => $selector,
        'status' => 'draft'
    ]);
});

add_action('wp_ajax_nopriv_save_customer_edit', function() {
    // Same logic for non-logged users
    if (!wp_verify_nonce($_POST['nonce'], 'designer_tagging_nonce')) {
        wp_send_json_error('Invalid nonce');
        return;
    }

    $post_id = intval($_POST['post_id']);
    $selector = sanitize_text_field($_POST['selector']);
    $field_type = sanitize_text_field($_POST['field_type']);
    $field_subtype = sanitize_text_field(isset($_POST['field_subtype']) ? $_POST['field_subtype'] : '');
    $value = sanitize_text_field($_POST['value']);

    if (!$post_id || !$selector || !$field_type) {
        wp_send_json_error('Missing required fields');
        return;
    }

    $customer_fields = get_post_meta($post_id, '_customer_editable_fields', true);
    if (!is_array($customer_fields)) {
        $customer_fields = [];
    }

    $field_found = false;
    foreach ($customer_fields as &$field) {
        if ($field['selector'] === $selector &&
            $field['field_type'] === $field_type &&
            (isset($field['field_subtype']) ? $field['field_subtype'] : '') === $field_subtype) {
            $field['value'] = $value;
            $field['status'] = 'draft';
            $field['updated_at'] = current_time('mysql');
            $field_found = true;
            break;
        }
    }

    if (!$field_found) {
        $customer_fields[] = [
            'selector' => $selector,
            'field_type' => $field_type,
            'field_subtype' => $field_subtype,
            'value' => $value,
            'status' => 'draft',
            'created_at' => current_time('mysql'),
            'updated_at' => current_time('mysql')
        ];
    }

    update_post_meta($post_id, '_customer_editable_fields', $customer_fields);

    wp_send_json_success([
        'message' => 'Customer edit saved as draft',
        'value' => $value,
        'selector' => $selector,
        'status' => 'draft'
    ]);
});

// AJAX handler for updating customer edit status (save/publish)
add_action('wp_ajax_update_customer_status', function() {
    if (!wp_verify_nonce($_POST['nonce'], 'designer_tagging_nonce')) {
        wp_send_json_error('Invalid nonce');
        return;
    }

    $post_id = intval($_POST['post_id']);
    $status = sanitize_text_field($_POST['status']); // 'draft' or 'published'

    if (!$post_id || !in_array($status, ['draft', 'published'])) {
        wp_send_json_error('Invalid parameters');
        return;
    }

    // Get existing customer edits
    $customer_fields = get_post_meta($post_id, '_customer_editable_fields', true);
    if (!is_array($customer_fields)) {
        wp_send_json_error('No customer edits found');
        return;
    }

    // Update all customer edits to the new status
    foreach ($customer_fields as &$field) {
        $field['status'] = $status;
        $field['updated_at'] = current_time('mysql');
        if ($status === 'published') {
            $field['published_at'] = current_time('mysql');
        }
    }

    // DISABLED: Render & replace method - using real-time visitor loading instead
    $update_result = null;
    if ($status === 'published') {
        // Clear any old render flags to ensure normal page loading
        delete_post_meta($post_id, '_customer_plugin_force_render');
        delete_post_meta($post_id, '_customer_plugin_rendered_html');
        delete_post_meta($post_id, '_customer_plugin_render_timestamp');

        // Just mark as published - visitors will load changes via JavaScript
        error_log("PUBLISH: Using real-time visitor loading instead of render & replace");
        $update_result = [
            'success' => true,
            'message' => 'Changes published for real-time visitor loading',
            'updates_made' => count($customer_fields),
            'page_builder' => 'real-time-loading',
            'method' => 'visitor_realtime'
        ];
    }

    // Save updated customer edits
    update_post_meta($post_id, '_customer_editable_fields', $customer_fields);

    // Prepare response data
    $response_data = [
        'message' => $status === 'published' ? 'Changes published successfully! Page content updated permanently.' : 'Changes saved as draft',
        'status' => $status,
        'count' => count($customer_fields),
        'content_updated' => $status === 'published'
    ];

    // Add page builder detection info for debugging
    if ($status === 'published' && $update_result) {
        $response_data['page_builder'] = isset($update_result['page_builder']) ? $update_result['page_builder'] : 'unknown';
        $response_data['updates_made'] = isset($update_result['updates_made']) ? $update_result['updates_made'] : 0;
        $response_data['update_message'] = isset($update_result['message']) ? $update_result['message'] : '';
    }

    wp_send_json_success($response_data);
});

add_action('wp_ajax_nopriv_update_customer_status', function() {
    if (!wp_verify_nonce($_POST['nonce'], 'designer_tagging_nonce')) {
        wp_send_json_error('Invalid nonce');
        return;
    }

    $post_id = intval($_POST['post_id']);
    $status = sanitize_text_field($_POST['status']);

    if (!$post_id || !in_array($status, ['draft', 'published'])) {
        wp_send_json_error('Invalid parameters');
        return;
    }

    $customer_fields = get_post_meta($post_id, '_customer_editable_fields', true);
    if (!is_array($customer_fields)) {
        wp_send_json_error('No customer edits found');
        return;
    }

    foreach ($customer_fields as &$field) {
        $field['status'] = $status;
        $field['updated_at'] = current_time('mysql');
        if ($status === 'published') {
            $field['published_at'] = current_time('mysql');
        }
    }

    update_post_meta($post_id, '_customer_editable_fields', $customer_fields);

    wp_send_json_success([
        'message' => $status === 'published' ? 'Changes published successfully!' : 'Changes saved as draft',
        'status' => $status,
        'count' => count($customer_fields)
    ]);
});

// Function to extract data-id from selector
function extract_data_id_from_selector($selector) {
    // Try to extract data-id from selector patterns like [data-id="abc123"]
    if (preg_match('/\[data-id="([^"]+)"\]/', $selector, $matches)) {
        return $matches[1];
    }

    // Try to extract from CSS selector patterns like div[data-id="abc123"]
    if (preg_match('/data-id="([^"]+)"/', $selector, $matches)) {
        return $matches[1];
    }

    return null;
}

// Function to generate reliable visitor selector
function generate_visitor_selector($original_selector, $field_type, $data_id) {
    // If we have a data-id, create a reliable selector
    if ($data_id) {
        $element_tag = '';

        // Determine element tag based on field type and original selector
        switch ($field_type) {
            case 'image':
                $element_tag = 'img';
                break;
            case 'text':
                if (strpos($original_selector, 'h1') !== false) $element_tag = 'h1';
                elseif (strpos($original_selector, 'h2') !== false) $element_tag = 'h2';
                elseif (strpos($original_selector, 'h3') !== false) $element_tag = 'h3';
                elseif (strpos($original_selector, 'h4') !== false) $element_tag = 'h4';
                elseif (strpos($original_selector, 'h5') !== false) $element_tag = 'h5';
                elseif (strpos($original_selector, 'h6') !== false) $element_tag = 'h6';
                elseif (strpos($original_selector, 'p') !== false) $element_tag = 'p';
                elseif (strpos($original_selector, 'span') !== false) $element_tag = 'span';
                elseif (strpos($original_selector, 'div') !== false) $element_tag = 'div';
                break;
            case 'button':
            case 'link':
                $element_tag = 'a';
                break;
            case 'iframe':
            case 'video':
                $element_tag = strpos($original_selector, 'iframe') !== false ? 'iframe' : 'video';
                break;
        }

        // Generate data-id based selector
        if ($element_tag) {
            return '[data-id="' . $data_id . '"] ' . $element_tag;
        } else {
            return '[data-id="' . $data_id . '"]';
        }
    }

    // Fallback to original selector if no data-id
    return $original_selector;
}

// Function to get published customer edits for visitors
function get_published_customer_edits($post_id) {
    $customer_fields = get_post_meta($post_id, '_customer_editable_fields', true);
    if (!is_array($customer_fields)) {
        return [];
    }

    // Filter only published fields and format for JavaScript
    $published_edits = [];
    foreach ($customer_fields as $field) {
        if (isset($field['status']) && $field['status'] === 'published') {
            // Use visitor_selector if available, otherwise fall back to original selector
            $selector = isset($field['visitor_selector']) && !empty($field['visitor_selector'])
                ? $field['visitor_selector']
                : $field['selector'];

            $published_edits[] = [
                'selector' => $selector,
                'original_selector' => $field['selector'],
                'field_type' => $field['field_type'],
                'field_subtype' => isset($field['field_subtype']) ? $field['field_subtype'] : '',
                'value' => $field['value'],
                'label' => isset($field['label']) ? $field['label'] : '',
                'data_id' => isset($field['data_id']) ? $field['data_id'] : null,
                'visitor_selector' => isset($field['visitor_selector']) ? $field['visitor_selector'] : null
            ];
        }
    }

    return $published_edits;
}

// AJAX handler for customer image upload
add_action('wp_ajax_upload_customer_image', 'handle_customer_image_upload');
add_action('wp_ajax_nopriv_upload_customer_image', 'handle_customer_image_upload');

function handle_customer_image_upload() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'customer_image_upload')) {
        wp_send_json_error('Security check failed');
        return;
    }

    // Check if file was uploaded
    if (!isset($_FILES['file']) || $_FILES['file']['error'] !== UPLOAD_ERR_OK) {
        wp_send_json_error('No file uploaded or upload error');
        return;
    }

    $file = $_FILES['file'];

    // Validate file type
    $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
    if (!in_array($file['type'], $allowed_types)) {
        wp_send_json_error('Invalid file type. Please upload JPG, PNG, or GIF images only.');
        return;
    }

    // Validate file size (800KB)
    if ($file['size'] > 800 * 1024) {
        wp_send_json_error('File size too large. Maximum size is 800KB.');
        return;
    }

    // Include WordPress file handling functions
    if (!function_exists('wp_handle_upload')) {
        require_once(ABSPATH . 'wp-admin/includes/file.php');
    }

    // Handle the upload
    $upload_overrides = [
        'test_form' => false,
        'unique_filename_callback' => function($dir, $name, $ext) {
            return 'customer-edit-' . time() . '-' . $name;
        }
    ];

    $uploaded_file = wp_handle_upload($file, $upload_overrides);

    if (isset($uploaded_file['error'])) {
        wp_send_json_error('Upload failed: ' . $uploaded_file['error']);
        return;
    }

    // Create attachment
    $attachment = [
        'post_mime_type' => $uploaded_file['type'],
        'post_title' => sanitize_file_name(basename($uploaded_file['file'])),
        'post_content' => '',
        'post_status' => 'inherit'
    ];

    $attachment_id = wp_insert_attachment($attachment, $uploaded_file['file']);

    if (is_wp_error($attachment_id)) {
        wp_send_json_error('Failed to create attachment');
        return;
    }

    // Generate attachment metadata
    if (!function_exists('wp_generate_attachment_metadata')) {
        require_once(ABSPATH . 'wp-admin/includes/image.php');
    }

    $attachment_data = wp_generate_attachment_metadata($attachment_id, $uploaded_file['file']);
    wp_update_attachment_metadata($attachment_id, $attachment_data);

    wp_send_json_success([
        'url' => $uploaded_file['url'],
        'attachment_id' => $attachment_id,
        'message' => 'Image uploaded successfully'
    ]);
}

// Function to update page content using render & replace approach
function update_page_builder_content_with_customer_values($post_id, $customer_fields) {
    try {
        error_log("=== RENDER & REPLACE APPROACH START ===");

        $page_builder = detect_page_builder($post_id);
        error_log("Page builder detected: $page_builder");

        // Step 1: Render the current page HTML
        $rendered_html = render_page_html($post_id);
        if (!$rendered_html) {
            return ['success' => false, 'message' => 'Failed to render page HTML'];
        }

        error_log("Page HTML rendered successfully (" . strlen($rendered_html) . " chars)");

        // Debug: Save rendered HTML for inspection
        $debug_file = dirname(__FILE__) . '/debug/rendered_html_debug.html';
        file_put_contents($debug_file, $rendered_html);
        error_log("Rendered HTML saved to debug/rendered_html_debug.html for inspection");

        // Step 2: Apply customer values to rendered HTML
        $updated_html = $rendered_html;
        $updates_made = 0;

        foreach ($customer_fields as $index => $field) {
            if ($field['status'] !== 'published') {
                continue;
            }

            $selector = $field['selector'];
            $field_type = $field['field_type'];
            $field_subtype = isset($field['field_subtype']) ? $field['field_subtype'] : '';
            $value = $field['value'];

            error_log("=== PROCESSING FIELD $index ===");
            error_log("Selector: '$selector'");
            error_log("Type: '$field_type'");
            error_log("Subtype: '$field_subtype'");
            error_log("Value: '$value'");
            error_log("HTML length: " . strlen($updated_html) . " characters");

            if (apply_value_to_rendered_html($updated_html, $selector, $value, $field_type, $field_subtype)) {
                $updates_made++;
                error_log("Field $index: SUCCESS - Applied to rendered HTML");
            } else {
                error_log("Field $index: FAILED - Could not apply to rendered HTML");
                error_log("This field will not be updated in the published page");
            }
            error_log("=== END FIELD $index ===");
        }

        // Step 3: Save the rendered HTML and mark as plugin-controlled
        if ($updates_made > 0) {
            // Save the updated HTML as plugin-rendered content
            update_post_meta($post_id, '_customer_plugin_rendered_html', $updated_html);
            update_post_meta($post_id, '_customer_plugin_render_timestamp', time());
            update_post_meta($post_id, '_customer_plugin_force_render', true);

            error_log("Plugin-rendered HTML saved with $updates_made updates");
        }

        error_log("=== RENDER & REPLACE APPROACH END ===");

        return [
            'success' => true,
            'message' => "Page content updated successfully. {$updates_made} changes applied using render & replace method.",
            'updates_made' => $updates_made,
            'page_builder' => $page_builder,
            'method' => 'render_and_replace'
        ];

    } catch (Exception $e) {
        error_log("ERROR in render & replace: " . $e->getMessage());
        return ['success' => false, 'message' => 'Error: ' . $e->getMessage()];
    }
}

// Function to render page HTML (simulate page load)
function render_page_html($post_id) {
    try {
        error_log("Rendering HTML for post ID: $post_id");

        // Get the page URL
        $page_url = get_permalink($post_id);
        if (!$page_url) {
            error_log("ERROR: Could not get permalink for post $post_id");
            return false;
        }

        error_log("Page URL: $page_url");

        // Use WordPress HTTP API to fetch the rendered page
        $response = wp_remote_get($page_url, [
            'timeout' => 30,
            'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'sslverify' => false, // For local development
            'headers' => [
                'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language' => 'en-US,en;q=0.5',
                'Accept-Encoding' => 'gzip, deflate',
                'Cache-Control' => 'no-cache',
                'Pragma' => 'no-cache'
            ]
        ]);

        if (is_wp_error($response)) {
            error_log("ERROR: HTTP request failed: " . $response->get_error_message());
            return false;
        }

        $http_code = wp_remote_retrieve_response_code($response);
        if ($http_code !== 200) {
            error_log("ERROR: HTTP request returned code $http_code");
            return false;
        }

        $html = wp_remote_retrieve_body($response);
        if (empty($html)) {
            error_log("ERROR: Empty HTML response");
            return false;
        }

        error_log("HTML rendered successfully (" . strlen($html) . " characters)");

        // Debug: Check if we got full HTML or just content
        if (strpos($html, '<html') === false) {
            error_log("WARNING: Response doesn't contain full HTML structure");
            error_log("Response preview: " . substr($html, 0, 200) . "...");
        }

        if (strpos($html, 'elementor') === false) {
            error_log("WARNING: Response doesn't contain Elementor content");
        }

        if (strpos($html, 'data-id') === false) {
            error_log("WARNING: Response doesn't contain data-id attributes");
            error_log("Trying alternative method to get full page content...");

            // Alternative method: Try to render the page content directly
            $alternative_html = render_page_content_directly($post_id);
            if ($alternative_html && strpos($alternative_html, 'data-id') !== false) {
                error_log("SUCCESS: Alternative method found data-id attributes");
                return $alternative_html;
            }
        }

        return $html;

    } catch (Exception $e) {
        error_log("ERROR in render_page_html: " . $e->getMessage());
        return false;
    }
}

// Alternative method to render page content directly
function render_page_content_directly($post_id) {
    try {
        error_log("Trying alternative rendering method for post $post_id");

        // Get the post
        $post = get_post($post_id);
        if (!$post) {
            error_log("ERROR: Could not get post $post_id");
            return false;
        }

        // Set up global post data
        global $wp_query, $post;
        $original_post = $post;
        $post = get_post($post_id);
        setup_postdata($post);

        // Start output buffering
        ob_start();

        // Try to include the page template
        $template = get_page_template();
        if ($template && file_exists($template)) {
            include $template;
        } else {
            // Fallback to content rendering
            echo apply_filters('the_content', $post->post_content);
        }

        // Get the output
        $html = ob_get_clean();

        // Restore original post data
        $post = $original_post;
        wp_reset_postdata();

        error_log("Alternative rendering completed (" . strlen($html) . " characters)");

        if (strpos($html, 'elementor') !== false) {
            error_log("SUCCESS: Alternative method contains Elementor content");
        }

        return $html;

    } catch (Exception $e) {
        error_log("ERROR in alternative rendering: " . $e->getMessage());
        return false;
    }
}

// Function to apply customer value to rendered HTML
function apply_value_to_rendered_html(&$html, $selector, $value, $field_type, $field_subtype) {
    try {
        error_log("=== APPLY VALUE TO HTML START ===");
        error_log("Selector: '$selector'");
        error_log("Field Type: '$field_type'");
        error_log("Field Subtype: '$field_subtype'");
        error_log("Value: '$value'");
        error_log("HTML length: " . strlen($html) . " characters");

        // TRY SAFER STRING-BASED REPLACEMENT FIRST (preserves layout)
        if (try_safe_string_replacement($html, $selector, $value, $field_type, $field_subtype)) {
            error_log("SUCCESS: Safe string replacement worked - layout preserved");
            return true;
        }

        error_log("Safe string replacement failed, trying DOM manipulation...");

        // Use DOMDocument for reliable HTML parsing with CONSERVATIVE settings
        $dom = new DOMDocument('1.0', 'UTF-8');

        // CRITICAL: Preserve formatting and whitespace to avoid layout breaks
        $dom->preserveWhiteSpace = true;
        $dom->formatOutput = false;
        $dom->substituteEntities = false;

        // Suppress warnings for malformed HTML
        libxml_use_internal_errors(true);

        // Load HTML with CONSERVATIVE flags to preserve structure
        $dom->loadHTML(
            '<!DOCTYPE html><html><head><meta charset="UTF-8"></head><body>' . $html . '</body></html>',
            LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD | LIBXML_NOENT | LIBXML_NONET
        );

        // Clear libxml errors
        libxml_clear_errors();

        error_log("DOM loaded successfully");

        // Convert CSS selector to XPath
        $xpath_query = css_selector_to_xpath($selector);
        if (!$xpath_query) {
            error_log("ERROR: Could not convert CSS selector to XPath: $selector");
            return false;
        }

        $xpath = new DOMXPath($dom);

        // SMART SELECTOR RESOLUTION: Try to find data-id from complex selector
        $smart_selector = resolve_smart_selector($dom, $xpath, $selector, $field_type);
        if ($smart_selector && $smart_selector !== $selector) {
            error_log("SMART RESOLUTION: Using '$smart_selector' instead of '$selector'");
            $selector = $smart_selector;
            $xpath_query = css_selector_to_xpath($selector);
            error_log("SMART RESOLUTION: New XPath query: '$xpath_query'");
        }

        error_log("Executing XPath query: '$xpath_query'");

        $elements = $xpath->query($xpath_query);

        if ($elements === false) {
            error_log("ERROR: XPath query failed");
            return false;
        }

        error_log("XPath query executed. Found " . $elements->length . " elements");

        if ($elements->length === 0) {
            error_log("WARNING: No elements found for complex selector: $selector");

            // Try fallback strategies for complex selectors
            $fallback_elements = try_fallback_selectors($xpath, $selector);
            if ($fallback_elements && $fallback_elements->length > 0) {
                error_log("SUCCESS: Found " . $fallback_elements->length . " elements using fallback strategy");
                $elements = $fallback_elements;
            } else {
                error_log("FAILED: No elements found even with fallback strategies");
                return false;
            }
        }

        error_log("Found " . $elements->length . " matching elements");

        // Apply the value based on field type
        $updated = false;
        foreach ($elements as $index => $element) {
            error_log("Processing element $index: " . $element->nodeName);

            // Add safety check - if we're updating more than 1 element, something is wrong
            if ($elements->length > 1) {
                error_log("WARNING: Found " . $elements->length . " elements for selector '$selector' - this might be too broad!");
                error_log("Selector should target exactly 1 element. Only updating the first element to prevent mass replacement");
                if ($index > 0) {
                    break; // Only update the first element
                }
            }

            if (apply_value_to_dom_element($element, $value, $field_type, $field_subtype)) {
                $updated = true;
                error_log("Element $index updated successfully");
            } else {
                error_log("Element $index update failed");
            }
        }

        if ($updated) {
            // Save the updated HTML back with CONSERVATIVE approach
            $body = $dom->getElementsByTagName('body')->item(0);
            if ($body) {
                // Extract only the body content to preserve original structure
                $html = '';
                foreach ($body->childNodes as $child) {
                    $html .= $dom->saveHTML($child);
                }
            } else {
                // Fallback to full HTML if body not found
                $html = $dom->saveHTML();
                // Remove the DOCTYPE and html/body wrapper we added
                $html = preg_replace('/^<!DOCTYPE[^>]*>\s*<html><head><meta charset="UTF-8"><\/head><body>/', '', $html);
                $html = preg_replace('/<\/body><\/html>\s*$/', '', $html);
            }

            error_log("HTML updated successfully for selector: $selector");
            error_log("=== APPLY VALUE TO HTML END (SUCCESS) ===");
            return true;
        }

        error_log("=== APPLY VALUE TO HTML END (NO UPDATES) ===");
        return false;

    } catch (Exception $e) {
        error_log("ERROR in apply_value_to_rendered_html: " . $e->getMessage());
        error_log("=== APPLY VALUE TO HTML END (ERROR) ===");
        return false;
    }
}

// Safe string-based replacement that preserves layout
function try_safe_string_replacement(&$html, $selector, $value, $field_type, $field_subtype) {
    error_log("=== TRYING SAFE STRING REPLACEMENT ===");

    // Extract data-id from selector if available
    if (preg_match('/\[data-id="([^"]+)"\]/', $selector, $matches)) {
        $data_id = $matches[1];
        error_log("Found data-id in selector: $data_id");

        // Find the widget with this data-id using a more precise pattern
        $pattern = '/(<[^>]*data-id="' . preg_quote($data_id, '/') . '"[^>]*>)/';
        if (preg_match($pattern, $html, $widget_matches, PREG_OFFSET_CAPTURE)) {
            $widget_start = $widget_matches[0][1];
            error_log("Found widget start for data-id: $data_id at position $widget_start");

            // Apply safe replacement for images only (safest)
            if ($field_type === 'image') {
                return try_safe_image_replacement($html, $data_id, $value);
            }
        }
    }

    error_log("Safe string replacement not applicable for this selector/type");
    return false;
}

// Safe image replacement within data-id widget
function try_safe_image_replacement(&$html, $data_id, $new_src) {
    // Find img tag within the data-id widget
    $pattern = '/(<[^>]*data-id="' . preg_quote($data_id, '/') . '"[^>]*>.*?)(<img[^>]*\s+src=")[^"]*("[^>]*>)(.*?<\/[^>]+>)/s';

    if (preg_match($pattern, $html, $matches)) {
        $before_img = $matches[1];
        $img_start = $matches[2];
        $img_end = $matches[3];
        $after_img = $matches[4];

        // Build the replacement with new src
        $new_img = $img_start . $new_src . $img_end;

        // Handle srcset - update it to use same new image URL with original size descriptors
        if (preg_match('/\s+srcset="([^"]*)"/i', $new_img, $srcset_matches)) {
            $original_srcset = $srcset_matches[1];
            $new_srcset = generate_responsive_srcset_php($new_src, $original_srcset);
            if ($new_srcset) {
                $new_img = preg_replace('/\s+srcset="[^"]*"/i', ' srcset="' . $new_srcset . '"', $new_img);
                error_log("Updated srcset in HTML: " . $new_srcset);
            } else {
                // If we can't generate proper srcset, remove it to prevent conflicts
                $new_img = preg_replace('/\s+srcset="[^"]*"/i', '', $new_img);
                error_log("Removed srcset from HTML (could not generate proper responsive set)");
            }
        }
        // Keep sizes attribute - it defines responsive behavior

        $replacement = $before_img . $new_img . $after_img;

        // Replace in the full HTML
        $html = str_replace($matches[0], $replacement, $html);

        error_log("SUCCESS: Safe image replacement completed for data-id: $data_id");
        return true;
    }

    error_log("Could not find img tag within data-id widget: $data_id");
    return false;
}

// Try fallback selectors when complex selector fails
function try_fallback_selectors($xpath, $original_selector) {
    error_log("=== TRYING FALLBACK SELECTORS ===");

    // Strategy 1: Try Elementor data-id attribute selector (most reliable)
    if (preg_match('/data-id="([^"]+)"/', $original_selector, $matches)) {
        $data_id = $matches[1];
        error_log("Fallback 1: Trying Elementor data-id selector: '[data-id=\"$data_id\"]'");

        $elements = $xpath->query("//*[@data-id='$data_id']");
        if ($elements && $elements->length == 1) {
            error_log("Fallback 1 SUCCESS: Found exactly 1 element with data-id='$data_id'");
            return $elements;
        } else {
            error_log("Fallback 1 SKIPPED: Found " . $elements->length . " elements with data-id='$data_id'");
        }
    }

    // Strategy 2: Try the last element only (most specific target)
    $parts = explode(' > ', $original_selector);
    $last_part = end($parts);
    error_log("Fallback 2: Trying last element only: '$last_part'");

    $last_xpath = css_selector_to_xpath($last_part);
    if ($last_xpath) {
        $elements = $xpath->query($last_xpath);
        if ($elements && $elements->length > 0) {
            // Only use this fallback if it finds exactly 1 element (precise targeting)
            if ($elements->length == 1) {
                error_log("Fallback 2 SUCCESS: Found exactly 1 element (precise match)");
                return $elements;
            } else {
                error_log("Fallback 2 SKIPPED: Found " . $elements->length . " elements (not precise enough)");
            }
        }
    }

    // Strategy 3: Try last 2 elements (parent > child)
    if (count($parts) >= 2) {
        $last_two = $parts[count($parts)-2] . ' > ' . $parts[count($parts)-1];
        error_log("Fallback 3: Trying last 2 elements: '$last_two'");

        $last_two_xpath = css_selector_to_xpath($last_two);
        if ($last_two_xpath) {
            $elements = $xpath->query($last_two_xpath);
            if ($elements && $elements->length == 1) {
                error_log("Fallback 3 SUCCESS: Found exactly 1 element (precise match)");
                return $elements;
            } else if ($elements && $elements->length > 1) {
                error_log("Fallback 3 SKIPPED: Found " . $elements->length . " elements (not precise enough)");
            }
        }
    }

    // Strategy 4: Try last 3 elements (grandparent > parent > child)
    if (count($parts) >= 3) {
        $last_three = $parts[count($parts)-3] . ' > ' . $parts[count($parts)-2] . ' > ' . $parts[count($parts)-1];
        error_log("Fallback 4: Trying last 3 elements: '$last_three'");

        $last_three_xpath = css_selector_to_xpath($last_three);
        if ($last_three_xpath) {
            $elements = $xpath->query($last_three_xpath);
            if ($elements && $elements->length == 1) {
                error_log("Fallback 4 SUCCESS: Found exactly 1 element (precise match)");
                return $elements;
            } else if ($elements && $elements->length > 1) {
                error_log("Fallback 4 SKIPPED: Found " . $elements->length . " elements (not precise enough)");
            }
        }
    }

    // Strategy 4: REMOVED - Too broad and dangerous
    // The old strategy would match ALL elements of the same type (e.g., all img tags)
    // This caused the bug where all images were replaced with the same URL
    error_log("Fallback 4: Skipped - avoiding overly broad element type matching");

    error_log("=== ALL FALLBACK STRATEGIES FAILED ===");
    return null;
}

// Smart selector resolution: Find data-id from complex selector
function resolve_smart_selector($dom, $xpath, $original_selector, $field_type) {
    error_log("=== SMART SELECTOR RESOLUTION START ===");
    error_log("Original selector: '$original_selector'");
    error_log("Field type: '$field_type'");

    // First, try to find the element using the original complex selector
    $xpath_query = css_selector_to_xpath($original_selector);
    if (!$xpath_query) {
        error_log("Could not convert original selector to XPath");
        return null;
    }

    $elements = $xpath->query($xpath_query);
    if (!$elements || $elements->length === 0) {
        error_log("Original selector found 0 elements, trying to find data-id in DOM");

        // If original selector fails, try to extract element info and find data-id
        return find_data_id_from_selector_pattern($dom, $xpath, $original_selector, $field_type);
    }

    if ($elements->length > 1) {
        error_log("Original selector found " . $elements->length . " elements (too many), trying data-id approach");
        return find_data_id_from_first_match($elements->item(0), $field_type);
    }

    // If we found exactly 1 element, try to get its data-id for more reliable targeting
    $element = $elements->item(0);
    $data_id_selector = find_data_id_from_element($element, $field_type);

    if ($data_id_selector) {
        error_log("Found data-id selector: '$data_id_selector'");
        return $data_id_selector;
    }

    error_log("No data-id found, keeping original selector");
    return null;
}

// Find data-id from a specific element
function find_data_id_from_element($element, $field_type) {
    if (!$element) return null;

    // Look for data-id in the element or its parents
    $current = $element;
    while ($current && $current->nodeType === XML_ELEMENT_NODE) {
        if ($current->hasAttribute('data-id')) {
            $data_id = $current->getAttribute('data-id');
            $element_tag = strtolower($element->nodeName);
            error_log("Found data-id '$data_id' for element '$element_tag'");
            return "[data-id=\"{$data_id}\"] {$element_tag}";
        }
        $current = $current->parentNode;

        // Stop at document or body
        if (!$current || $current->nodeName === 'body' || $current->nodeName === 'html') {
            break;
        }
    }

    return null;
}

// Find data-id from first matching element
function find_data_id_from_first_match($element, $field_type) {
    return find_data_id_from_element($element, $field_type);
}

// Find data-id by analyzing selector pattern
function find_data_id_from_selector_pattern($dom, $xpath, $selector, $field_type) {
    error_log("Analyzing selector pattern to find data-id");

    // Extract the target element type from the selector
    $element_type = 'div'; // default
    if (preg_match('/(\w+):nth-of-type\(\d+\)$/', $selector, $matches)) {
        $element_type = $matches[1];
    }

    error_log("Looking for '$element_type' elements with data-id attributes");

    // Find all elements of this type that have data-id
    $elements = $xpath->query("//{$element_type}[@data-id]");

    if ($elements && $elements->length > 0) {
        error_log("Found " . $elements->length . " {$element_type} elements with data-id");

        // For now, return the first one (could be enhanced with more logic)
        $element = $elements->item(0);
        return find_data_id_from_element($element, $field_type);
    }

    error_log("No {$element_type} elements with data-id found");
    return null;
}

// Generate data-attribute selector for element
function generate_data_attribute_selector($element, $field_type) {
    // Extract Elementor data-id from the element's HTML
    $data_id = null;

    // Look for data-id in the element or its parents
    $current = $element;
    while ($current && !$data_id) {
        if ($current->hasAttribute('data-id')) {
            $data_id = $current->getAttribute('data-id');
            break;
        }
        $current = $current->parentNode;
        // Stop if we reach document or non-element node
        if (!$current || $current->nodeType !== XML_ELEMENT_NODE) {
            break;
        }
    }

    if (!$data_id) {
        error_log("Could not find data-id for element");
        return null;
    }

    // Generate selector based on field type
    switch ($field_type) {
        case 'image':
            return "[data-id=\"{$data_id}\"] img";
        case 'text':
            // Could be h1, h2, h3, p, span, etc.
            $tag = strtolower($element->nodeName);
            return "[data-id=\"{$data_id}\"] {$tag}";
        case 'button':
        case 'link':
            return "[data-id=\"{$data_id}\"] a";
        case 'iframe':
            return "[data-id=\"{$data_id}\"] iframe";
        case 'video':
            return "[data-id=\"{$data_id}\"] video";
        case 'icon':
            // Could be img or svg
            $tag = strtolower($element->nodeName);
            return "[data-id=\"{$data_id}\"] {$tag}";
        default:
            $tag = strtolower($element->nodeName);
            return "[data-id=\"{$data_id}\"] {$tag}";
    }
}

// Convert CSS selector to XPath query
function css_selector_to_xpath($css_selector) {
    try {
        error_log("=== CSS TO XPATH CONVERSION START ===");
        error_log("Input CSS selector: '$css_selector'");

        // For complex selectors like: div:nth-of-type(2) > section:nth-of-type(3) > ... > a:nth-of-type(1)
        // We need a more sophisticated approach

        $original_selector = $css_selector;
        $xpath = '';

        // Split by child combinator (>)
        $parts = explode(' > ', $css_selector);
        error_log("Split into " . count($parts) . " parts: " . json_encode($parts));

        foreach ($parts as $index => $part) {
            $part = trim($part);
            error_log("Processing part $index: '$part'");

            // Handle nth-of-type selectors
            if (preg_match('/^([a-zA-Z0-9]+):nth-of-type\((\d+)\)$/', $part, $matches)) {
                $element = $matches[1];
                $position = $matches[2];
                // CSS nth-of-type(n) means nth element of that type among siblings
                // XPath equivalent: element[count(preceding-sibling::element) + 1 = n]
                // For child combinators, we need direct child relationship (no //)
                $xpath_part = "{$element}[count(preceding-sibling::{$element}) + 1 = {$position}]";
                error_log("nth-of-type match: element='$element', position='$position' -> '$xpath_part'");
            }
            // Handle data-id selectors (Elementor)
            elseif (preg_match('/^\[data-id="([^"]+)"\]$/', $part, $matches)) {
                $data_id = $matches[1];
                $xpath_part = "*[@data-id='{$data_id}']";
                error_log("Data-ID match: data-id='$data_id' -> '$xpath_part'");
            }
            // Handle ID selectors
            elseif (preg_match('/^#([a-zA-Z0-9_-]+)$/', $part, $matches)) {
                $id = $matches[1];
                $xpath_part = "*[@id='{$id}']";
                error_log("ID match: id='$id' -> '$xpath_part'");
            }
            // Handle class selectors
            elseif (preg_match('/^\.([a-zA-Z0-9_-]+)$/', $part, $matches)) {
                $class = $matches[1];
                $xpath_part = "*[contains(@class,'{$class}')]";
                error_log("Class match: class='$class' -> '$xpath_part'");
            }
            // Handle element with ID: div#myid
            elseif (preg_match('/^([a-zA-Z0-9]+)#([a-zA-Z0-9_-]+)$/', $part, $matches)) {
                $element = $matches[1];
                $id = $matches[2];
                $xpath_part = "{$element}[@id='{$id}']";
                error_log("Element+ID match: element='$element', id='$id' -> '$xpath_part'");
            }
            // Handle plain element
            elseif (preg_match('/^([a-zA-Z0-9]+)$/', $part, $matches)) {
                $element = $matches[1];
                $xpath_part = "{$element}";
                error_log("Element match: element='$element' -> '$xpath_part'");
            }
            else {
                error_log("WARNING: Unrecognized selector part: '$part'");
                $xpath_part = "//*"; // Fallback
            }

            // For child combinators, we need direct child relationship
            if ($index === 0) {
                // First element needs to start from root
                $xpath = '//' . $xpath_part;
            } else {
                // Subsequent elements are direct children
                $xpath .= '/' . $xpath_part;
            }
        }

        error_log("Final XPath: '$xpath'");
        error_log("=== CSS TO XPATH CONVERSION END ===");

        return $xpath;

    } catch (Exception $e) {
        error_log("ERROR converting CSS to XPath: " . $e->getMessage());
        return false;
    }
}

// Generate responsive srcset from new image URL - keep same URL with original size descriptors (PHP version)
function generate_responsive_srcset_php($newImageUrl, $originalSrcset) {
    try {
        error_log("Generating responsive srcset for: " . $newImageUrl);
        error_log("Original srcset: " . $originalSrcset);

        // Parse the original srcset to understand the size pattern
        $srcsetEntries = array_map('trim', explode(',', $originalSrcset));

        $newSrcsetEntries = array();

        foreach ($srcsetEntries as $entry) {
            $parts = explode(' ', trim($entry));
            if (count($parts) >= 2) {
                $sizeDescriptor = end($parts); // Last part is the size descriptor (e.g., "300w", "2x")

                // Use the SAME new image URL for all sizes, just change the size descriptor
                $newSrcsetEntries[] = $newImageUrl . ' ' . $sizeDescriptor;
            }
        }

        if (!empty($newSrcsetEntries)) {
            $newSrcset = implode(', ', $newSrcsetEntries);
            error_log("Generated new srcset (same URL, original sizes): " . $newSrcset);
            return $newSrcset;
        }

        error_log("Could not generate valid srcset");
        return null;

    } catch (Exception $e) {
        error_log("Error generating srcset: " . $e->getMessage());
        return null;
    }
}

// Apply value to DOM element based on field type
function apply_value_to_dom_element($element, $value, $field_type, $field_subtype) {
    try {
        switch ($field_type) {
            case 'text':
                $element->textContent = $value;
                return true;

            case 'button':
                if ($field_subtype === 'label') {
                    $element->textContent = $value;
                    return true;
                } elseif ($field_subtype === 'url') {
                    $element->setAttribute('href', $value);
                    return true;
                }
                break;

            case 'link':
                if ($field_subtype === 'label') {
                    $element->textContent = $value;
                    return true;
                } elseif ($field_subtype === 'url') {
                    $element->setAttribute('href', $value);
                    return true;
                }
                break;

            case 'image':
                // Handle modern WordPress responsive images - preserve responsive behavior
                $element->setAttribute('src', $value);

                // If srcset exists, update it to use the same new image URL with original size descriptors
                $srcset = $element->getAttribute('srcset');
                if (!empty($srcset)) {
                    $newSrcset = generate_responsive_srcset_php($value, $srcset);
                    if ($newSrcset) {
                        $element->setAttribute('srcset', $newSrcset);
                        error_log("Updated srcset to: " . $newSrcset);
                    } else {
                        // If we can't generate proper srcset, remove it to prevent conflicts
                        $element->removeAttribute('srcset');
                        error_log("Removed srcset (could not generate proper responsive set)");
                    }
                }
                // Keep sizes attribute - it defines responsive behavior
                return true;

            case 'iframe':
                $element->setAttribute('src', $value);
                return true;

            case 'video':
                $element->setAttribute('src', $value);
                return true;

            case 'icon':
                if (strpos($value, '<svg') !== false) {
                    $element->innerHTML = $value;
                } else {
                    $element->setAttribute('src', $value);
                }
                return true;
        }

        return false;

    } catch (Exception $e) {
        error_log("ERROR applying value to DOM element: " . $e->getMessage());
        return false;
    }
}

// DISABLED: Template override for plugin-rendered HTML
// This was causing visitors to see minimal content instead of full Elementor layout
// Now using real-time visitor loading instead
/*
add_action('template_redirect', function() {
    global $post;

    // Don't override for logged-in users (they need to see the sidebar)
    if (is_user_logged_in()) {
        return;
    }

    if (!$post || (!is_single() && !is_page())) {
        return;
    }

    // Check if this page should use plugin-rendered HTML
    $force_render = get_post_meta($post->ID, '_customer_plugin_force_render', true);
    if (!$force_render) {
        return;
    }

    $rendered_html = get_post_meta($post->ID, '_customer_plugin_rendered_html', true);
    if (empty($rendered_html)) {
        return;
    }

    // Override the page content with our rendered HTML
    error_log("Serving plugin-rendered HTML for post ID: " . $post->ID . " (non-logged-in user)");

    // Output the rendered HTML and exit
    echo $rendered_html;
    exit;
});
*/

// Detect which page builder is being used
function detect_page_builder($post_id) {
    // Debug logging
    error_log("=== PAGE BUILDER DETECTION FOR POST ID: $post_id ===");

    // Check for Elementor
    $elementor_data = get_post_meta($post_id, '_elementor_data', true);
    error_log("Elementor data check: " . (empty($elementor_data) ? 'EMPTY' : 'FOUND (' . strlen($elementor_data) . ' chars)'));
    if (!empty($elementor_data)) {
        error_log("DETECTED: Elementor");
        return 'elementor';
    }

    // Check for Gutenberg blocks
    $post = get_post($post_id);
    if ($post) {
        $has_blocks = has_blocks($post->post_content);
        error_log("Gutenberg blocks check: " . ($has_blocks ? 'FOUND' : 'NOT FOUND'));
        if ($has_blocks) {
            error_log("DETECTED: Gutenberg");
            return 'gutenberg';
        }
    } else {
        error_log("Post not found for ID: $post_id");
    }

    // Check for other page builders
    $divi_data = get_post_meta($post_id, '_et_pb_use_builder', true);
    error_log("Divi data check: " . ($divi_data === 'on' ? 'FOUND' : 'NOT FOUND'));
    if ($divi_data === 'on') {
        error_log("DETECTED: Divi");
        return 'divi';
    }

    $beaver_data = get_post_meta($post_id, '_fl_builder_enabled', true);
    error_log("Beaver data check: " . ($beaver_data ? 'FOUND' : 'NOT FOUND'));
    if ($beaver_data) {
        error_log("DETECTED: Beaver Builder");
        return 'beaver';
    }

    error_log("DETECTED: Standard WordPress");
    return 'standard';
}

// Update Elementor content
function update_elementor_content($post_id, $customer_fields) {
    error_log("=== ELEMENTOR CONTENT UPDATE START ===");
    error_log("IMPORTANT: Elementor detected but using HTML parsing method for complex selectors");

    // For Elementor pages, we'll use the standard HTML parsing method
    // because the selectors are complex CSS selectors, not simple IDs/classes
    // Elementor renders to HTML, so we can update the rendered HTML content

    $post = get_post($post_id);
    if (!$post) {
        error_log("ERROR: Post not found");
        return 0;
    }

    $content = $post->post_content;
    $original_content = $content;
    $updates_made = 0;

    error_log("Processing " . count($customer_fields) . " customer fields using HTML parsing");

    // Process each customer field using HTML parsing
    foreach ($customer_fields as $index => $field) {
        if ($field['status'] !== 'published') {
            error_log("Field $index: Skipping (status: {$field['status']})");
            continue;
        }

        $selector = $field['selector'];
        $field_type = $field['field_type'];
        $field_subtype = isset($field['field_subtype']) ? $field['field_subtype'] : '';
        $value = $field['value'];

        error_log("Field $index: Processing selector='$selector', type='$field_type', subtype='$field_subtype', value='$value'");

        // Use standard HTML parsing methods
        $updated = false;
        switch ($field_type) {
            case 'text':
                $updated = update_text_content($content, $selector, $value);
                break;
            case 'button':
                if ($field_subtype === 'label') {
                    $updated = update_button_text($content, $selector, $value);
                } elseif ($field_subtype === 'url') {
                    $updated = update_button_url($content, $selector, $value);
                }
                break;
            case 'link':
                if ($field_subtype === 'label') {
                    $updated = update_link_text($content, $selector, $value);
                } elseif ($field_subtype === 'url') {
                    $updated = update_link_url($content, $selector, $value);
                }
                break;
            case 'image':
                $updated = update_image_src($content, $selector, $value);
                break;
            case 'iframe':
                $updated = update_iframe_src($content, $selector, $value);
                break;
            case 'video':
                $updated = update_video_src($content, $selector, $value);
                break;
            case 'icon':
                $updated = update_icon_content($content, $selector, $value);
                break;
        }

        if ($updated) {
            $updates_made++;
            error_log("Field $index: SUCCESS - Updated using HTML parsing!");
        } else {
            error_log("Field $index: FAILED - No HTML match found");
        }
    }

    error_log("Total updates made: $updates_made");

    // Save updated content if changes were made
    if ($content !== $original_content && $updates_made > 0) {
        $result = wp_update_post([
            'ID' => $post_id,
            'post_content' => $content
        ]);

        if (is_wp_error($result)) {
            error_log("ERROR: Failed to update post content: " . $result->get_error_message());
            return 0;
        }

        error_log("Post content updated successfully");

        // Clear Elementor cache
        if (class_exists('\Elementor\Plugin')) {
            \Elementor\Plugin::$instance->files_manager->clear_cache();
            error_log("Elementor cache cleared");
        }
    }

    error_log("=== ELEMENTOR CONTENT UPDATE END ===");
    return $updates_made;
}

// Update Elementor data recursively
function update_elementor_data_recursive(&$data, $selector, $value, $field_type, $field_subtype) {
    $updated = false;

    if (is_array($data)) {
        foreach ($data as &$item) {
            if (is_array($item)) {
                // Check if this element matches our selector
                if (isset($item['settings']) && element_matches_selector($item, $selector)) {
                    if (update_elementor_element_value($item, $value, $field_type, $field_subtype)) {
                        $updated = true;
                    }
                }

                // Recursively check children
                if (update_elementor_data_recursive($item, $selector, $value, $field_type, $field_subtype)) {
                    $updated = true;
                }
            }
        }
    }

    return $updated;
}

// Check if Elementor element matches selector
function element_matches_selector($element, $selector) {
    if (!isset($element['settings'])) {
        error_log("Element has no settings");
        return false;
    }

    $settings = $element['settings'];
    error_log("Checking element with settings: " . json_encode(array_keys($settings)));

    // Check for ID match
    if (strpos($selector, '#') === 0) {
        $id = substr($selector, 1);
        $element_id = isset($settings['_element_id']) ? $settings['_element_id'] : '';
        error_log("ID check: Looking for '$id', found '$element_id'");
        return $element_id === $id;
    }

    // Check for class match
    if (strpos($selector, '.') === 0) {
        $class = substr($selector, 1);
        $css_classes = isset($settings['_css_classes']) ? $settings['_css_classes'] : '';
        error_log("Class check: Looking for '$class', found '$css_classes'");
        return strpos($css_classes, $class) !== false;
    }

    error_log("Selector '$selector' not recognized (not ID or class)");
    return false;
}

// Update Elementor element value
function update_elementor_element_value(&$element, $value, $field_type, $field_subtype) {
    if (!isset($element['settings'])) {
        return false;
    }

    $settings = &$element['settings'];

    switch ($field_type) {
        case 'text':
            if (isset($settings['title'])) {
                $settings['title'] = $value;
                return true;
            } elseif (isset($settings['text'])) {
                $settings['text'] = $value;
                return true;
            } elseif (isset($settings['editor'])) {
                $settings['editor'] = $value;
                return true;
            }
            break;

        case 'button':
            if ($field_subtype === 'label' && isset($settings['text'])) {
                $settings['text'] = $value;
                return true;
            } elseif ($field_subtype === 'url' && isset($settings['link']['url'])) {
                $settings['link']['url'] = $value;
                return true;
            }
            break;

        case 'link':
            if ($field_subtype === 'label' && isset($settings['text'])) {
                $settings['text'] = $value;
                return true;
            } elseif ($field_subtype === 'url' && isset($settings['link']['url'])) {
                $settings['link']['url'] = $value;
                return true;
            }
            break;

        case 'image':
            if (isset($settings['image']['url'])) {
                $settings['image']['url'] = $value;
                return true;
            }
            break;
    }

    return false;
}

// Update Gutenberg content
function update_gutenberg_content($post_id, $customer_fields) {
    $post = get_post($post_id);
    if (!$post) {
        return 0;
    }

    $content = $post->post_content;
    $original_content = $content;
    $updates_made = 0;

    // Process each customer field
    foreach ($customer_fields as $field) {
        if ($field['status'] !== 'published') {
            continue;
        }

        $selector = $field['selector'];
        $field_type = $field['field_type'];
        $field_subtype = isset($field['field_subtype']) ? $field['field_subtype'] : '';
        $value = $field['value'];

        // Update Gutenberg blocks
        if (update_gutenberg_block_content($content, $selector, $value, $field_type, $field_subtype)) {
            $updates_made++;
        }
    }

    // Save updated content
    if ($content !== $original_content && $updates_made > 0) {
        wp_update_post([
            'ID' => $post_id,
            'post_content' => $content
        ]);
    }

    return $updates_made;
}

// Update standard WordPress content
function update_standard_content($post_id, $customer_fields) {
    $post = get_post($post_id);
    if (!$post) {
        return 0;
    }

    $content = $post->post_content;
    $original_content = $content;
    $updates_made = 0;

    // Process each customer field using the original HTML parsing method
    foreach ($customer_fields as $field) {
        if ($field['status'] !== 'published') {
            continue;
        }

        $selector = $field['selector'];
        $field_type = $field['field_type'];
        $field_subtype = isset($field['field_subtype']) ? $field['field_subtype'] : '';
        $value = $field['value'];

        // Use original content update methods
        $updated = false;
        switch ($field_type) {
            case 'text':
                $updated = update_text_content($content, $selector, $value);
                break;
            case 'button':
                if ($field_subtype === 'label') {
                    $updated = update_button_text($content, $selector, $value);
                } elseif ($field_subtype === 'url') {
                    $updated = update_button_url($content, $selector, $value);
                }
                break;
            case 'link':
                if ($field_subtype === 'label') {
                    $updated = update_link_text($content, $selector, $value);
                } elseif ($field_subtype === 'url') {
                    $updated = update_link_url($content, $selector, $value);
                }
                break;
            case 'image':
                $updated = update_image_src($content, $selector, $value);
                break;
        }

        if ($updated) {
            $updates_made++;
        }
    }

    // Save updated content
    if ($content !== $original_content && $updates_made > 0) {
        wp_update_post([
            'ID' => $post_id,
            'post_content' => $content
        ]);
    }

    return $updates_made;
}

// Update Gutenberg block content
function update_gutenberg_block_content(&$content, $selector, $value, $field_type, $field_subtype) {
    // For Gutenberg, we'll use the same HTML parsing approach
    // since Gutenberg blocks are rendered as HTML in post_content
    switch ($field_type) {
        case 'text':
            return update_text_content($content, $selector, $value);
        case 'button':
            if ($field_subtype === 'label') {
                return update_button_text($content, $selector, $value);
            } elseif ($field_subtype === 'url') {
                return update_button_url($content, $selector, $value);
            }
            break;
        case 'link':
            if ($field_subtype === 'label') {
                return update_link_text($content, $selector, $value);
            } elseif ($field_subtype === 'url') {
                return update_link_url($content, $selector, $value);
            }
            break;
        case 'image':
            return update_image_src($content, $selector, $value);
        case 'iframe':
            return update_iframe_src($content, $selector, $value);
        case 'video':
            return update_video_src($content, $selector, $value);
    }

    return false;
}

// Helper functions to update different types of content in HTML

function update_text_content(&$content, $selector, $value) {
    // Convert CSS selector to find text elements
    $patterns = get_selector_patterns($selector);
    $updated = false;

    foreach ($patterns as $pattern) {
        if (preg_match($pattern['regex'], $content, $matches)) {
            $new_content = str_replace($matches[0], $pattern['replacement']($matches, $value), $content);
            if ($new_content !== $content) {
                $content = $new_content;
                $updated = true;
            }
        }
    }

    return $updated;
}

function update_button_text(&$content, $selector, $value) {
    return update_text_content($content, $selector, $value);
}

function update_button_url(&$content, $selector, $value) {
    return update_href_attribute($content, $selector, $value);
}

function update_link_text(&$content, $selector, $value) {
    return update_text_content($content, $selector, $value);
}

function update_link_url(&$content, $selector, $value) {
    return update_href_attribute($content, $selector, $value);
}

function update_image_src(&$content, $selector, $value) {
    return update_src_attribute($content, $selector, $value);
}

function update_iframe_src(&$content, $selector, $value) {
    return update_src_attribute($content, $selector, $value);
}

function update_video_src(&$content, $selector, $value) {
    return update_src_attribute($content, $selector, $value);
}

function update_icon_content(&$content, $selector, $value) {
    // For icons, could be src attribute or inner HTML (for SVG)
    if (strpos($value, '<svg') !== false) {
        return update_inner_html($content, $selector, $value);
    } else {
        return update_src_attribute($content, $selector, $value);
    }
}

function update_href_attribute(&$content, $selector, $value) {
    $patterns = get_selector_patterns($selector, 'href');
    $updated = false;

    foreach ($patterns as $pattern) {
        $content = preg_replace_callback($pattern['regex'], function($matches) use ($value) {
            return preg_replace('/href=["\'][^"\']*["\']/', 'href="' . esc_attr($value) . '"', $matches[0]);
        }, $content, -1, $count);

        if ($count > 0) {
            $updated = true;
        }
    }

    return $updated;
}

function update_src_attribute(&$content, $selector, $value) {
    $patterns = get_selector_patterns($selector, 'src');
    $updated = false;

    foreach ($patterns as $pattern) {
        $content = preg_replace_callback($pattern['regex'], function($matches) use ($value) {
            return preg_replace('/src=["\'][^"\']*["\']/', 'src="' . esc_attr($value) . '"', $matches[0]);
        }, $content, -1, $count);

        if ($count > 0) {
            $updated = true;
        }
    }

    return $updated;
}

function update_inner_html(&$content, $selector, $value) {
    $patterns = get_selector_patterns($selector, 'innerHTML');
    $updated = false;

    foreach ($patterns as $pattern) {
        if (preg_match($pattern['regex'], $content, $matches)) {
            $opening_tag = $matches[1];
            $closing_tag = $matches[3];
            $new_element = $opening_tag . $value . $closing_tag;

            $new_content = str_replace($matches[0], $new_element, $content);
            if ($new_content !== $content) {
                $content = $new_content;
                $updated = true;
            }
        }
    }

    return $updated;
}

function get_selector_patterns($selector, $type = 'text') {
    $patterns = [];

    // Handle different CSS selector types
    if (strpos($selector, '#') === 0) {
        // ID selector: #my-id
        $id = substr($selector, 1);
        $patterns[] = create_pattern_for_id($id, $type);
    } elseif (strpos($selector, '.') === 0) {
        // Class selector: .my-class
        $class = substr($selector, 1);
        $patterns[] = create_pattern_for_class($class, $type);
    } else {
        // Element selector: h1, div, etc.
        $patterns[] = create_pattern_for_element($selector, $type);
    }

    return $patterns;
}

function create_pattern_for_id($id, $type) {
    switch ($type) {
        case 'text':
        case 'innerHTML':
            return [
                'regex' => '/(<[^>]*id=["\']' . preg_quote($id, '/') . '["\'][^>]*>)(.*?)(<\/[^>]+>)/s',
                'replacement' => function($matches, $value) {
                    return $matches[1] . $value . $matches[3];
                }
            ];
        case 'href':
        case 'src':
            return [
                'regex' => '/<[^>]*id=["\']' . preg_quote($id, '/') . '["\'][^>]*>/s'
            ];
        default:
            return ['regex' => '/.*/', 'replacement' => function($m, $v) { return $m[0]; }];
    }
}

function create_pattern_for_class($class, $type) {
    switch ($type) {
        case 'text':
        case 'innerHTML':
            return [
                'regex' => '/(<[^>]*class=["\'][^"\']*' . preg_quote($class, '/') . '[^"\']*["\'][^>]*>)(.*?)(<\/[^>]+>)/s',
                'replacement' => function($matches, $value) {
                    return $matches[1] . $value . $matches[3];
                }
            ];
        case 'href':
        case 'src':
            return [
                'regex' => '/<[^>]*class=["\'][^"\']*' . preg_quote($class, '/') . '[^"\']*["\'][^>]*>/s'
            ];
        default:
            return ['regex' => '/.*/', 'replacement' => function($m, $v) { return $m[0]; }];
    }
}

function create_pattern_for_element($element, $type) {
    switch ($type) {
        case 'text':
        case 'innerHTML':
            return [
                'regex' => '/(<' . preg_quote($element, '/') . '[^>]*>)(.*?)(<\/' . preg_quote($element, '/') . '>)/s',
                'replacement' => function($matches, $value) {
                    return $matches[1] . $value . $matches[3];
                }
            ];
        case 'href':
        case 'src':
            return [
                'regex' => '/<' . preg_quote($element, '/') . '[^>]*>/s'
            ];
        default:
            return ['regex' => '/.*/', 'replacement' => function($m, $v) { return $m[0]; }];
    }
}

// add_action('acf/init', function() {
//     if (!function_exists('acf_add_local_field_group')) return;

//     acf_add_local_field_group([
//         'key' => 'group_test_33',
//         'title' => 'Test Group 33',
//         'fields' => [
//             [
//                 'key' => 'field_test_33',
//                 'label' => 'Test Field',
//                 'name' => 'test_field_33',
//                 'type' => 'text',
//             ]
//         ],
//         'location' => [
//             [
//                 [
//                     'param' => 'post_id',
//                     'operator' => '==',
//                     'value' => 33,
//                 ],
//             ],
//         ],
//     ]);
// });


add_action('wp_ajax_designer_remove_editable', function() {
    check_ajax_referer('designer_tagging_nonce', 'nonce');
    $post_id = intval($_POST['post_id']);
    $index = intval($_POST['index']);

    $fields = get_post_meta($post_id, '_designer_editable_fields', true);
    if (!$fields || !isset($fields[$index])) {
        wp_send_json_error('Tag not found');
    }

    // Xóa tag
    unset($fields[$index]);
    $fields = array_values($fields); // Reindex array
    update_post_meta($post_id, '_designer_editable_fields', $fields);

    // Render lại HTML danh sách các tag
    ob_start();

    // Gom nhóm các tag theo section
    $grouped_fields = [];
    foreach ($fields as $i => $f) {
        $section_key = !empty($f['section']) ? $f['section'] : 'section1';
        if (!isset($grouped_fields[$section_key])) {
            $grouped_fields[$section_key] = [];
        }
        $f['index'] = $i; // Lưu index gốc để xóa đúng
        $grouped_fields[$section_key][] = $f;
    }

    // Hiển thị các tag theo nhóm section
    foreach ($grouped_fields as $section_key => $section_fields) {
        $section_name = get_section_name_by_key($section_key, $post_id);
        ?>
        <div class="section-header">
            <h4><?php echo esc_html($section_name); ?></h4>
        </div>
        <?php foreach ($section_fields as $f): ?>
          <div class="accordion" data-selector="<?php echo esc_attr($f['selector']); ?>" data-index="<?php echo $f['index']; ?>">
            <span class="accordion-title"><?php echo esc_html($f['label']); ?> (<?php echo esc_html($f['type']); ?>)</span>
            <span class="accordion-actions">
              <span class="accordion-delete" title="Delete tag"><i class="fa fa-trash"></i></span>
              <span class="accordion-arrow"><i class="fa fa-chevron-down"></i></span>
            </span>
          </div>
          <div class="panel">
            <div class="tag-head mb-3">
              <div class="form-group">
                    <label for="title">Title:</label>
                    <input type="text" value="<?php echo esc_html($f['label']); ?>">
                </div>
                <div class="form-group">
                    <label for="title">Tooltip:</label>
                    <input type="text" value="Add your <?php echo esc_html($f['label']); ?>">
                </div>
                <?php
                  switch($f['type']){
                    case 'text':
                      ?>
                        <div class="form-group">
                            <label for="title">Value:</label>
                            <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> value">
                        </div>
                      <?php
                      break;
                    case 'image':
                      ?>
                        <div class="form-group">
                            <label for="tooltip">Value URL:</label>
                            <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> URL">
                        </div>
                      <?php
                      break;
                     case 'link':
                        ?>
                          <div class="form-group">
                              <label for="tooltip">Value URL:</label>
                              <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> URL">
                          </div>
                        <?php
                        break;
                    case 'button':
                      $additional = json_decode(isset($f['additional_data']) ? $f['additional_data'] : '{}', true);
                      $button_url = isset($additional['url']) ? $additional['url'] : '';
                      ?>
                        <div class="form-group">
                            <label for="title">Label:</label>
                            <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> value">
                        </div>
                        <div class="form-group">
                            <label for="title">URL:</label>
                            <input type="text" readonly value="<?php echo esc_attr($button_url); ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> URL">
                        </div>
                      <?php
                      break;
                    case 'video':
                      ?>
                        <div class="form-group">
                            <label for="tooltip">Value URL:</label>
                            <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> URL">
                        </div>
                      <?php
                      break;
                    case 'iframe':
                      ?>
                        <div class="form-group">
                            <label for="title">URL:</label>
                            <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> URL">
                        </div>
                      <?php
                      break;
                    case 'default':
                      ?>
                        <div class="form-group">
                            <label for="title">Value:</label>
                            <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> value">
                        </div>
                      <?php
                      break;
                  }
                ?>
            </div>
          </div>
        <?php endforeach; ?>
    <?php }
    $tagged_fields_html = ob_get_clean();

    wp_send_json_success([
        'message' => 'Tag deleted successfully!',
        'tagged_fields_html' => $tagged_fields_html
    ]);
});

add_action('wp_ajax_nopriv_designer_remove_editable', function() {
    check_ajax_referer('designer_tagging_nonce', 'nonce');
    $post_id = intval($_POST['post_id']);
    $index = intval($_POST['index']);
    
    $fields = get_post_meta($post_id, '_designer_editable_fields', true);
    if (!$fields || !isset($fields[$index])) {
        wp_send_json_error('Tag not found');
    }
    
    // Xóa tag
    unset($fields[$index]);
    $fields = array_values($fields); // Reindex array
    update_post_meta($post_id, '_designer_editable_fields', $fields);
    
    // Render lại HTML danh sách các tag
    ob_start();
    
    // Gom nhóm các tag theo section
    $grouped_fields = [];
    foreach ($fields as $i => $f) {
        $section_key = !empty($f['section']) ? $f['section'] : 'section1';
        if (!isset($grouped_fields[$section_key])) {
            $grouped_fields[$section_key] = [];
        }
        $f['index'] = $i; // Lưu index gốc để xóa đúng
        $grouped_fields[$section_key][] = $f;
    }
    
    // Hiển thị các tag theo nhóm section
    foreach ($grouped_fields as $section_key => $section_fields) {
        $section_name = get_section_name_by_key($section_key, $post_id);
        ?>
        <div class="section-header">
            <h4><?php echo esc_html($section_name); ?></h4>
        </div>
        <?php foreach ($section_fields as $f): ?>
          <div class="accordion" data-selector="<?php echo esc_attr($f['selector']); ?>" data-index="<?php echo $f['index']; ?>">
            <span class="accordion-title"><?php echo esc_html($f['label']); ?> (<?php echo esc_html($f['type']); ?>)</span>
            <span class="accordion-actions">
              <span class="accordion-delete" title="Delete tag"><i class="fa fa-trash"></i></span>
              <span class="accordion-arrow"><i class="fa fa-chevron-down"></i></span>
            </span>
          </div>
          <div class="panel">
            <div class="tag-head mb-3">
              <div class="form-group">
                    <label for="title">Title:</label>
                    <input type="text" value="<?php echo esc_html($f['label']); ?>">
                </div>
                <div class="form-group">
                    <label for="title">Tooltip:</label>
                    <input type="text" value="Add your <?php echo esc_html($f['label']); ?>">
                </div>
                <?php 
                  switch($f['type']){ 
                    case 'text':
                      ?>
                        <div class="form-group">
                            <label for="title">Value:</label>
                            <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> value">
                        </div>
                      <?php
                      break;
                    case 'image':
                      ?>
                        <div class="form-group">
                            <label for="tooltip">Value URL:</label>
                            <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> URL">
                        </div>
                      <?php
                      break;
                     case 'link':
                        ?>
                          <div class="form-group">
                              <label for="tooltip">Value URL:</label>
                              <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> URL">
                          </div>
                        <?php
                        break;
                    case 'button':
                      $additional = json_decode(isset($f['additional_data']) ? $f['additional_data'] : '{}', true);
                      $button_url = isset($additional['url']) ? $additional['url'] : '';
                      ?>
                        <div class="form-group">
                            <label for="title">Label:</label>
                            <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> value">
                        </div>
                        <div class="form-group">
                            <label for="title">URL:</label>
                            <input type="text" readonly value="<?php echo esc_attr($button_url); ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> URL">
                        </div>
                      <?php
                      break;
                    case 'video':
                      ?>
                        <div class="form-group">
                            <label for="tooltip">Value URL:</label>
                            <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> URL">
                        </div>
                      <?php
                      break;
                    case 'iframe':
                      ?>
                        <div class="form-group">
                            <label for="title">URL:</label>
                            <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> URL">
                        </div>
                      <?php
                      break;
                    case 'default':
                      ?>
                        <div class="form-group">
                            <label for="title">Value:</label>
                            <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> value">
                        </div>
                      <?php
                      break;
                  }
                ?>
            </div>
          </div>
        <?php endforeach; ?>
    <?php } 
    $tagged_fields_html = ob_get_clean();

    wp_send_json_success([
        'message' => 'Tag deleted successfully!',
        'tagged_fields_html' => $tagged_fields_html
    ]);
});


add_action('wp_footer', function() {
    if (!is_singular()) return; // Chỉ hiển thị ở trang chi tiết (page, post)

     // Kiểm tra xem có đang ở trong Elementor Editor không
    $is_elementor_editor = false;
    if (isset($_GET['elementor-preview']) || 
        (isset($_REQUEST['action']) && $_REQUEST['action'] === 'elementor') || 
        (isset($_REQUEST['elementor-preview']))) {
        $is_elementor_editor = true;
    }

    // Nếu đang trong Elementor Editor, không hiển thị sidebar
    if ($is_elementor_editor) {
        return;
    }
    
    // Kiểm tra quyền truy cập dựa trên chế độ - ONLY SHOW SIDEBAR FOR LOGGED-IN USERS
    $show_sidebar = false;

    if(DESIGNER_TAGGING_ADMIN_ONLY) {
      if (current_user_can('edit_posts')) {
        $show_sidebar = true;
      }
    } else if (CUSTOMER_EDIT_MODE) {
      // Only logged-in users see the sidebar interface
      if (is_user_logged_in()) {
        $show_sidebar = true;
      }
      // Visitors don't see sidebar but get JavaScript data (handled in wp_head)
    }

    // If sidebar not needed, exit (visitors will still get JavaScript data from wp_head)
    if (!$show_sidebar) {
      return;
    }

    // Define approval mode variable for JavaScript
    $is_approval_mode = isset($_GET['action']) && $_GET['action'] === 'approve' && !preg_match('/\/designer\//', $_SERVER['REQUEST_URI']);

    //if (!function_exists('acf_form')) return;
    ?>
        <!-- JS / CSS -->
        <script src="https://cdn.tailwindcss.com"></script>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
         <link href="<?php echo plugin_dir_url(__FILE__);?>assets/layout.css" rel="stylesheet">
         <style>
            /* Ensure topbar stays fixed at top */
            #plugin-topbar {
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                right: 0 !important;
                z-index: 999999 !important;
                width: 100% !important;
                box-sizing: border-box !important;
            }

            /* Add padding to body to prevent content from being hidden behind topbar */
            body {
                padding-top: 56px !important;
            }

            /* Ensure main sidebar accounts for topbar */
            #main-sidebar {
                top: 56px !important;
                height: calc(100vh - 56px) !important;
            }

            /* Custom brand color for sidebar menu */
            .bg-brand-main {
                background-color: #48C9B0!important; /* emerald-500 as default brand color */
            }

            .border-brand-main {
                border-color: #48C9B0!important;
            }

            /* Group hover variants for brand colors */
            .group:hover .group-hover\:bg-brand-main {
                background-color: #48C9B0!important;
            }

            .group:hover .group-hover\:border-brand-main {
                border-color: #48C9B0!important;
            }

            /* Active state for sidebar menu items */
            .main_active .w-12 {
                background-color: #48C9B0!important;
                border-color: #48C9B0!important;
            }

            .main_active .fa {
                color: #1f2937 !important; /* dark color */
            }

            /* Customer edit draft status indicators */
            .customer-editable-field.draft-saved {
                border-color: #10b981 !important;
                box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2) !important;
            }

            .customer-editable-field.draft-error {
                border-color: #ef4444 !important;
                box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2) !important;
            }

            /* Draft status animation */
            .customer-editable-field.draft-saved,
            .customer-editable-field.draft-error {
                transition: border-color 0.3s ease, box-shadow 0.3s ease;
            }

            /* Loading overlay to prevent content flash */
            .customer-content-loading {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(255, 255, 255, 0.9);
                z-index: 9999;
                display: flex;
                align-items: center;
                justify-content: center;
                backdrop-filter: blur(2px);
            }

            /* Theme option selection styles */
            .theme-option {
                transition: all 0.3s ease;
                position: relative;
                overflow: hidden;
            }

            .theme-option:hover {
                background: #1a1a1a !important;
                color: white !important;
                border-color: #333 !important;
            }

            .theme-option:hover .theme-info strong,
            .theme-option:hover .theme-info .w3-small {
                color: white !important;
            }

            .theme-option.selected {
                background: #1a1a1a !important;
                color: white !important;
                border-color: #333 !important;
            }

            .theme-option.selected .theme-info strong,
            .theme-option.selected .theme-info .w3-small {
                color: white !important;
            }

            .customer-loading-spinner {
                width: 40px;
                height: 40px;
                border: 3px solid #f3f4f6;
                border-top: 3px solid #3b82f6;
                border-radius: 50%;
                animation: spin 1s linear infinite;
            }

            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }

            .customer-loading-text {
                margin-left: 12px;
                color: #6b7280;
                font-size: 14px;
                font-weight: 500;
            }

            /* Simple sidebar scrolling fix */
            #mySidebar {
                position: fixed !important;
                top: 56px !important;
                right: -400px !important;
                width: 400px !important;
                height: calc(100vh - 56px) !important;
                background: white !important;
                box-shadow: -2px 0 10px rgba(0,0,0,0.2) !important;
                z-index: 999998 !important;
                overflow-y: auto !important;
                transition: right 0.3s ease !important;
            }

            #mySidebar.customform {
                right: 0 !important;
            }

            /* SEO Sidebar positioning - slides in from left like main sidebar */
            #seoSidebar {
                position: fixed !important;
                top: 56px !important;
                left: -400px !important;
                width: 400px !important;
                height: calc(100vh - 56px) !important;
                background: #FFFFFF !important;
                border-right: 1px solid #e5e7eb !important;
                z-index: 999998 !important;
                overflow-y: auto !important;
                transition: left 0.3s ease !important;
            }

            #seoSidebar.customform {
                left: 0 !important;
            }

            /* Sidebar item highlight for bidirectional navigation */
            .sidebar-item-highlight {
                background-color: #e3f2fd !important;
                border-left: 4px solid #2196f3 !important;
                transform: scale(1.02);
                transition: all 0.3s ease;
                box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3) !important;
            }

            /* Make highlighted elements clickable with hover effect */
            .designer-highlight {
                cursor: pointer !important;
                transition: all 0.2s ease;
            }

            .designer-highlight:hover {
                transform: scale(1.02);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
            }

            /* Tagged media overlay clickable styling */
            .tagged-media-overlay {
                cursor: pointer !important;
                transition: all 0.2s ease;
            }

            .tagged-media-overlay:hover {
                background-color: rgba(33, 150, 243, 0.1) !important;
                border-color: #2196f3 !important;
            }
         </style>
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
        <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>

        <script>
        // Ensure jQuery is available before running visitor code
        function initVisitorCustomerEdits() {
            // DEBUG: Show all available data for visitor loading
            if (typeof designerTagging !== 'undefined') {
            }

            // VISITOR REAL-TIME LOADING: Apply published customer edits for visitors
            if (typeof designerTagging !== 'undefined' && designerTagging.customer_edits) {
                applyVisitorCustomerEdits();
            } else {
                if (typeof designerTagging === 'undefined') {
                } else if (!designerTagging.customer_edits) {
                }
            }

            // Initialize page dropdown functionality (only for logged-in users with sidebar)
            if (typeof initPageDropdown === 'function') {
                initPageDropdown();
            }
        }

        // Initialize when jQuery is ready
        if (typeof jQuery !== 'undefined') {
            jQuery(document).ready(initVisitorCustomerEdits);
        } else if (typeof $ !== 'undefined') {
            $(document).ready(initVisitorCustomerEdits);
        } else {
            // Fallback: wait for jQuery to load
            setTimeout(function() {
                if (typeof jQuery !== 'undefined') {
                    jQuery(document).ready(initVisitorCustomerEdits);
                } else if (typeof $ !== 'undefined') {
                    $(document).ready(initVisitorCustomerEdits);
                } else {
                    // Try to run without jQuery for basic functionality
                    if (document.readyState === 'loading') {
                        document.addEventListener('DOMContentLoaded', initVisitorCustomerEdits);
                    } else {
                        initVisitorCustomerEdits();
                    }
                }
            }, 1000);
        }

            function initPageDropdown() {
                // Populate dropdown with pages from sidebar
                populatePageDropdown();

                // Set current page as selected
                setCurrentPageSelected();

                // Handle dropdown toggle
                $('#pageDropdownBtn').on('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    $('#pageDropdown').toggleClass('hidden');

                    // Rotate arrow when dropdown is open/closed
                    const arrow = $(this).find('svg');
                    if ($('#pageDropdown').hasClass('hidden')) {
                        arrow.removeClass('rotate-180');
                    } else {
                        arrow.addClass('rotate-180');
                    }
                });

                // Handle dropdown item clicks
                $('#pageDropdown').on('click', 'a', function(e) {
                    e.preventDefault();
                    const pageUrl = $(this).attr('href');
                    const pageName = $(this).text().trim();

                    // Update dropdown button text
                    $('#pageDropdownBtn span').text(pageName);

                    // Hide dropdown and reset arrow
                    $('#pageDropdown').addClass('hidden');
                    $('#pageDropdownBtn svg').removeClass('rotate-180');

                    // Redirect to page
                    if (pageUrl && pageUrl !== '#') {
                        window.location.href = pageUrl;
                    }
                });

                // Close dropdown when clicking outside
                $(document).on('click', function(e) {
                    if (!$(e.target).closest('.relative').length) {
                        $('#pageDropdown').addClass('hidden');
                    }
                });
            }

            function populatePageDropdown() {
                const $dropdown = $('#pageDropdown');

                // Show loading state
                $dropdown.html('<div class="block px-4 py-2 text-sm text-gray-500"><i class="fa fa-spinner fa-spin"></i> Loading pages...</div>');

                // Fetch WordPress pages via AJAX (same as sidebar)
                const currentUrl = window.location.href;
                $.ajax({
                    url: '<?php echo admin_url('admin-ajax.php'); ?>',
                    type: 'POST',
                    data: {
                        action: 'ipt_get_wordpress_pages',
                        current_url: currentUrl,
                        security: '<?php echo wp_create_nonce('ipt_pages_nonce'); ?>'
                    },
                    success: function(response) {

                        if (response.success && response.data && response.data.pages) {
                            renderDropdownPageList(response.data.pages, response.data.homepage_id, response.data.current_page_id);
                        } else {
                            $dropdown.html('<div class="block px-4 py-2 text-sm text-gray-500">No pages found</div>');
                        }
                    },
                    error: function(xhr, status, error) {
                        $dropdown.html('<div class="block px-4 py-2 text-sm text-red-500">Error loading pages</div>');
                    }
                });
            }

            function renderDropdownPageList(pages, homepageId, currentPageId) {
                const $dropdown = $('#pageDropdown');
                $dropdown.empty();

                if (pages.length === 0) {
                    $dropdown.html('<div class="block px-4 py-2 text-sm text-gray-500">No pages found</div>');
                    return;
                }

                // Sort pages: current page first, then homepage, then alphabetically
                const sortedPages = pages.sort(function(a, b) {
                    // Current page first
                    if (a.id == currentPageId) return -1;
                    if (b.id == currentPageId) return 1;

                    // Homepage second
                    if (a.id == homepageId) return -1;
                    if (b.id == homepageId) return 1;

                    // Then alphabetically
                    return a.title.localeCompare(b.title);
                });

                // Render each page
                sortedPages.forEach(function(page) {
                    const isHomePage = (homepageId && page.id == homepageId);
                    const isCurrentPage = (currentPageId && page.id == currentPageId);

                    // Determine icon
                    let pageIcon = 'fa-file-o';
                    if (isHomePage) {
                        pageIcon = 'fa-home';
                    }

                    // Determine styling
                    let itemClass = 'block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50';
                    if (isCurrentPage) {
                        itemClass += ' bg-blue-50 text-blue-700 font-medium';
                    }

                    // Add page to dropdown
                    // $dropdown.append(`
                    //     <a href="${page.view_url}" class="${itemClass}" data-page-id="${page.id}">
                    //         <i class="fa ${pageIcon} mr-2"></i>${page.title}
                    //     </a>
                    // `);
                    $dropdown.append(`
                        <a href="${page.view_url}" class="${itemClass} text-decoration-none" data-page-id="${page.id}">
                            ${page.title}
                        </a>
                    `);
                });
            }

            function getCurrentPageName() {
                // Try to get page name from various sources
                let pageName = '';

                // 1. From page title
                pageName = document.title.split(' | ')[0].split(' - ')[0];

                // 2. From sidebar if available
                if (!pageName || pageName === document.title) {
                    const $sidebar = $('#pageSectionSidebar');
                    const $activeLink = $sidebar.find('a.active, a.current, .active a, .current a').first();
                    if ($activeLink.length) {
                        pageName = $activeLink.text().trim();
                    }
                }

                // 3. From URL path
                if (!pageName) {
                    const path = window.location.pathname;
                    if (path === '/' || path === '') {
                        pageName = 'Homepage';
                    } else {
                        pageName = path.split('/').filter(Boolean).pop().replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                    }
                }

                return pageName || 'Homepage';
            }

            function setCurrentPageSelected() {
                const currentPageName = getCurrentPageName();
                $('#pageDropdownBtn span').text(currentPageName);
            }
        </script>
          <div id="plugin-topbar" style="position: fixed !important; top: 0 !important; left: 0 !important; right: 0 !important; z-index: 999999 !important; width: 100% !important;">
            <div class="flex items-center justify-between bg-white px-6 py-2 border-b border-gray-200">
              <div class="flex items-center gap-4">
                <!-- <span class="text-sm font-medium text-gray-700">Homepage</span> -->
                <!-- Dropdown menu thay thế cho span Homepage -->
                <div class="relative">
                  <button id="pageDropdownBtn" class="text-sm font-medium text-gray-700 hover:text-gray-900 focus:text-gray-700 focus:outline-none bg-white border-none flex items-center gap-2">
                    <span>Homepage</span>
                    <svg class="w-4 h-4 transform transition-transform" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path fill-rule="evenodd" clip-rule="evenodd" d="M5.29289 8.29289C5.68342 7.90237 6.31658 7.90237 6.70711 8.29289L12 13.5858L17.2929 8.29289C17.6834 7.90237 18.3166 7.90237 18.7071 8.29289C19.0976 8.68342 19.0976 9.31658 18.7071 9.70711L12.7071 15.7071C12.3166 16.0976 11.6834 16.0976 11.2929 15.7071L5.29289 9.70711C4.90237 9.31658 4.90237 8.68342 5.29289 8.29289Z" fill="#202020"/>
                    </svg>
                  </button>
                  <div id="pageDropdown" class="absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-md shadow-lg py-1 min-w-[200px] hidden z-50">
                  </div>
                </div>
                <div class="flex items-center gap-1 rounded-lg p-1">
                  <button class="p-1.5 text-gray-600 hover:text-gray-900 hover:bg-gray-50 active:bg-gray-100 focus:bg-gray-100 hover:text-gray-900 focus:text-gray-900 active:text-gray-900 rounded transition-colors border-none">
                    <svg class="w-5 h-5" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path fill-rule="evenodd" clip-rule="evenodd" d="M6.7587 2H17.2413C18.0463 1.99999 18.7106 1.99998 19.2518 2.04419C19.8139 2.09012 20.3306 2.18868 20.816 2.43597C21.5686 2.81947 22.1805 3.43139 22.564 4.18404C22.8113 4.66937 22.9099 5.18608 22.9558 5.74817C23 6.28936 23 6.95372 23 7.75868V13.2413C23 13.4904 23 13.726 22.9987 13.9486C22.9996 13.9656 23 13.9828 23 14C23 14.0221 22.9993 14.044 22.9979 14.0657C22.9942 14.5138 22.9839 14.9077 22.9558 15.2518C22.9099 15.8139 22.8113 16.3306 22.564 16.816C22.1805 17.5686 21.5686 18.1805 20.816 18.564C20.3306 18.8113 19.8139 18.9099 19.2518 18.9558C18.7106 19 18.0463 19 17.2413 19H6.75868C5.95372 19 5.28937 19 4.74818 18.9558C4.18608 18.9099 3.66937 18.8113 3.18404 18.564C2.43139 18.1805 1.81947 17.5686 1.43597 16.816C1.18868 16.3306 1.09012 15.8139 1.04419 15.2518C1.01608 14.9077 1.00585 14.5138 1.00212 14.0657C1.00072 14.044 1 14.0221 1 14C1 13.9828 1.00044 13.9656 1.0013 13.9486C0.999993 13.7259 0.999996 13.4904 1 13.2413V7.7587C0.999988 6.95373 0.999977 6.28937 1.04419 5.74817C1.09012 5.18608 1.18868 4.66937 1.43597 4.18404C1.81947 3.43139 2.43139 2.81947 3.18404 2.43597C3.66937 2.18868 4.18608 2.09012 4.74818 2.04419C5.28937 1.99998 5.95373 1.99999 6.7587 2ZM3.03086 15C3.03295 15.0302 3.03518 15.0599 3.03755 15.089C3.07337 15.5274 3.1383 15.7516 3.21799 15.908C3.40973 16.2843 3.7157 16.5903 4.09202 16.782C4.24842 16.8617 4.47262 16.9266 4.91104 16.9624C5.36113 16.9992 5.94342 17 6.8 17H17.2C18.0566 17 18.6389 16.9992 19.089 16.9624C19.5274 16.9266 19.7516 16.8617 19.908 16.782C20.2843 16.5903 20.5903 16.2843 20.782 15.908C20.8617 15.7516 20.9266 15.5274 20.9625 15.089C20.9648 15.0599 20.9671 15.0302 20.9691 15L3.03086 15ZM21 13L3 13V7.8C3 6.94342 3.00078 6.36113 3.03755 5.91104C3.07337 5.47262 3.1383 5.24842 3.21799 5.09202C3.40973 4.7157 3.7157 4.40973 4.09202 4.21799C4.24842 4.1383 4.47262 4.07337 4.91104 4.03755C5.36113 4.00078 5.94342 4 6.8 4H17.2C18.0566 4 18.6389 4.00078 19.089 4.03755C19.5274 4.07337 19.7516 4.1383 19.908 4.21799C20.2843 4.40973 20.5903 4.7157 20.782 5.09202C20.8617 5.24842 20.9266 5.47262 20.9625 5.91104C20.9992 6.36113 21 6.94342 21 7.8V13ZM7 21C7 20.4477 7.44772 20 8 20H16C16.5523 20 17 20.4477 17 21C17 21.5523 16.5523 22 16 22H8C7.44772 22 7 21.5523 7 21Z" fill="currentColor"/>
                    </svg>
                  </button>
                  <button class="p-1.5 text-gray-600 hover:text-gray-900 hover:bg-gray-50 active:bg-gray-100 focus:bg-gray-100 hover:text-gray-900 focus:text-gray-900 active:text-gray-900 rounded transition-colors  border-none">
                    <svg class="w-5 h-5" viewBox="0 0 16 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path fill-rule="evenodd" clip-rule="evenodd" d="M4.16146 8.6011e-07H11.8385C12.3657 -1.70213e-05 12.8205 -3.25181e-05 13.195 0.0305713C13.5904 0.0628723 13.9836 0.134188 14.362 0.326982C14.9265 0.614602 15.3854 1.07354 15.673 1.63803C15.8658 2.01641 15.9371 2.40963 15.9694 2.80497C16 3.17955 16 3.63432 16 4.16148V17.8385C16 18.3657 16 18.8205 15.9694 19.195C15.9371 19.5904 15.8658 19.9836 15.673 20.362C15.3854 20.9265 14.9265 21.3854 14.362 21.673C13.9836 21.8658 13.5904 21.9371 13.195 21.9694C12.8205 22 12.3657 22 11.8386 22H4.16144C3.6343 22 3.17954 22 2.80497 21.9694C2.40963 21.9371 2.01641 21.8658 1.63803 21.673C1.07354 21.3854 0.614603 20.9265 0.326982 20.362C0.134188 19.9836 0.0628722 19.5904 0.0305712 19.195C-3.25181e-05 18.8205 -1.70213e-05 18.3657 8.6011e-07 17.8385V4.16146C-1.70213e-05 3.63431 -3.25181e-05 3.17955 0.0305712 2.80497C0.0628723 2.40963 0.134188 2.01641 0.326982 1.63803C0.614602 1.07354 1.07354 0.614602 1.63803 0.326982C2.01641 0.134188 2.40963 0.0628723 2.80497 0.0305713C3.17955 -3.25181e-05 3.63431 -1.70213e-05 4.16146 8.6011e-07ZM2.96784 2.02393C2.69617 2.04613 2.59546 2.0838 2.54601 2.109C2.35785 2.20487 2.20487 2.35785 2.109 2.54601C2.0838 2.59546 2.04612 2.69617 2.02393 2.96784C2.00078 3.25118 2 3.62345 2 4.2V17.8C2 18.3766 2.00078 18.7488 2.02393 19.0322C2.04612 19.3038 2.0838 19.4046 2.109 19.454C2.20487 19.6422 2.35785 19.7951 2.54601 19.891C2.59546 19.9162 2.69617 19.9539 2.96784 19.9761C3.25117 19.9992 3.62345 20 4.2 20H11.8C12.3766 20 12.7488 19.9992 13.0322 19.9761C13.3038 19.9539 13.4045 19.9162 13.454 19.891C13.6422 19.7951 13.7951 19.6422 13.891 19.454C13.9162 19.4046 13.9539 19.3038 13.9761 19.0322C13.9992 18.7488 14 18.3766 14 17.8V4.2C14 3.62345 13.9992 3.25118 13.9761 2.96784C13.9539 2.69617 13.9162 2.59546 13.891 2.54601C13.7951 2.35785 13.6422 2.20487 13.454 2.109C13.4045 2.0838 13.3038 2.04613 13.0322 2.02393C12.7488 2.00078 12.3766 2 11.8 2H4.2C3.62345 2 3.25117 2.00078 2.96784 2.02393ZM6.5 16.5C6.5 15.6716 7.17157 15 8 15C8.82843 15 9.5 15.6716 9.5 16.5C9.5 17.3284 8.82843 18 8 18C7.17157 18 6.5 17.3284 6.5 16.5Z" fill="currentColor"/>
                    </svg>
                  </button>
                </div>
                <div class="flex items-center gap-2 bg-gray-100 px-3 py-1 rounded-md w-[800px] justify-between">
                  <div class="text-sm text-gray-600 font-mono">
                    <?php echo home_url(); ?>
                  </div>
                  <?php
                  // Only show "Connect your Domain" button if custom domain hasn't been set yet
                  $is_custom_domain = get_option('is_custom_domain', 0);
                  if ($is_custom_domain == 0):
                  ?>
                    <a href="<?php echo MAIN_WEBSITE_DOMAIN; ?>/customer/search_domain/" target="_blank" class="text-sm text-cyan-500 hover:text-cyan-600 font-medium">Connect your Domain</a>
                  <?php else: ?>
                    <!-- <span class="text-sm text-green-600 font-medium">✅ Custom Domain Active</span> -->
                  <?php endif; ?>
                </div>
              </div>
              <div class="flex items-center gap-3">
                <!-- <button class="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-50 rounded transition-colors">
                  <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd"/>
                  </svg>
                </button>
                <button class="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-50 rounded transition-colors">
                  <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"/>
                  </svg>
                </button> -->
                <button id="btn-save-customer-updates" class="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-50 rounded transition-colors focus:bg-gray-100 hover:text-gray-900 focus:text-gray-900 active:text-gray-900  border-none">
                  <svg class="w-6 h-6" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M0 15C0 6.71573 6.71573 0 15 0C23.2843 0 30 6.71573 30 15C30 23.2843 23.2843 30 15 30C6.71573 30 0 23.2843 0 15Z" fill="white"/>
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M18.405 7.02773C18.3167 7.00653 18.2113 7.0001 17.6745 7.0001H11V9.4001C11 9.69663 11.0008 9.85888 11.0103 9.97547C11.0107 9.98011 11.0111 9.9845 11.0114 9.98865C11.0156 9.98904 11.02 9.98942 11.0246 9.9898C11.1412 9.99932 11.3035 10.0001 11.6 10.0001H18.4C18.6965 10.0001 18.8588 9.99932 18.9754 9.9898C18.98 9.98942 18.9844 9.98904 18.9886 9.98865C18.9889 9.9845 18.9893 9.98011 18.9897 9.97547C18.9992 9.85888 19 9.69663 19 9.4001V7.41614C18.8118 7.23181 18.7524 7.18324 18.6941 7.14746C18.6046 7.09263 18.5071 7.05223 18.405 7.02773ZM20.707 6.29286L20.6444 6.23025C20.6246 6.21046 20.605 6.19081 20.5855 6.17131C20.298 5.88333 20.0446 5.62942 19.7391 5.44218C19.4707 5.2777 19.178 5.15648 18.8719 5.08299C18.5235 4.99934 18.1647 4.99967 17.7578 5.00005C17.7303 5.00008 17.7025 5.0001 17.6745 5.0001L10.7587 5.0001C10.5096 5.0001 10.2741 5.00009 10.0514 5.0014C10.0344 5.00054 10.0172 5.0001 10 5.0001C9.97793 5.0001 9.95603 5.00082 9.93432 5.00222C9.48616 5.00595 9.09227 5.01618 8.74817 5.0443C8.18608 5.09022 7.66937 5.18878 7.18404 5.43608C6.43139 5.81957 5.81947 6.43149 5.43597 7.18414C5.18868 7.66948 5.09012 8.18618 5.04419 8.74828C4.99998 9.28947 4.99999 9.95383 5 10.7588V19.2414C4.99999 20.0464 4.99998 20.7107 5.04419 21.2519C5.09012 21.814 5.18868 22.3307 5.43597 22.8161C5.81947 23.5687 6.43139 24.1806 7.18404 24.5641C7.66937 24.8114 8.18608 24.91 8.74817 24.9559C9.09227 24.984 9.48616 24.9943 9.93431 24.998C9.95603 24.9994 9.97793 25.0001 10 25.0001C10.0172 25.0001 10.0344 24.9997 10.0514 24.9988C10.274 25.0001 10.5096 25.0001 10.7587 25.0001H19.2413C19.4904 25.0001 19.726 25.0001 19.9486 24.9988C19.9656 24.9997 19.9828 25.0001 20 25.0001C20.0221 25.0001 20.044 24.9994 20.0657 24.998C20.5138 24.9943 20.9077 24.984 21.2518 24.9559C21.8139 24.91 22.3306 24.8114 22.816 24.5641C23.5686 24.1806 24.1805 23.5687 24.564 22.8161C24.8113 22.3307 24.9099 21.814 24.9558 21.2519C25 20.7107 25 20.0464 25 19.2414V12.3256C25 12.2976 25 12.2698 25.0001 12.2423C25.0004 11.8354 25.0008 11.4766 24.9171 11.1282C24.8436 10.8221 24.7224 10.5294 24.5579 10.261C24.3707 9.9555 24.1168 9.70206 23.8288 9.41461C23.8093 9.39515 23.7896 9.37554 23.7698 9.35574L20.707 6.29286C20.7071 6.29295 20.7069 6.29277 20.707 6.29286ZM21 9.41432V9.43198C21 9.68437 21.0001 9.93017 20.9831 10.1383C20.9644 10.3669 20.9203 10.6367 20.782 10.9081C20.5903 11.2844 20.2843 11.5904 19.908 11.7821C19.6366 11.9204 19.3668 11.9645 19.1382 11.9832C18.9301 12.0002 18.6843 12.0001 18.4319 12.0001L11.6 12.0001C11.5894 12.0001 11.5787 12.0001 11.5681 12.0001C11.3157 12.0001 11.0699 12.0002 10.8618 11.9832C10.6332 11.9645 10.3634 11.9204 10.092 11.7821C9.7157 11.5904 9.40973 11.2844 9.21799 10.9081C9.07969 10.6367 9.03562 10.3669 9.01695 10.1383C8.99994 9.93017 8.99997 9.68438 9 9.432C9 9.42138 9 9.41074 9 9.4001V7.03096C8.96977 7.03305 8.94013 7.03528 8.91104 7.03765C8.47262 7.07347 8.24842 7.1384 8.09202 7.21809C7.7157 7.40984 7.40973 7.7158 7.21799 8.09212C7.1383 8.24852 7.07337 8.47272 7.03755 8.91114C7.00078 9.36123 7 9.94352 7 10.8001V19.2001C7 20.0567 7.00078 20.639 7.03755 21.0891C7.07337 21.5275 7.1383 21.7517 7.21799 21.9081C7.40973 22.2844 7.7157 22.5904 8.09202 22.7821C8.24842 22.8618 8.47262 22.9267 8.91104 22.9625C8.94013 22.9649 8.96977 22.9672 9 22.9692L9 17.5682C8.99997 17.3158 8.99994 17.07 9.01695 16.8619C9.03562 16.6333 9.07969 16.3635 9.21799 16.0921C9.40974 15.7158 9.7157 15.4098 10.092 15.2181C10.3634 15.0798 10.6332 15.0357 10.8618 15.017C11.0699 15 11.3157 15.0001 11.5681 15.0001H18.4319C18.6843 15.0001 18.9301 15 19.1382 15.017C19.3668 15.0357 19.6366 15.0798 19.908 15.2181C20.2843 15.4098 20.5903 15.7158 20.782 16.0921C20.9203 16.3635 20.9644 16.6333 20.9831 16.8619C21.0001 17.07 21 17.3158 21 17.5682L21 22.9692C21.0302 22.9672 21.0599 22.9649 21.089 22.9625C21.5274 22.9267 21.7516 22.8618 21.908 22.7821C22.2843 22.5904 22.5903 22.2844 22.782 21.9081C22.8617 21.7517 22.9266 21.5275 22.9624 21.0891C22.9992 20.639 23 20.0567 23 19.2001V12.3256C23 11.7888 22.9936 11.6834 22.9724 11.5951C22.9479 11.493 22.9075 11.3955 22.8526 11.306C22.8052 11.2286 22.7352 11.1495 22.3556 10.77L21 9.41432ZM19 23.0001V17.6001C19 17.3036 18.9992 17.1413 18.9897 17.0247C18.9893 17.0201 18.9889 17.0157 18.9886 17.0116C18.9844 17.0112 18.98 17.0108 18.9754 17.0104C18.8588 17.0009 18.6965 17.0001 18.4 17.0001H11.6C11.3035 17.0001 11.1412 17.0009 11.0246 17.0104C11.02 17.0108 11.0156 17.0112 11.0114 17.0116C11.0111 17.0157 11.0107 17.0201 11.0103 17.0247C11.0008 17.1413 11 17.3036 11 17.6001V23.0001H19Z" fill="#202020"/>
                  </svg>
                </button>
                
                <!-- Always show Preview and Publish buttons for customer edit plugin -->
                <button id="btn-preview-customer-updates"  type="button" class="text-gray-600 border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50 hover:text-gray-700 font-medium transition-colors">Preview</button>
                <button id="btn-publish-customer-updates"  type="button"  class="bg-emerald-500 text-white px-4 py-2 rounded-md hover:bg-emerald-600 hover:border-none border-none font-medium transition-colors">Publish</button>
              
              </div>
            </div>

          </div>
          <div class="px-0 h-100" id="main-sidebar">
            <div class="h-100 w-100">
              <div id="mainbtn" class="flex flex-col items-center py-6 px-3 bg-white h-full border-r border-gray-200">

                <!-- Site Content -->
                <div class="flex flex-col items-center mb-8 cursor-pointer group" id="openNavSC" onclick="sidebar_opensc()">
                  <div class="w-12 h-12 border border-gray-300 rounded-full flex items-center justify-center mb-2 group-hover:bg-brand-main group-hover:border-brand-main transition-all duration-200">
                    <svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path fill-rule="evenodd" clip-rule="evenodd" d="M12.3026 5.52051L12.3026 5.52052L12.3026 5.52051ZM12.5 5.6181L20.2639 9.50006L17.0618 11.1011C17.0553 11.1043 17.0488 11.1075 17.0424 11.1108L12.5 13.382L7.95763 11.1108C7.95118 11.1075 7.94469 11.1043 7.93817 11.1011L4.73607 9.50006L12.5 5.6181ZM5.26393 12.0001L2.05279 10.3945C1.714 10.2251 1.5 9.87884 1.5 9.50006C1.5 9.12129 1.714 8.77503 2.05279 8.60564L11.695 3.78452C11.7024 3.78082 11.711 3.77646 11.7206 3.77155C11.8139 3.72394 12.007 3.62541 12.2234 3.5848C12.4062 3.55051 12.5938 3.55051 12.7766 3.5848C12.993 3.62541 13.1861 3.72394 13.2794 3.77155C13.289 3.77646 13.2976 3.78082 13.305 3.78452L22.9472 8.60564C23.286 8.77503 23.5 9.12129 23.5 9.50006C23.5 9.87884 23.286 10.2251 22.9472 10.3945L19.7361 12.0001L22.9472 13.6056C23.286 13.775 23.5 14.1213 23.5 14.5001C23.5 14.8788 23.286 15.2251 22.9472 15.3945L13.305 20.2156C13.2976 20.2193 13.289 20.2237 13.2794 20.2286C13.1861 20.2762 12.993 20.3747 12.7766 20.4153C12.5938 20.4496 12.4062 20.4496 12.2234 20.4153C12.007 20.3747 11.8139 20.2762 11.7206 20.2286C11.711 20.2237 11.7024 20.2193 11.695 20.2156L2.05279 15.3945C1.714 15.2251 1.5 14.8788 1.5 14.5001C1.5 14.1213 1.714 13.775 2.05279 13.6056L5.26393 12.0001ZM7.5 13.1181L4.73607 14.5001L12.5 18.382L20.2639 14.5001L17.5 13.1181L13.305 15.2156C13.2976 15.2193 13.289 15.2237 13.2794 15.2286C13.1861 15.2762 12.993 15.3747 12.7766 15.4153C12.5938 15.4496 12.4062 15.4496 12.2234 15.4153C12.007 15.3747 11.8139 15.2762 11.7206 15.2286C11.711 15.2237 11.7024 15.2193 11.695 15.2156L7.5 13.1181ZM12.3026 18.4796C12.3024 18.4797 12.3024 18.4797 12.3026 18.4796L12.3026 18.4796ZM12.6974 18.4796C12.6976 18.4797 12.6976 18.4797 12.6974 18.4796L12.6974 18.4796ZM12.3026 13.4796C12.3024 13.4797 12.3024 13.4797 12.3026 13.4796L12.3026 13.4796ZM12.6974 13.4796C12.6976 13.4797 12.6976 13.4797 12.6974 13.4796L12.6974 13.4796ZM12.6974 5.52052L12.6974 5.52051L12.6974 5.52052Z" fill="#202020"/>
                    </svg>
                  </div>
                  <span class="text-xs text-gray-600 text-center leading-tight">Site Content</span>
                </div>

                <!-- Site Theme -->
                <div class="flex flex-col items-center mb-8 cursor-pointer group" id="openSiteTheme" onclick="openSiteTheme()">
                  <div class="w-12 h-12 border border-gray-300 rounded-full flex items-center justify-center mb-2 group-hover:bg-brand-main group-hover:border-brand-main transition-all duration-200">
                    <svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M12.5 2C6.97715 2 2.5 6.47715 2.5 12C2.5 17.5228 6.97715 22 12.5 22C18.0228 22 22.5 17.5228 22.5 12C22.5 6.47715 18.0228 2 12.5 2ZM12.5 4C16.9183 4 20.5 7.58172 20.5 12C20.5 16.4183 16.9183 20 12.5 20C8.08172 20 4.5 16.4183 4.5 12C4.5 7.58172 8.08172 4 12.5 4ZM12.5 6C9.18629 6 6.5 8.68629 6.5 12C6.5 15.3137 9.18629 18 12.5 18V6Z" fill="#202020"/>
                    </svg>
                  </div>
                  <span class="text-xs text-gray-600 text-center leading-tight">Site Theme</span>
                </div>

                <!-- Color Theme -->
                <div class="flex flex-col items-center mb-8 cursor-pointer group" id="openColorTheme" onclick="openColorTheme()">
                  <div class="w-12 h-12 border border-gray-300 rounded-full flex items-center justify-center mb-2 group-hover:bg-brand-main group-hover:border-brand-main transition-all duration-200">
                    <svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M12.505 22.99C10.5761 22.99 9.00694 21.4207 9.00694 19.4916V15.9932C7.36785 15.9932 6.49833 15.9732 5.68878 15.5534C4.92921 15.1736 4.32954 14.5638 3.93976 13.8042C3.5 12.9446 3.5 12.025 3.5 10.1858V3.59882C3.5 2.94911 3.5 2.51931 3.71988 2.0895C3.90977 1.70968 4.2196 1.40981 4.58939 1.2199C5.01916 1 5.44892 1 6.09856 1H18.9014C19.5511 1 19.9808 1 20.4106 1.2199C20.7904 1.40981 21.0902 1.71967 21.2801 2.0895C21.5 2.51931 21.5 2.94911 21.5 3.59882V10.1958C21.5 12.035 21.5 12.9546 21.0602 13.8142C20.6805 14.5738 20.0708 15.1736 19.3112 15.5634C18.5017 15.9732 17.6421 15.9932 15.9931 16.0032V19.5016C15.9931 21.4307 14.4239 23 12.495 23L12.505 22.99ZM11.0058 15.9932V19.4916C11.0058 20.3212 11.6755 20.9909 12.505 20.9909C13.3345 20.9909 14.0042 20.3212 14.0042 19.4916V15.9932H11.0058ZM15.0036 13.9941H15.7032C17.1724 13.9941 17.982 13.9941 18.4117 13.7742C18.7915 13.5843 19.0913 13.2844 19.2812 12.9046C19.4611 12.5647 19.4911 11.975 19.5011 10.9955H5.50888C5.50888 11.975 5.54886 12.5547 5.72876 12.9046C5.91866 13.2844 6.22848 13.5843 6.59828 13.7742C7.02804 13.9941 7.83759 13.9941 9.30677 13.9941H15.0036ZM5.50888 8.99637H19.5011V3.59882C19.5011 3.31895 19.5011 3.02908 19.4811 2.94911C19.4711 2.99909 19.1813 2.99909 18.9014 2.99909H6.10855C5.82871 2.99909 5.53887 2.99909 5.45891 3.01908C5.50888 3.02908 5.50888 3.31895 5.50888 3.59882V8.99637Z" fill="#202020"/>
                    </svg>
                  </div>
                  <span class="text-xs text-gray-600 text-center leading-tight">Color Theme</span>
                </div>

                <!-- Advanced Settings -->
                <div class="flex flex-col items-center mb-8 cursor-pointer group" id="openAdvanced" onclick="openAdvanced()">
                  <div class="w-12 h-12 border border-gray-300 rounded-full flex items-center justify-center mb-2 group-hover:bg-brand-main group-hover:border-brand-main transition-all duration-200">
                    <svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path fill-rule="evenodd" clip-rule="evenodd" d="M18.5 6C17.3954 6 16.5 6.89543 16.5 8C16.5 9.10457 17.3954 10 18.5 10C19.6046 10 20.5 9.10457 20.5 8C20.5 6.89543 19.6046 6 18.5 6ZM14.626 7C15.0701 5.27477 16.6362 4 18.5 4C20.7091 4 22.5 5.79086 22.5 8C22.5 10.2091 20.7091 12 18.5 12C16.6362 12 15.0701 10.7252 14.626 9L3.5 9C2.94772 9 2.5 8.55228 2.5 8C2.5 7.44772 2.94772 7 3.5 7L14.626 7ZM6.5 14C5.39543 14 4.5 14.8954 4.5 16C4.5 17.1046 5.39543 18 6.5 18C7.60457 18 8.5 17.1046 8.5 16C8.5 14.8954 7.60457 14 6.5 14ZM2.5 16C2.5 13.7909 4.29086 12 6.5 12C8.36384 12 9.92994 13.2748 10.374 15L21.5 15C22.0523 15 22.5 15.4477 22.5 16C22.5 16.5523 22.0523 17 21.5 17L10.374 17C9.92994 18.7252 8.36384 20 6.5 20C4.29086 20 2.5 18.2091 2.5 16Z" fill="#202020"/>
                    </svg>
                  </div>
                  <span class="text-xs text-gray-600 text-center leading-tight">Advanced Settings</span>
                </div>

                <!-- SEO Settings -->
                <div class="flex flex-col items-center mb-8 cursor-pointer group" id="openSEO" onclick="openSEO()">
                  <div class="w-12 h-12 border border-gray-300 rounded-full flex items-center justify-center mb-2 group-hover:bg-brand-main group-hover:border-brand-main transition-all duration-200">
                    <svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path fill-rule="evenodd" clip-rule="evenodd" d="M12.5 3C7.52944 3 3.5 7.02944 3.5 12C3.5 16.9706 7.52944 21 12.5 21C17.4706 21 21.5 16.9706 21.5 12C21.5 7.02944 17.4706 3 12.5 3ZM1.5 12C1.5 5.92487 6.42487 1 12.5 1C18.5751 1 23.5 5.92487 23.5 12C23.5 18.0751 18.5751 23 12.5 23C6.42487 23 1.5 18.0751 1.5 12ZM19.2071 9.29289C19.5976 9.68342 19.5976 10.3166 19.2071 10.7071L15.7728 14.1414C15.7674 14.1468 15.762 14.1523 15.7564 14.1578C15.6722 14.2421 15.5717 14.3427 15.4758 14.4241C15.3648 14.5183 15.1962 14.6439 14.9635 14.7195C14.6623 14.8174 14.3377 14.8174 14.0365 14.7195C13.8038 14.6439 13.6352 14.5183 13.5242 14.4241C13.4283 14.3427 13.3278 14.2421 13.2436 14.1578C13.238 14.1523 13.2326 14.1468 13.2272 14.1414L10.5 11.4142L7.20711 14.7071C6.81658 15.0976 6.18342 15.0976 5.79289 14.7071C5.40237 14.3166 5.40237 13.6834 5.79289 13.2929L9.22721 9.85858C9.23259 9.8532 9.23804 9.84774 9.24357 9.84221C9.32776 9.75795 9.42832 9.65732 9.52417 9.57595C9.63524 9.48165 9.80384 9.35611 10.0365 9.28052C10.3377 9.18264 10.6623 9.18264 10.9635 9.28052C11.1962 9.35611 11.3648 9.48166 11.4758 9.57595C11.5717 9.65732 11.6722 9.75795 11.7564 9.84221C11.762 9.84774 11.7674 9.8532 11.7728 9.85858L14.5 12.5858L17.7929 9.29289C18.1834 8.90237 18.8166 8.90237 19.2071 9.29289Z" fill="#202020"/>
                    </svg>
                  </div>
                  <span class="text-xs text-gray-600 text-center leading-tight">SEO Settings</span>
                </div>
              </div>
            </div>
            <div class="w3-bar-block w3-card w3-animate-left" style="display:block" id="mySidebar">
              <!-- Header - Fixed at top -->
              <div class="sidebar-header px-2 py-2">
                <div class="flex items-center justify-between">
                  <h3 class="text-lg font-semibold text-gray-900">Site content</h3>
                  <button onclick="closeAllSidebars()" class="text-gray-400 hover:text-gray-600 border-none hover:bg-gray-100 rounded-full p-1">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                  </button>
                </div>
              </div>


              <!-- <button class="w3-bar-item w3-button w3-large btn-custom" onclick="sidebar_close()">&times;</button>
              <p class="title-fr">Site content</p> -->
              <!-- <div class="d-flex text-center mt-4 btn-search mb-3"><i class="fa fa-search"></i><input type="text" placeholder="Search tagged field.."></div> -->
              <!-- <p class="fw-semibold sub-title">Current Page</p> -->
              <!-- OLD BACKUP CODE (COMMENTED OUT)
              <div class="content-form" id="tagged_fields_old_backup">
                <?php
                    // OLD ACCORDION LAYOUT - KEPT AS BACKUP
                    /*
                    global $post;
                    if (!$post) return;

                    $fields = get_post_meta($post->ID, '_designer_editable_fields', true);

                    if (!empty($fields)) {
                        // Gom nhóm các tag theo section
                        $grouped_fields = [];
                        foreach ($fields as $i => $f) {
                            $section_key = !empty($f['section']) ? $f['section'] : 'section1';
                            if (!isset($grouped_fields[$section_key])) {
                                $grouped_fields[$section_key] = [];
                            }
                            $f['index'] = $i; // Lưu index gốc để xóa đúng
                            $grouped_fields[$section_key][] = $f;
                        }

                        // Hiển thị các tag theo nhóm section
                        foreach ($grouped_fields as $section_key => $section_fields) {
                            $section_name = get_section_name_by_key($section_key, $post->ID);
                            ?>
                            <div class="section-header">
                                <h4><?php echo esc_html($section_name); ?></h4>
                            </div>
                    */
                ?>
              -->

              <!-- NEW DESIGN - SITE CONTENT TAGS -->
              <div class="content-form" id="tagged_fields">
                <?php
                    global $post;
                    if (!$post) return;

                    $fields = get_post_meta($post->ID, '_designer_editable_fields', true);
                    $customer_fields = get_post_meta($post->ID, '_customer_editable_fields', true);

                    // Merge customer edits with designer fields
                    if (!empty($fields)) {
                        // Create lookup array for customer edits
                        $customer_lookup = [];
                        if (!empty($customer_fields)) {
                            foreach ($customer_fields as $customer_field) {
                                $key = $customer_field['selector'] . '|' . $customer_field['field_type'] . '|' . (isset($customer_field['field_subtype']) ? $customer_field['field_subtype'] : '');
                                $customer_lookup[$key] = $customer_field;
                            }
                        }
                        // Group tags by section
                        $grouped_fields = [];
                        foreach ($fields as $i => $f) {
                            $section_key = !empty($f['section']) ? $f['section'] : 'section1';
                            if (!isset($grouped_fields[$section_key])) {
                                $grouped_fields[$section_key] = [];
                            }
                            $f['index'] = $i;
                            $grouped_fields[$section_key][] = $f;
                        }

                        // Display sections with new design
                        foreach ($grouped_fields as $section_key => $section_fields) {
                            $section_name = get_section_name_by_key($section_key, $post->ID);
                            ?>
                            <!-- Collapsible Section -->
                            <div class="section-container mb-4">
                                <div class="section-header-new cursor-pointer flex items-center justify-between p-2 transition-colors" onclick="toggleSection('<?php echo esc_attr($section_key); ?>')">
                                    <div class="flex items-center">
                                        <svg class="w-5 h-5 mr-2 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                        </svg>
                                        <h4 class="text-sm font-medium text-gray-800 mb-0"><?php echo esc_html($section_name); ?></h4>
                                    </div>
                                    <svg class="w-6 h-6 transform transition-transform section-arrow" data-section="<?php echo esc_attr($section_key); ?>" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M5.29289 8.29289C5.68342 7.90237 6.31658 7.90237 6.70711 8.29289L12 13.5858L17.2929 8.29289C17.6834 7.90237 18.3166 7.90237 18.7071 8.29289C19.0976 8.68342 19.0976 9.31658 18.7071 9.70711L12.7071 15.7071C12.3166 16.0976 11.6834 16.0976 11.2929 15.7071L5.29289 9.70711C4.90237 9.31658 4.90237 8.68342 5.29289 8.29289Z" fill="#202020"/>
                                    </svg>
                                </div>

                                <!-- Section Content -->
                                <div class="section-content mt-2 space-y-3 bg-gray-100 p-2" id="section-<?php echo esc_attr($section_key); ?>" style="display: block;">
                                    <?php foreach ($section_fields as $f):
                                        // Get customer value if exists
                                        $customer_key = $f['selector'] . '|' . $f['type'] . '|';
                                        $customer_data = isset($customer_lookup[$customer_key]) ? $customer_lookup[$customer_key] : null;
                                        $display_value = $customer_data ? $customer_data['value'] : (isset($f['value']) ? $f['value'] : '');
                                        ?>
                                        <!-- Tagging Item Card -->
                                        <div class="tagging-item bg-gray-100" data-selector="<?php echo esc_attr($f['selector']); ?>" data-index="<?php echo $f['index']; ?>">
                                            <!-- Item Title -->
                                            <div class="item-title bg-gray-200 py-2 mb-2 px-2 rounded-md">
                                                <h5 class="text-sm font-medium text-gray-800 mb-0">
                                                    <?php echo esc_html($f['label']); ?>
                                                </h5>
                                            </div>

                                            <!-- Item Tooltip -->
                                            <div class="item-tooltip mb-3">
                                                <p class="text-xs text-gray-600 mb-0">Please add your <?php echo strtolower(esc_html($f['label'])); ?> <?php echo $f['type'] === 'image' || $f['type'] === 'icon' ? '' : 'text'; ?></p>
                                            </div>

                                            <!-- Item Value Field -->
                                            <div class="item-value">
                                                <?php
                                                switch($f['type']){
                                                    case 'text':
                                                        ?>
                                                        <div class="value-label text-xs font-medium text-gray-700 mb-1">Value</div>
                                                        <input type="text"
                                                               value="<?php echo esc_attr($display_value); ?>"
                                                               placeholder="Add your <?php echo strtolower(esc_html($f['label'])); ?> text"
                                                               class="customer-editable-field w-full px-3 py-2 bg-blue-50 border border-blue-200 !rounded-md text-sm text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                                               data-field-type="text"
                                                               data-selector="<?php echo esc_attr($f['selector']); ?>">
                                                        <?php
                                                        break;

                                                    case 'image':
                                                        // Get customer value for image
                                                        $customer_image_key = $f['selector'] . '|image|';
                                                        $customer_image_data = isset($customer_lookup[$customer_image_key]) ? $customer_lookup[$customer_image_key] : null;
                                                        $display_image_value = $customer_image_data ? $customer_image_data['value'] : (isset($f['value']) ? $f['value'] : '');
                                                        ?>
                                                        <div class="image-upload-area border-2 border-dashed border-gray-300 rounded-lg p-6 text-center bg-gray-50 hover:bg-gray-100 transition-colors"
                                                             onclick="openWordPressMediaLibrary(this)">
                                                            <div class="upload-icon mb-3">
                                                                <svg class="w-8 h-8 mx-auto text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                                                </svg>
                                                            </div>
                                                            <p class="text-sm text-gray-600 mb-1">Drag & Drop or <span class="text-blue-500 hover:text-blue-600 cursor-pointer">Choose file</span> to upload</p>
                                                            <p class="text-xs text-gray-500">JPG, GIF or PNG. Max size of 800K</p>
                                                            <input type="text"
                                                                   value="<?php echo esc_attr($display_image_value); ?>"
                                                                   placeholder="Or paste image URL here"
                                                                   class="customer-editable-field w-full mt-3 px-3 py-2 bg-white border border-gray-300 !rounded-md text-sm text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                                                   data-field-type="image"
                                                                   data-selector="<?php echo esc_attr($f['selector']); ?>"
                                                                   onclick="event.stopPropagation();">
                                                            <!-- Hidden file input for drag & drop -->
                                                            <input type="file"
                                                                   class="hidden-file-input"
                                                                   accept="image/*"
                                                                   style="display: none;"
                                                                   onchange="handleFileUpload(this)">
                                                        </div>
                                                        <?php
                                                        break;

                                                    case 'button':
                                                        $additional = json_decode(isset($f['additional_data']) ? $f['additional_data'] : '{}', true);
                                                        $button_url = isset($additional['url']) ? $additional['url'] : '';

                                                        // Get customer values for button text and URL
                                                        $customer_text_key = $f['selector'] . '|button|label';
                                                        $customer_url_key = $f['selector'] . '|button|url';
                                                        $customer_text_data = isset($customer_lookup[$customer_text_key]) ? $customer_lookup[$customer_text_key] : null;
                                                        $customer_url_data = isset($customer_lookup[$customer_url_key]) ? $customer_lookup[$customer_url_key] : null;

                                                        $display_text = $customer_text_data ? $customer_text_data['value'] : (isset($f['value']) ? $f['value'] : '');
                                                        $display_url = $customer_url_data ? $customer_url_data['value'] : $button_url;
                                                        ?>
                                                        <div class="space-y-3">
                                                            <div>
                                                                <div class="value-label text-xs font-medium text-gray-700 mb-1">Button Text</div>
                                                                <input type="text"
                                                                       value="<?php echo esc_attr($display_text); ?>"
                                                                       placeholder="Add your button text"
                                                                       class="customer-editable-field w-full px-3 py-2 bg-blue-50 border border-blue-200 !rounded-md text-sm text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                                                       data-field-type="button"
                                                                       data-field-subtype="label"
                                                                       data-selector="<?php echo esc_attr($f['selector']); ?>">
                                                            </div>
                                                            <div>
                                                                <div class="value-label text-xs font-medium text-gray-700 mb-1">Button URL</div>
                                                                <input type="text"
                                                                       value="<?php echo esc_attr($display_url); ?>"
                                                                       placeholder="Add your button URL"
                                                                       class="customer-editable-field w-full px-3 py-2 bg-blue-50 border border-blue-200 !rounded-md text-sm text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                                                       data-field-type="button"
                                                                       data-field-subtype="url"
                                                                       data-selector="<?php echo esc_attr($f['selector']); ?>">
                                                            </div>
                                                        </div>
                                                        <?php
                                                        break;

                                                    case 'link':
                                                        $additional = json_decode(isset($f['additional_data']) ? $f['additional_data'] : '{}', true);
                                                        $link_url = isset($additional['url']) ? $additional['url'] : '';

                                                        // Get customer values for link text and URL
                                                        $customer_text_key = $f['selector'] . '|link|label';
                                                        $customer_url_key = $f['selector'] . '|link|url';
                                                        $customer_text_data = isset($customer_lookup[$customer_text_key]) ? $customer_lookup[$customer_text_key] : null;
                                                        $customer_url_data = isset($customer_lookup[$customer_url_key]) ? $customer_lookup[$customer_url_key] : null;

                                                        $display_text = $customer_text_data ? $customer_text_data['value'] : (isset($f['value']) ? $f['value'] : '');
                                                        $display_url = $customer_url_data ? $customer_url_data['value'] : $link_url;
                                                        ?>
                                                        <div class="space-y-3">
                                                            <div>
                                                                <div class="value-label text-xs font-medium text-gray-700 mb-1">Link Text</div>
                                                                <input type="text"
                                                                       value="<?php echo esc_attr($display_text); ?>"
                                                                       placeholder="Add your link text"
                                                                       class="customer-editable-field w-full px-3 py-2 bg-blue-50 border border-blue-200 !rounded-md text-sm text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                                                       data-field-type="link"
                                                                       data-field-subtype="label"
                                                                       data-selector="<?php echo esc_attr($f['selector']); ?>">
                                                            </div>
                                                            <div>
                                                                <div class="value-label text-xs font-medium text-gray-700 mb-1">Link URL</div>
                                                                <input type="text"
                                                                       value="<?php echo esc_attr($display_url); ?>"
                                                                       placeholder="Add your link URL"
                                                                       class="customer-editable-field w-full px-3 py-2 bg-blue-50 border border-blue-200 !rounded-md text-sm text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                                                       data-field-type="link"
                                                                       data-field-subtype="url"
                                                                       data-selector="<?php echo esc_attr($f['selector']); ?>">
                                                            </div>
                                                        </div>
                                                        <?php
                                                        break;

                                                    case 'iframe':
                                                        // Get customer value for iframe
                                                        $customer_iframe_key = $f['selector'] . '|iframe|';
                                                        $customer_iframe_data = isset($customer_lookup[$customer_iframe_key]) ? $customer_lookup[$customer_iframe_key] : null;
                                                        $display_iframe_value = $customer_iframe_data ? $customer_iframe_data['value'] : (isset($f['value']) ? $f['value'] : '');
                                                        ?>
                                                        <div class="value-label text-xs font-medium text-gray-700 mb-1">Iframe URL</div>
                                                        <input type="text"
                                                               value="<?php echo esc_attr($display_iframe_value); ?>"
                                                               placeholder="Add your iframe URL"
                                                               class="customer-editable-field w-full px-3 py-2 bg-blue-50 border border-blue-200 !rounded-md text-sm text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                                               data-field-type="iframe"
                                                               data-selector="<?php echo esc_attr($f['selector']); ?>">
                                                        <?php
                                                        break;

                                                    case 'video':
                                                        // Get customer value for video
                                                        $customer_video_key = $f['selector'] . '|video|';
                                                        $customer_video_data = isset($customer_lookup[$customer_video_key]) ? $customer_lookup[$customer_video_key] : null;
                                                        $display_video_value = $customer_video_data ? $customer_video_data['value'] : (isset($f['value']) ? $f['value'] : '');
                                                        ?>
                                                        <div class="value-label text-xs font-medium text-gray-700 mb-1">Video URL</div>
                                                        <input type="text"
                                                               value="<?php echo esc_attr($display_video_value); ?>"
                                                               placeholder="Add your video URL"
                                                               class="customer-editable-field w-full px-3 py-2 bg-blue-50 border border-blue-200 !rounded-md text-sm text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                                               data-field-type="video"
                                                               data-selector="<?php echo esc_attr($f['selector']); ?>">
                                                        <?php
                                                        break;

                                                    case 'icon':
                                                        // Get customer value for icon
                                                        $customer_icon_key = $f['selector'] . '|icon|';
                                                        $customer_icon_data = isset($customer_lookup[$customer_icon_key]) ? $customer_lookup[$customer_icon_key] : null;
                                                        $display_icon_value = $customer_icon_data ? $customer_icon_data['value'] : (isset($f['value']) ? $f['value'] : '');
                                                        ?>
                                                        <div class="icon-upload-area">
                                                            <div class="value-label text-xs font-medium text-gray-700 mb-1">Icon</div>
                                                            <div class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center bg-gray-50 hover:bg-gray-100 transition-colors">
                                                                <div class="upload-icon mb-2">
                                                                    <svg class="w-6 h-6 mx-auto text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                                                    </svg>
                                                                </div>
                                                                <p class="text-xs text-gray-600 mb-1">Upload icon or <span class="text-blue-500 hover:text-blue-600 cursor-pointer">Choose file</span></p>
                                                                <p class="text-xs text-gray-500">SVG, PNG or JPG</p>
                                                                <input type="text"
                                                                       value="<?php echo esc_attr($display_icon_value); ?>"
                                                                       placeholder="Or paste icon URL/SVG code here"
                                                                       class="customer-editable-field w-full mt-2 px-3 py-2 bg-white border border-gray-300 rounded-md text-sm text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                                                       data-field-type="icon"
                                                                       data-selector="<?php echo esc_attr($f['selector']); ?>">
                                                            </div>
                                                        </div>
                                                        <?php
                                                        break;
                                      case 'video':
                                        ?>
                                          <div class="form-group">
                                              <label for="tooltip">Value URL:</label>
                                              <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> URL">
                                          </div>
                                        <?php
                                        break;
                                      case 'iframe':
                                        ?>
                                          <div class="form-group">
                                              <label for="title">URL:</label>
                                              <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> URL">
                                          </div>
                                        <?php
                                        break;
                                      case 'progress':
                                        $percent = isset($f['value']) ? intval($f['value']) : 0;
                                        ?>
                                          <div class="form-group">
                                              <label for="title">Label:</label>
                                              <input type="text" readonly value="<?php echo isset($f['label']) ? esc_attr($f['label']) : ''; ?>">
                                          </div>
                                          <div class="form-group">
                                              <label for="title">Percentage:</label>
                                              <input type="text" readonly value="<?php echo $percent; ?>%">
                                          </div>
                                          
                                        <?php
                                        break;
                                      case 'default':
                                        ?>
                                          <div class="form-group">
                                              <label for="title">Value:</label>
                                              <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> value">
                                          </div>
                                        <?php
                                        break;

                                                    default:
                                                        ?>
                                                        <div class="value-label text-xs font-medium text-gray-700 mb-1">Value</div>
                                                        <input type="text"
                                                               value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>"
                                                               placeholder="Add your <?php echo strtolower(esc_html($f['label'])); ?> value"
                                                               class="customer-editable-field w-full px-3 py-2 bg-blue-50 border border-blue-200 !rounded-md text-sm text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                                               data-field-type="<?php echo esc_attr($f['type']); ?>"
                                                               data-selector="<?php echo esc_attr($f['selector']); ?>">
                                                        <?php
                                                        break;
                                                }
                                                ?>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                            <?php
                        }
                    } else {
                        ?>
                        <div class="no-tags-message text-center py-8">
                            <svg class="w-12 h-12 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.99 1.99 0 013 12V7a2 2 0 012-2z"></path>
                            </svg>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">No Tags Found</h3>
                            <p class="text-gray-500">No editable content has been tagged on this page yet.</p>
                        </div>
                        <?php
                    }
                ?>
              </div>
            </div>

            <!-- Page List Sidebar -->
            <div class="w3-bar-block w3-card w3-animate-left" style="display:none" id="pageSectionSidebar">
              <!-- Header with close button -->
              <div class="d-flex justify-content-between align-items-center p-2 border-bottom">
                <h5 class="mb-0 fw-semibold">Site Page</h5>
                <button class="btn btn-sm btn-outline-secondary" onclick="pageSection_close()" style="width: 30px; height: 30px; padding: 0; border-radius: 4px;">
                  <i class="fa fa-times"></i>
                </button>
              </div>

              <!-- Search box -->
              <div class="p-2 border-bottom">
                <div class="position-relative">
                  <i class="fa fa-search position-absolute" style="left: 12px; top: 50%; transform: translateY(-50%); color: #999; font-size: 14px;"></i>
                  <input type="text" id="page-search" class="form-control ps-5" placeholder="search all pages...." style="border-radius: 20px; border: 1px solid #ddd; font-size: 14px; padding: 8px 15px 8px 35px;">
                </div>
              </div>

              <!-- Page list container -->
              <div class="flex-grow-1" id="page_list_container" style="max-height: calc(100vh - 200px); overflow-y: auto; position: relative;">
                <!-- Page list will be loaded here -->
                <div class="text-center py-4">
                  <i class="fa fa-spinner fa-spin"></i>
                  <p class="mt-2 mb-0">Loading pages...</p>
                </div>
              </div>
            </div>

            <!-- Site Theme Sidebar -->
            <div class="w3-bar-block w3-card p-2 w3-animate-left" style="display:none;" id="siteThemeSidebar">
                <!-- <div class="w3-bar w3-dark-grey">
                    <span class="w3-bar-item w3-large">Site Theme</span>
                    <button class="w3-bar-item w3-button w3-large w3-right" onclick="closeSiteThemeSidebar()">&times;</button>
                </div> -->
                <div class="sidebar-header px-2 py-2">
                    <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">Site Theme</h3>
                    <button onclick="closeAllSidebars()" class="text-gray-400 hover:text-gray-600 border-none hover:bg-gray-100 rounded-full p-1">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                    </div>
                </div>

                <div class="w3-container w3-padding">
                    <!-- Current Theme Section -->
                    <div class="w3-margin-bottom">
                        <h4 class="w3-text-grey">Current theme</h4>
                        <div class="current-theme-container w3-border w3-round w3-padding" style="border: 2px solid #00bcd4 !important;padding: 5px;">
                            <div class="theme-info">
                                <strong>Site Theme</strong>
                                <div class="w3-small w3-text-grey">This theme is used across your site.</div>
                            </div>
                            <div class="theme-colors" style="float: right; margin-top: 5px;">
                                <span class="color-dot" style="background: #ff6b35; width: 20px; height: 20px; display: inline-block; border-radius: 3px; margin-left: 3px;"></span>
                                <span class="color-dot" style="background: #f7931e; width: 20px; height: 20px; display: inline-block; border-radius: 3px; margin-left: 3px;"></span>
                            </div>
                            <div style="clear: both;"></div>
                        </div>
                    </div>

                    <!-- Featured Themes Section -->
                    <div class="w3-margin-bottom">
                        <h4 class="w3-text-grey">Featured themes</h4>

                        <!-- Energetic Theme -->
                        <div class="theme-option w3-border w3-round w3-padding w3-margin-bottom w3-hover-light-grey" onclick="selectTheme(this, 'energetic')" style="cursor: pointer; transition: all 0.3s ease;">
                            <div class="theme-info">
                                <strong>Energetic</strong>
                                <div class="w3-small w3-text-grey">Vibrant & joyful</div>
                            </div>
                            <div class="theme-colors" style="float: right; margin-top: 5px;">
                                <span class="color-dot" style="background: #4285f4; width: 20px; height: 20px; display: inline-block; border-radius: 3px; margin-left: 3px;"></span>
                                <span class="color-dot" style="background: #ea4335; width: 20px; height: 20px; display: inline-block; border-radius: 3px; margin-left: 3px;"></span>
                                <span class="color-dot" style="background: #c5a3ff; width: 20px; height: 20px; display: inline-block; border-radius: 3px; margin-left: 3px;"></span>
                            </div>
                            <div style="clear: both;"></div>
                        </div>

                        <!-- Retro Vibrant Theme -->
                        <div class="theme-option w3-border w3-round w3-padding w3-margin-bottom w3-hover-light-grey" onclick="selectTheme(this, 'retro-vibrant')" style="cursor: pointer; transition: all 0.3s ease;">
                            <div class="theme-info">
                                <strong>Retro vibrant</strong>
                                <div class="w3-small w3-text-grey">Hip, mod & stylish</div>
                            </div>
                            <div class="theme-colors" style="float: right; margin-top: 5px;">
                                <span class="color-dot" style="background: #1e3a8a; width: 20px; height: 20px; display: inline-block; border-radius: 3px; margin-left: 3px;"></span>
                                <span class="color-dot" style="background: #eab308; width: 20px; height: 20px; display: inline-block; border-radius: 3px; margin-left: 3px;"></span>
                                <span class="color-dot" style="background: #c084fc; width: 20px; height: 20px; display: inline-block; border-radius: 3px; margin-left: 3px;"></span>
                                <span class="color-dot" style="background: #be185d; width: 20px; height: 20px; display: inline-block; border-radius: 3px; margin-left: 3px;"></span>
                            </div>
                            <div style="clear: both;"></div>
                        </div>

                        <!-- Eclectic Theme -->
                        <div class="theme-option w3-border w3-round w3-padding w3-margin-bottom w3-hover-light-grey" onclick="selectTheme(this, 'eclectic')" style="cursor: pointer; transition: all 0.3s ease;">
                            <div class="theme-info">
                                <strong>Eclectic</strong>
                                <div class="w3-small w3-text-grey">Diverse & refreshing</div>
                            </div>
                            <div class="theme-colors" style="float: right; margin-top: 5px;">
                                <span class="color-dot" style="background: #f8fafc; width: 20px; height: 20px; display: inline-block; border-radius: 3px; margin-left: 3px;"></span>
                                <span class="color-dot" style="background: #ef4444; width: 20px; height: 20px; display: inline-block; border-radius: 3px; margin-left: 3px;"></span>
                                <span class="color-dot" style="background: #f97316; width: 20px; height: 20px; display: inline-block; border-radius: 3px; margin-left: 3px;"></span>
                                <span class="color-dot" style="background: #10b981; width: 20px; height: 20px; display: inline-block; border-radius: 3px; margin-left: 3px;"></span>
                            </div>
                            <div style="clear: both;"></div>
                        </div>

                        <!-- Dynamic Theme -->
                        <div class="theme-option w3-border w3-round w3-padding w3-margin-bottom w3-hover-light-grey" onclick="selectTheme(this, 'dynamic')" style="cursor: pointer; transition: all 0.3s ease;">
                            <div class="theme-info">
                                <strong>Dynamic</strong>
                                <div class="w3-small w3-text-grey">Lively, active & bright</div>
                            </div>
                            <div class="theme-colors" style="float: right; margin-top: 5px;">
                                <span class="color-dot" style="background: #84cc16; width: 20px; height: 20px; display: inline-block; border-radius: 3px; margin-left: 3px;"></span>
                                <span class="color-dot" style="background: #7c3aed; width: 20px; height: 20px; display: inline-block; border-radius: 3px; margin-left: 3px;"></span>
                                <span class="color-dot" style="background: #a855f7; width: 20px; height: 20px; display: inline-block; border-radius: 3px; margin-left: 3px;"></span>
                            </div>
                            <div style="clear: both;"></div>
                        </div>

                        <!-- Technical Theme -->
                        <div class="theme-option w3-border w3-round w3-padding w3-margin-bottom w3-hover-light-grey" onclick="selectTheme(this, 'technical')" style="cursor: pointer; transition: all 0.3s ease;">
                            <div class="theme-info">
                                <strong>Technical</strong>
                                <div class="w3-small w3-text-grey">Modern & efficient</div>
                            </div>
                            <div class="theme-colors" style="float: right; margin-top: 5px;">
                                <span class="color-dot" style="background: #1e40af; width: 20px; height: 20px; display: inline-block; border-radius: 3px; margin-left: 3px;"></span>
                                <span class="color-dot" style="background: #06b6d4; width: 20px; height: 20px; display: inline-block; border-radius: 3px; margin-left: 3px;"></span>
                                <span class="color-dot" style="background: #d1fae5; width: 20px; height: 20px; display: inline-block; border-radius: 3px; margin-left: 3px;"></span>
                            </div>
                            <div style="clear: both;"></div>
                        </div>

                        <!-- Nostalgic Theme -->
                        <div class="theme-option w3-border w3-round w3-padding w3-margin-bottom w3-hover-light-grey" onclick="selectTheme(this, 'nostalgic')" style="cursor: pointer; transition: all 0.3s ease;">
                            <div class="theme-info">
                                <strong>Nostalgic</strong>
                                <div class="w3-small w3-text-grey">Cozy & comfortable</div>
                            </div>
                            <div class="theme-colors" style="float: right; margin-top: 5px;">
                                <span class="color-dot" style="background: #dc2626; width: 20px; height: 20px; display: inline-block; border-radius: 3px; margin-left: 3px;"></span>
                                <span class="color-dot" style="background: #94a3b8; width: 20px; height: 20px; display: inline-block; border-radius: 3px; margin-left: 3px;"></span>
                                <span class="color-dot" style="background: #475569; width: 20px; height: 20px; display: inline-block; border-radius: 3px; margin-left: 3px;"></span>
                            </div>
                            <div style="clear: both;"></div>
                        </div>

                        <!-- Natural Theme -->
                        <div class="theme-option w3-border w3-round w3-padding w3-margin-bottom w3-hover-light-grey" onclick="selectTheme(this, 'natural')" style="cursor: pointer; transition: all 0.3s ease;">
                            <div class="theme-info">
                                <strong>Natural</strong>
                                <div class="w3-small w3-text-grey">Earthy & verdant</div>
                            </div>
                            <div class="theme-colors" style="float: right; margin-top: 5px;">
                                <span class="color-dot" style="background: #065f46; width: 20px; height: 20px; display: inline-block; border-radius: 3px; margin-left: 3px;"></span>
                                <span class="color-dot" style="background: #059669; width: 20px; height: 20px; display: inline-block; border-radius: 3px; margin-left: 3px;"></span>
                                <span class="color-dot" style="background: #ea580c; width: 20px; height: 20px; display: inline-block; border-radius: 3px; margin-left: 3px;"></span>
                            </div>
                            <div style="clear: both;"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Color Theme Sidebar -->
            <div class="w3-bar-block w3-card p-2 w3-animate-left" style="display:none;" id="colorThemeSidebar">
                <!-- <div class="w3-bar w3-dark-grey">
                    <span class="w3-bar-item w3-large">Color Theme</span>
                    <button class="w3-bar-item w3-button w3-large w3-right" onclick="closeColorThemeSidebar()">&times;</button>
                </div> -->
                 <div class="sidebar-header px-2 py-2">
                    <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">Site content</h3>
                    <button onclick="closeAllSidebars()" class="text-gray-400 hover:text-gray-600 border-none hover:bg-gray-100 rounded-full p-1">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                    </div>
                </div>

                <div class="w3-container w3-padding">
                    <!-- Search Bar -->
                    <!-- <div class="w3-margin-bottom">
                        <div class="search-container" style="position: relative;">
                            <i class="fa fa-search" style="position: absolute; left: 12px; top: 50%; transform: translateY(-50%); color: #999; z-index: 1;"></i>
                            <input type="text" placeholder="Placeholder" class="w3-input w3-border w3-round" style="padding-left: 35px; background-color: #f8f9fa;" id="colorThemeSearch">
                        </div>
                    </div> -->

                    <!-- Colors Picker Section -->
                    <div class="w3-margin-bottom">
                        <h4 class="w3-text-grey">Colors Picker</h4>

                        <!-- Base Color -->
                        <div class="color-section w3-margin-bottom">
                            <div class="flex items-center justify-between mb-2">
                                <label class="w3-text-dark-grey"><strong>Base color</strong></label>
                                <i class="fa fa-info-circle w3-text-light-blue" title="Base color is used for primary elements"></i>
                            </div>

                            <div class="base-color-picker" style="display: flex; gap: 8px; margin-bottom: 8px;">
                                <!-- Light Base Color -->
                                <div class="color-option" onclick="selectBaseColor('light')" style="flex: 1; cursor: pointer;">
                                    <div class="color-preview" style="background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 8px; height: 50px; display: flex; align-items: center; justify-content: center; position: relative;">
                                        <i class="fa fa-tint" style="color: #6c757d; font-size: 18px;"></i>
                                    </div>
                                </div>

                                <!-- Dark Base Color -->
                                <div class="color-option selected-base" onclick="selectBaseColor('dark')" style="flex: 1; cursor: pointer;">
                                    <div class="color-preview" style="background: #212529; border: 2px solid #00bcd4; border-radius: 8px; height: 50px; display: flex; align-items: center; justify-content: center; position: relative;">
                                        <i class="fa fa-tint" style="color: #ffffff; font-size: 18px;"></i>
                                    </div>
                                </div>
                            </div>

                            <!-- Color Gradient Slider -->
                            <div class="color-gradient-slider" style="margin-bottom: 16px;">
                                <input type="range" id="baseColorSlider" min="0" max="100" value="50" class="w3-input" style="width: 100%; height: 8px; border-radius: 4px; background: linear-gradient(to right, #f8f9fa, #6c757d, #212529); outline: none; -webkit-appearance: none;">
                            </div>
                        </div>

                        <!-- Accent Colors -->
                        <div class="accent-colors-section">
                            <div class="flex items-center justify-between mb-2">
                                <label class="w3-text-dark-grey"><strong>Accent colors</strong></label>
                                <i class="fa fa-info-circle w3-text-light-blue" title="Accent colors are used for highlights and secondary elements"></i>
                            </div>

                            <div class="accent-colors-grid" style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; margin-bottom: 16px;">
                                <!-- Teal Accent -->
                                <div class="accent-color-option selected-accent" onclick="selectAccentColor('teal')" style="cursor: pointer;">
                                    <div class="color-preview" style="background: #20b2aa; border: 2px solid #00bcd4; border-radius: 8px; height: 50px; display: flex; align-items: center; justify-content: center;">
                                        <i class="fa fa-tint" style="color: #ffffff; font-size: 18px;"></i>
                                    </div>
                                </div>

                                <!-- Lime Accent -->
                                <div class="accent-color-option" onclick="selectAccentColor('lime')" style="cursor: pointer;">
                                    <div class="color-preview" style="background: #cddc39; border: 2px solid #e9ecef; border-radius: 8px; height: 50px; display: flex; align-items: center; justify-content: center;">
                                        <i class="fa fa-tint" style="color: #333333; font-size: 18px;"></i>
                                    </div>
                                </div>

                                <!-- Light Blue Accent -->
                                <div class="accent-color-option" onclick="selectAccentColor('lightblue')" style="cursor: pointer;">
                                    <div class="color-preview" style="background: #87ceeb; border: 2px solid #e9ecef; border-radius: 8px; height: 50px; display: flex; align-items: center; justify-content: center;">
                                        <i class="fa fa-tint" style="color: #ffffff; font-size: 18px;"></i>
                                    </div>
                                </div>

                                <!-- Maroon Accent -->
                                <div class="accent-color-option" onclick="selectAccentColor('maroon')" style="cursor: pointer;">
                                    <div class="color-preview" style="background: #a0306e; border: 2px solid #e9ecef; border-radius: 8px; height: 50px; display: flex; align-items: center; justify-content: center;">
                                        <i class="fa fa-tint" style="color: #ffffff; font-size: 18px;"></i>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Apply Button -->
                        <div class="w3-margin-top  grid grid-cols-2 gap-2">
                            <button type="button" class="bg-emerald-500 text-white px-4 py-2 rounded-md hover:bg-emerald-600 hover:border-none border-none font-medium transition-colors" onclick="applyAdvancedSettings()">
                                <i class="fa fa-check"></i> Apply
                            </button>
                            <button  type="button" class="bg-gray-300 text-gray-600 px-4 py-2 rounded-md hover:bg-emerald-600 hover:border-none border-none font-medium transition-colors" onclick="resetAdvancedSettings()">
                                <i class="fa fa-undo"></i> Reset
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Advanced Settings Sidebar -->
            <div class="w3-bar-block w3-card p-2 w3-animate-left" style="display:none;" id="advancedSidebar">
                <!-- <div class="w3-bar w3-dark-grey">
                    <span class="w3-bar-item w3-large">Advanced Settings</span>
                    <button class="w3-bar-item w3-button w3-large w3-right" onclick="closeAdvancedSidebar()">&times;</button>
                </div> -->
                <div class="sidebar-header px-2 py-2">
                    <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">Site content</h3>
                    <button onclick="closeAllSidebars()" class="text-gray-400 hover:text-gray-600 border-none hover:bg-gray-100 rounded-full p-1">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                    </div>
                </div>

                <div class="w3-container w3-padding">
                    <!-- Search Bar -->
                    <!-- <div class="w3-margin-bottom">
                        <div class="search-container" style="position: relative;">
                            <i class="fa fa-search" style="position: absolute; left: 12px; top: 50%; transform: translateY(-50%); color: #999; z-index: 1;"></i>
                            <input type="text" placeholder="Placeholder" class="w3-input w3-border w3-round" style="padding-left: 35px; background-color: #f8f9fa;" id="advancedSearch">
                        </div>
                    </div> -->

                    <!-- General Color Pickers Section -->
                    <div class="collapsible-section w3-margin-bottom">
                        <div class="section-header w3-button w3-block w3-left-align w3-light-grey" onclick="toggleAdvancedSection('generalColors')">
                            <span>General Color Pickers</span>
                            <i class="fa fa-chevron-down w3-right section-arrow" id="generalColors-arrow"></i>
                        </div>
                        <div class="section-content" id="generalColors-content" style="display: block;">
                            <!-- Primary Background Color -->
                            <div class="color-picker-item w3-padding-small w3-border-bottom">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <span>Primary Background Color</span>
                                    <div class="color-picker-box" style="width: 30px; height: 20px; background: #e3f2fd; border: 1px solid #ccc; border-radius: 4px; cursor: pointer;" onclick="openColorPicker('primaryBg', '#e3f2fd')"></div>
                                </div>
                            </div>

                            <!-- Secondary Background Color -->
                            <div class="color-picker-item w3-padding-small w3-border-bottom">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <span>Secondary Background Color</span>
                                    <div class="color-picker-box" style="width: 30px; height: 20px; background: #f5f5f5; border: 1px solid #ccc; border-radius: 4px; cursor: pointer;" onclick="openColorPicker('secondaryBg', '#f5f5f5')"></div>
                                </div>
                            </div>

                            <!-- Divider/Line Color -->
                            <div class="color-picker-item w3-padding-small">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <span>Divider/Line Color</span>
                                    <div class="color-picker-box" style="width: 30px; height: 20px; background: #e0e0e0; border: 1px solid #ccc; border-radius: 4px; cursor: pointer;" onclick="openColorPicker('dividerColor', '#e0e0e0')"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Text Color Pickers Section -->
                    <div class="collapsible-section w3-margin-bottom">
                        <div class="section-header w3-button w3-block w3-left-align w3-light-grey" onclick="toggleAdvancedSection('textColors')">
                            <span>Text Color Pickers</span>
                            <i class="fa fa-chevron-down w3-right section-arrow" id="textColors-arrow"></i>
                        </div>
                        <div class="section-content" id="textColors-content" style="display: block;">
                            <!-- Title Color -->
                            <div class="color-picker-item w3-padding-small w3-border-bottom">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <span>Title Color</span>
                                    <div class="color-picker-box" style="width: 30px; height: 20px; background: #000000; border: 1px solid #ccc; border-radius: 4px; cursor: pointer;" onclick="openColorPicker('titleColor', '#000000')"></div>
                                </div>
                            </div>

                            <!-- Subtitle Color -->
                            <div class="color-picker-item w3-padding-small w3-border-bottom">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <span>Subtitle Color</span>
                                    <div class="color-picker-box" style="width: 30px; height: 20px; background: #666666; border: 1px solid #ccc; border-radius: 4px; cursor: pointer;" onclick="openColorPicker('subtitleColor', '#666666')"></div>
                                </div>
                            </div>

                            <!-- Body Text Color -->
                            <div class="color-picker-item w3-padding-small w3-border-bottom">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <span>Body Text Color</span>
                                    <div class="color-picker-box" style="width: 30px; height: 20px; background: #333333; border: 1px solid #ccc; border-radius: 4px; cursor: pointer;" onclick="openColorPicker('bodyTextColor', '#333333')"></div>
                                </div>
                            </div>

                            <!-- Secondary Text Color -->
                            <div class="color-picker-item w3-padding-small w3-border-bottom">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <span>Secondary Text Color</span>
                                    <div class="color-picker-box" style="width: 30px; height: 20px; background: #2e7d32; border: 1px solid #ccc; border-radius: 4px; cursor: pointer;" onclick="openColorPicker('secondaryTextColor', '#2e7d32')"></div>
                                </div>
                            </div>

                            <!-- Link & Action Color -->
                            <div class="color-picker-item w3-padding-small">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <span>Link & Action Color</span>
                                    <div class="color-picker-box" style="width: 30px; height: 20px; background: #4dd0e1; border: 1px solid #ccc; border-radius: 4px; cursor: pointer;" onclick="openColorPicker('linkActionColor', '#4dd0e1')"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Button Customization Section -->
                    <div class="collapsible-section w3-margin-bottom">
                        <div class="section-header w3-button w3-block w3-left-align w3-light-grey" onclick="toggleAdvancedSection('buttonCustomization')">
                            <span>Button Customization</span>
                            <i class="fa fa-chevron-down w3-right section-arrow" id="buttonCustomization-arrow"></i>
                        </div>
                        <div class="section-content" id="buttonCustomization-content" style="display: block;">
                            <!-- Button Type -->
                            <div class="w3-padding-small w3-border-bottom">
                                <label class="w3-text-grey">Button type</label>
                                <select class="w3-select w3-border w3-margin-top" id="buttonType">
                                    <option value="primary">Primary button</option>
                                    <option value="secondary">Secondary button</option>
                                    <option value="outline">Outline button</option>
                                </select>
                            </div>

                            <!-- Regular/Hover Tabs -->
                            <div class="button-tabs w3-margin-top">
                                <div class="tab-buttons" style="display: flex; border-bottom: 1px solid #e0e0e0;">
                                    <button class="tab-button active" onclick="switchButtonTab('regular')" id="regularTab" style="flex: 1; padding: 8px; border: none; background: #f5f5f5; cursor: pointer;">Regular</button>
                                    <button class="tab-button" onclick="switchButtonTab('hover')" id="hoverTab" style="flex: 1; padding: 8px; border: none; background: #ffffff; cursor: pointer;">Hover</button>
                                </div>

                                <!-- Regular Tab Content -->
                                <div class="tab-content" id="regularTabContent">
                                    <!-- Button Fill Color -->
                                    <div class="color-picker-item w3-padding-small w3-border-bottom">
                                        <div style="display: flex; justify-content: space-between; align-items: center;">
                                            <span>Button Fill Color</span>
                                            <div class="color-picker-box" style="width: 30px; height: 20px; background: #2196f3; border: 1px solid #ccc; border-radius: 4px; cursor: pointer;" onclick="openColorPicker('buttonFillColor', '#2196f3')"></div>
                                        </div>
                                    </div>

                                    <!-- Button Border Color -->
                                    <div class="color-picker-item w3-padding-small w3-border-bottom">
                                        <div style="display: flex; justify-content: space-between; align-items: center;">
                                            <span>Button Border Color</span>
                                            <div class="color-picker-box" style="width: 30px; height: 20px; background: #2196f3; border: 1px solid #ccc; border-radius: 4px; cursor: pointer;" onclick="openColorPicker('buttonBorderColor', '#2196f3')"></div>
                                        </div>
                                    </div>

                                    <!-- Button Text Color -->
                                    <div class="color-picker-item w3-padding-small">
                                        <div style="display: flex; justify-content: space-between; align-items: center;">
                                            <span>Button Text Color</span>
                                            <div class="color-picker-box" style="width: 30px; height: 20px; background: #2196f3; border: 1px solid #ccc; border-radius: 4px; cursor: pointer;" onclick="openColorPicker('buttonTextColor', '#2196f3')"></div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Hover Tab Content -->
                                <div class="tab-content" id="hoverTabContent" style="display: none;">
                                    <!-- Hover Fill Color -->
                                    <div class="color-picker-item w3-padding-small w3-border-bottom">
                                        <div style="display: flex; justify-content: space-between; align-items: center;">
                                            <span>Hover Fill Color</span>
                                            <div class="color-picker-box" style="width: 30px; height: 20px; background: #e3f2fd; border: 1px solid #ccc; border-radius: 4px; cursor: pointer;" onclick="openColorPicker('hoverFillColor', '#e3f2fd')"></div>
                                        </div>
                                    </div>

                                    <!-- Hover Border Color -->
                                    <div class="color-picker-item w3-padding-small w3-border-bottom">
                                        <div style="display: flex; justify-content: space-between; align-items: center;">
                                            <span>Hover Border Color</span>
                                            <div class="color-picker-box" style="width: 30px; height: 20px; background: #e3f2fd; border: 1px solid #ccc; border-radius: 4px; cursor: pointer;" onclick="openColorPicker('hoverBorderColor', '#e3f2fd')"></div>
                                        </div>
                                    </div>

                                    <!-- Hover Text Color -->
                                    <div class="color-picker-item w3-padding-small">
                                        <div style="display: flex; justify-content: space-between; align-items: center;">
                                            <span>Hover Text Color</span>
                                            <div class="color-picker-box" style="width: 30px; height: 20px; background: #e3f2fd; border: 1px solid #ccc; border-radius: 4px; cursor: pointer;" onclick="openColorPicker('hoverTextColor', '#e3f2fd')"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Text Theme Section -->
                    <div class="collapsible-section w3-margin-bottom">
                        <div class="section-header w3-button w3-block w3-left-align w3-light-grey" onclick="toggleAdvancedSection('textTheme')">
                            <span>Text Theme</span>
                            <i class="fa fa-chevron-down w3-right section-arrow" id="textTheme-arrow"></i>
                        </div>
                        <div class="section-content" id="textTheme-content" style="display: block;">
                            <!-- Heading Font Dropdown -->
                            <div class="w3-padding-small w3-border-bottom">
                                <label class="w3-text-grey">Heading Font Dropdown</label>
                                <select class="w3-select w3-border w3-margin-top" id="headingFont">
                                    <option value="arial">Arial</option>
                                    <option value="helvetica">Helvetica</option>
                                    <option value="georgia">Georgia</option>
                                    <option value="times">Times New Roman</option>
                                    <option value="roboto">Roboto</option>
                                    <option value="opensans">Open Sans</option>
                                </select>
                            </div>

                            <!-- Paragraph Font Dropdown -->
                            <div class="w3-padding-small">
                                <label class="w3-text-grey">Paragraph Font Dropdown</label>
                                <select class="w3-select w3-border w3-margin-top" id="paragraphFont">
                                    <option value="" disabled selected>Add your heading text</option>
                                    <option value="arial">Arial</option>
                                    <option value="helvetica">Helvetica</option>
                                    <option value="georgia">Georgia</option>
                                    <option value="times">Times New Roman</option>
                                    <option value="roboto">Roboto</option>
                                    <option value="opensans">Open Sans</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Apply and Reset Buttons -->
                    <div class="w3-margin-top  grid grid-cols-2 gap-2">
                        <button type="button" class="bg-emerald-500 text-white px-4 py-2 rounded-md hover:bg-emerald-600 hover:border-none border-none font-medium transition-colors" onclick="applyAdvancedSettings()">
                            <i class="fa fa-check"></i> Apply
                        </button>
                        <button  type="button" class="bg-gray-300 text-gray-600 px-4 py-2 rounded-md hover:bg-emerald-600 hover:border-none border-none font-medium transition-colors" onclick="resetAdvancedSettings()">
                            <i class="fa fa-undo"></i> Reset
                        </button>
                    </div>
                </div>
            </div>

            <!-- SEO Settings Sidebar -->
            <div class="w3-bar-block w3-card w3-animate-left" style="display:none;" id="seoSidebar">
              <div class="flex flex-col h-full">
                <!-- Header -->
                <div class="flex items-center justify-between p-2 border-b border-gray-200">
                  <h3 class="text-lg font-semibold text-gray-900">SEO Settings</h3>
                  <button onclick="closeAllSidebars()" class="text-gray-400 hover:text-gray-600 border-none">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                  </button>
                </div>

                <!-- Content -->
                <div class="flex-1 p-2 space-y-6">
                  <?php
                  // Get current post data
                  $current_post = get_post();
                  $post_id = get_the_ID();
                  $current_title = get_the_title($post_id);
                  $current_slug = $current_post ? $current_post->post_name : '';
                  $current_description = get_post_meta($post_id, '_seo_description', true);
                  $current_keywords = get_post_meta($post_id, '_seo_keywords', true);

                  // Meta description should remain empty if not set (don't auto-generate)
                  // This is specifically for SEO meta description, not page content
                  ?>

                  <!-- PAGE-SPECIFIC SETTINGS -->
                  <!-- <div class="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
                    <h4 class="text-sm font-medium text-blue-800 mb-1">Page-Specific Settings</h4>
                    <p class="text-xs text-blue-600">These settings apply only to this page</p>
                  </div> -->

                  <!-- Page Title -->
                  <div class="space-y-2">
                    <label class="block text-sm font-medium text-gray-700">Page Title</label>
                    <input type="text" id="seo-page-title" placeholder="Enter page title" value="<?php echo esc_attr($current_title); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                  </div>

                  <!-- Page Permalink -->
                  <div class="space-y-2">
                    <label class="block text-sm font-medium text-gray-700">Page Permalink</label>
                    <p class="text-xs text-gray-500 mb-2">Allows users to see or modify the URL slug; usually auto-generated from the title.</p>
                    <input type="text" id="seo-page-slug" placeholder="page-slug" value="<?php echo esc_attr($current_slug); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                  </div>

                  <!-- Meta Description -->
                  <div class="space-y-2">
                    <label class="block text-sm font-medium text-gray-700">Meta Description</label>
                    <p class="text-xs text-gray-500 mb-2">Short description for search engines and social media (recommended: 150-160 characters)</p>
                    <textarea id="seo-page-description" placeholder="Enter a brief description that will appear in search results..." rows="3" maxlength="160" class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"><?php echo esc_textarea($current_description); ?></textarea>
                    <div class="flex justify-between text-xs text-gray-500">
                      <span>Used in search results and social media previews</span>
                      <span id="description-counter">0/160 characters</span>
                    </div>
                  </div>

                  <!-- Meta Keywords -->
                  <div class="space-y-2">
                    <label class="block text-sm font-medium text-gray-700">Meta Keywords <span class="text-gray-400">(Optional)</span></label>
                    <input type="text" id="seo-meta-keywords" placeholder="keyword1, keyword2, keyword3..." value="<?php echo esc_attr($current_keywords); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                  </div>

                  <!-- SITE-WIDE SETTINGS -->
                  <!-- <div class="bg-orange-50 border border-orange-200 rounded-lg p-3 mb-4 mt-6">
                    <h4 class="text-sm font-medium text-orange-800 mb-1">Site-Wide Settings</h4>
                    <p class="text-xs text-orange-600">These settings apply to all pages on this website</p>
                  </div> -->

                  <!-- Header Code -->
                  <div class="space-y-2">
                    <label class="block text-sm font-medium text-gray-700">Header Code</label>
                    <p class="text-xs text-gray-500 mb-2">(Note: this code will apply for all pages on this website)</p>
                    <p class="text-xs text-gray-500 mb-2">Allows insertion of additional HTML or JavaScript to be included in the site header (e.g., tracking codes)</p>
                    <textarea id="seo-header-code" placeholder="Paste your header code here" rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none font-mono text-xs"><?php echo esc_textarea(get_option('seo_header_code', '')); ?></textarea>
                  </div>

                  <!-- Footer Code -->
                  <div class="space-y-2">
                    <label class="block text-sm font-medium text-gray-700">Footer Code</label>
                    <p class="text-xs text-gray-500 mb-2">(Note: this code will apply for all pages on this website)</p>
                    <p class="text-xs text-gray-500 mb-2">Field for any additional scripts or markup to be included at the bottom of the page.</p>
                    <textarea id="seo-footer-code" placeholder="Paste your footer code here" rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none font-mono text-xs"><?php echo esc_textarea(get_option('seo_footer_code', '')); ?></textarea>
                  </div>

                  <!-- Hide Page from Search Engines -->
                  <div class="space-y-2">
                    <label class="block text-sm font-medium text-gray-700">Hide Site from Search Engines <span class="text-gray-400">(Optional)</span></label>
                    <p class="text-xs text-gray-500 mb-2">(Note: this configuration will apply for all pages on this website)</p>
                    <p class="text-xs text-gray-500 mb-3">When enabled, search engines will be discouraged from indexing this site (WordPress Reading Settings).</p>
                    <label class="flex items-center space-x-3">
                      <input type="checkbox" id="seo-hide-search" <?php echo get_option('blog_public') ? '' : 'checked'; ?> class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                      <span class="text-sm text-gray-700">Discourage search engines from indexing this site</span>
                    </label>
                  </div>
                </div>

                <!-- Footer Actions -->
                <div class="p-4 border-t border-gray-200">
                  <div class="flex space-x-3">
                    <button onclick="closeAllSidebars()" class="flex-1 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">
                      Cancel
                    </button>
                    <button onclick="saveSeoSettings()" class="flex-1 px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                      Save Changes
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Reject Template Modal -->
          <div class="modal fade" id="rejectTemplateModal" tabindex="-1" aria-labelledby="rejectTemplateModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered" style="max-width: 500px;">
              <div class="modal-content">
                <div class="modal-header border-0 pb-0">
                  <h5 class="modal-title fw-bold" id="rejectTemplateModalLabel">Reject Template</h5>
                  <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body pt-2">
                  <div class="mb-3">
                    <label for="rejectionReason" class="form-label fw-semibold">Reason of rejection</label>
                    <textarea
                      class="form-control"
                      id="rejectionReason"
                      rows="4"
                      placeholder="Enter reason..."
                      style="resize: none; border: 1px solid #ddd;"
                    ></textarea>
                  </div>

                  <div class="mb-3">
                    <label class="form-label fw-semibold">File attachments</label>
                    <div
                      class="border border-2 border-dashed rounded p-4 text-center"
                      style="border-color: #ddd !important; background-color: #fafafa;"
                      id="fileDropZone"
                    >
                      <i class="fa fa-cloud-upload fa-2x text-muted mb-2"></i>
                      <p class="text-muted mb-2">Drop files here or click to upload</p>
                      <p class="text-muted small mb-0">Supported formats: JPG, PNG, PDF, DOC (Max 10MB)</p>
                      <input type="file" id="attachmentFiles" multiple accept=".jpg,.jpeg,.png,.pdf,.doc,.docx" style="display: none;">
                    </div>
                    <div id="selectedFiles" class="mt-2"></div>
                  </div>
                </div>
                <div class="modal-footer border-0 pt-0">
                  <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel</button>
                  <button type="button" class="btn btn-danger" id="submitRejection">Submit</button>
                </div>
              </div>
            </div>
          </div>

        <!-- Loading Overlay for Customer Content -->
        <div id="customer-content-loading" class="customer-content-loading">
          <div class="customer-loading-spinner"></div>
          <div class="customer-loading-text">Loading your content...</div>
        </div>

        <script>
          // Approval mode detection - make it globally available
          window.isApprovalMode = <?php echo json_encode($is_approval_mode); ?>;
          window.isDesignerPath = <?php echo json_encode(preg_match('/\/designer\//', $_SERVER['REQUEST_URI'])); ?>;


          // Also set it on document ready to ensure it's available
          jQuery(document).ready(function() {
            window.isApprovalMode = <?php echo json_encode($is_approval_mode); ?>;
          });

          // General sidebar management system
          function openSidebar(sidebarId, buttonId) {
            // Close all other sidebars first
            closeAllSidebars();

            // Show the requested sidebar
            const sidebar = document.getElementById(sidebarId);
            const button = document.getElementById(buttonId);

            if (sidebar && button) {
              sidebar.classList.add("customform");
              sidebar.style.display = "block";
              button.classList.add("main_active");

              // Add body class for styling
              document.body.classList.add('sidebar-open');

              // Adjust main content
              // document.getElementById("mainbtn").style.marginRight = "25%";

              // Load specific content based on sidebar type
              if (sidebarId === "pageSectionSidebar") {
                loadWordPressPages();
              }

              // Log SEO data when SEO sidebar is opened
              if (sidebarId === "seoSidebar") {
                setTimeout(function() {
                  logCurrentSeoData();
                }, 100); // Small delay to ensure form is rendered
              }

            }
          }

          function closeAllSidebars() {
            // List of all sidebar IDs and their corresponding button IDs
            const sidebars = [
              { sidebarId: "pageSectionSidebar", buttonId: "openNav" },
              { sidebarId: "mySidebar", buttonId: "openNavSC" },
              { sidebarId: "siteThemeSidebar", buttonId: "openSiteTheme" },
              { sidebarId: "colorThemeSidebar", buttonId: "openColorTheme" },
              { sidebarId: "advancedSidebar", buttonId: "openAdvanced" },
              { sidebarId: "seoSidebar", buttonId: "openSEO" }
            ];

            sidebars.forEach(function(item) {
              const sidebar = document.getElementById(item.sidebarId);
              const button = document.getElementById(item.buttonId);

              if (sidebar) {
                sidebar.classList.remove("customform");
                sidebar.style.display = "none";
              }

              if (button) {
                button.classList.remove("main_active");
              }
            });

            // Remove body class
            document.body.classList.remove('sidebar-open');

            // Reset main content
            document.getElementById("mainbtn").style.marginRight = "0%";
          }

          // Backward compatibility functions
          function sidebar_open() {
            openSidebar("pageSectionSidebar", "openNav");
          }
          function sidebar_close() {
            // Xóa class sidebar-open
            document.body.classList.remove('sidebar-open');

            document.getElementById("mainbtn").style.marginRight = "0%";
            document.getElementById("mySidebar").style.display = "none";
            document.getElementById("openNavSC").classList.remove("main_active");

            // Also ensure page sidebar is closed
            document.getElementById("pageSectionSidebar").style.display = "none";
            document.getElementById("openNav").classList.remove("main_active");

            // Khôi phục kích thước nội dung chính
            document.body.style.marginLeft = "0";
            document.body.style.width = "100%";
          }

          function pageSection_close() {
            // Close page list sidebar
            document.body.classList.remove('sidebar-open');

            document.getElementById("mainbtn").style.marginRight = "0%";
            document.getElementById("pageSectionSidebar").style.display = "none";
            document.getElementById("openNav").classList.remove("main_active");

            // Also ensure tags sidebar is closed
            document.getElementById("mySidebar").style.display = "none";
            document.getElementById("openNavSC").classList.remove("main_active");

            // Restore main content size
            document.body.style.marginLeft = "";
            document.body.style.width = "";
          }
          function sidebar_opensc() {
            openSidebar("mySidebar", "openNavSC");
          }

          // New sidebar functions for additional menu items
          function openColorTheme() {
            openSidebar("colorThemeSidebar", "openColorTheme");
          }

          function openAdvanced() {
            openSidebar("advancedSidebar", "openAdvanced");
          }

          function openSEO() {
            openSidebar("seoSidebar", "openSEO");
          }

          // Section toggle function for new site content design
          function toggleSection(sectionKey) {
            const sectionContent = document.getElementById('section-' + sectionKey);
            const sectionArrow = document.querySelector('[data-section="' + sectionKey + '"]');

            if (sectionContent && sectionArrow) {
              if (sectionContent.style.display === 'none') {
                sectionContent.style.display = 'block';
                sectionArrow.style.transform = 'rotate(0deg)';
              } else {
                sectionContent.style.display = 'none';
                sectionArrow.style.transform = 'rotate(-90deg)';
              }
            }
          }

          // Consolidated event listener for tagging items and their inputs
          jQuery(document).on('click focus', '.tagging-item, .tagging-item input, .tagging-item textarea, .customer-editable-field', function(e) {
            var selector;
            var $target = jQuery(e.target);

            console.log('🖱️ Click/Focus event on:', e.target.tagName, e.target.className);

            // Determine the selector based on the target element
            if ($target.hasClass('tagging-item')) {
              // Clicked on the tagging item itself
              selector = $target.attr('data-selector');
              console.log('📋 Tagging item selector:', selector);
            } else if ($target.hasClass('customer-editable-field')) {
              // Clicked on a customer-editable-field (could be inside or outside tagging-item)
              selector = $target.attr('data-selector') || $target.closest('.tagging-item').attr('data-selector');
              console.log('📝 Customer field selector:', selector);
            } else if ($target.is('input, textarea')) {
              // Clicked on input/textarea inside tagging-item
              selector = $target.closest('.tagging-item').attr('data-selector');
              console.log('⌨️ Input field selector:', selector);
            }

            console.log('🎯 Final selected selector:', selector);

            // Scroll to element if selector found
            if (selector) {
              scrollToElement(selector);
            } else {
              console.log('❌ No selector found for this element');
            }
          });

          // Real-time customer editing functionality
          jQuery(document).on('input change', '.customer-editable-field', function(e) {
            var $input = jQuery(this);
            var value = $input.val();
            var selector = $input.attr('data-selector');
            var fieldType = $input.attr('data-field-type');
            var fieldSubtype = $input.attr('data-field-subtype') || '';

            console.log('⚡ Real-time input change detected:');
            console.log('  - Value:', value);
            console.log('  - Selector:', selector);
            console.log('  - Field Type:', fieldType);
            console.log('  - Field Subtype:', fieldSubtype);

            if (!selector || !fieldType) {
              console.log('❌ Missing selector or fieldType, skipping update');
              return;
            }

            // Check if target element exists
            var $targetElement = jQuery(selector);
            console.log('🎯 Target element found:', $targetElement.length, 'elements');

            // If no elements found and it's an nth-of-type selector, try smart resolution
            if ($targetElement.length === 0 && selector.includes('nth-of-type')) {
              console.log('🔄 Attempting smart selector resolution for nth-of-type selector');
              var smartSelector = trySmartSelectorResolution(selector, fieldType);
              if (smartSelector && smartSelector !== selector) {
                console.log('✨ Smart selector found:', smartSelector);
                var $smartElement = jQuery(smartSelector);
                console.log('🎯 Smart selector elements found:', $smartElement.length);
                if ($smartElement.length > 0) {
                  selector = smartSelector; // Use the smart selector
                  console.log('✅ Using smart selector for update');
                }
              }
            }

            // Update the site layout immediately
            updateSiteElement(selector, value, fieldType, fieldSubtype);

            // Debounced save to prevent too many AJAX calls
            clearTimeout($input.data('saveTimeout'));
            $input.data('saveTimeout', setTimeout(function() {
              saveCustomerEdit(selector, value, fieldType, fieldSubtype);
            }, 500)); // Save after 500ms of no typing
          });

          // Function to update site element immediately
          function updateSiteElement(selector, value, fieldType, fieldSubtype) {
            try {
              console.log('🔧 updateSiteElement called:');
              console.log('  - Selector:', selector);
              console.log('  - Value:', value);
              console.log('  - Field Type:', fieldType);
              console.log('  - Field Subtype:', fieldSubtype);

              var $element = jQuery(selector);
              console.log('  - Elements found:', $element.length);

              if ($element.length === 0) {
                console.log('❌ No elements found for selector:', selector);
                return false;
              }

              console.log('✅ Updating element with value:', value);

              switch(fieldType) {
                case 'text':
                  console.log('📝 Updating text content');
                  $element.text(value);
                  console.log('✅ Text updated to:', $element.text());
                  break;
                case 'button':
                  if (fieldSubtype === 'label') {
                    $element.text(value);
                  } else if (fieldSubtype === 'url') {
                    $element.attr('href', value);
                  }
                  break;
                case 'link':
                  if (fieldSubtype === 'label') {
                    $element.text(value);
                  } else if (fieldSubtype === 'url') {
                    $element.attr('href', value);
                  }
                  break;
                case 'image':
                  // Handle different types of image elements with responsive support
                  if ($element.is('img')) {
                    // Smart responsive image update
                    updateResponsiveImage($element, value);
                  } else if ($element.css('background-image') !== 'none') {
                    // Handle background images
                    $element.css('background-image', 'url(' + value + ')');
                  } else {
                    // Fallback: try to find img element inside
                    var $img = $element.find('img').first();
                    if ($img.length > 0) {
                      // Smart responsive image update for nested img
                      updateResponsiveImage($img, value);
                    } else {
                      // Last resort: set as src attribute
                      $element.attr('src', value);
                    }
                  }
                  break;
                case 'iframe':
                  $element.attr('src', value);
                  break;
                case 'video':
                  $element.attr('src', value);
                  break;
                case 'icon':
                  if (value.startsWith('<svg')) {
                    $element.html(value);
                  } else {
                    $element.attr('src', value);
                  }
                  break;
                default:
                  console.log('📝 Default: Updating text content');
                  $element.text(value);
                  console.log('✅ Default text updated to:', $element.text());
              }

              console.log('✅ updateSiteElement completed successfully');
              return true;

            } catch(error) {
              console.error('❌ Error in updateSiteElement:', error);
              return false;
            }
          }

          // Smart responsive image update function (same as visitor script)
          function updateResponsiveImage($element, newImageUrl) {
            try {
              console.log('🖼️ ADMIN: Updating responsive image with URL:', newImageUrl);

              // Get current srcset and sizes to understand the responsive setup
              var currentSrcset = $element.attr('srcset');
              var currentSizes = $element.attr('sizes');

              console.log('📊 ADMIN: Current srcset:', currentSrcset);
              console.log('📊 ADMIN: Current sizes:', currentSizes);

              // Update the main src attribute
              $element.attr('src', newImageUrl);

              // If there was a srcset, generate a new one based on the new image
              if (currentSrcset && currentSrcset.trim() !== '') {
                var newSrcset = generateResponsiveSrcset(newImageUrl, currentSrcset);
                if (newSrcset) {
                  $element.attr('srcset', newSrcset);
                  console.log('✅ ADMIN: Updated srcset:', newSrcset);
                } else {
                  // If we can't generate proper srcset, remove it to prevent conflicts
                  $element.removeAttr('srcset');
                  console.log('⚠️ ADMIN: Removed srcset (could not generate proper responsive set)');
                }
              }

              // Keep sizes attribute if it exists (it defines responsive behavior)
              if (currentSizes && currentSizes.trim() !== '') {
                console.log('✅ ADMIN: Preserved sizes attribute:', currentSizes);
              }

              // Add cache-busting parameter to force reload
              var cacheBustUrl = newImageUrl + (newImageUrl.includes('?') ? '&' : '?') + 'cb=' + Date.now();
              $element.attr('src', cacheBustUrl);

              // Force browser to reload the image by setting src directly
              if ($element[0]) {
                $element[0].src = cacheBustUrl;
              }

              console.log('✅ ADMIN: Responsive image update completed');
              return true;

            } catch (error) {
              console.error('❌ ADMIN: Error updating responsive image:', error);
              // Fallback to simple src update
              $element.attr('src', newImageUrl);
              return false;
            }
          }

          // Generate responsive srcset from new image URL - keep same URL with original size descriptors
          function generateResponsiveSrcset(newImageUrl, originalSrcset) {
            try {
              // Parse the original srcset to understand the size pattern
              var srcsetEntries = originalSrcset.split(',').map(function(entry) {
                return entry.trim();
              });

              console.log('🔍 ADMIN: Parsing srcset entries:', srcsetEntries);

              var newSrcsetEntries = [];

              srcsetEntries.forEach(function(entry) {
                var parts = entry.split(' ');
                if (parts.length >= 2) {
                  var sizeDescriptor = parts[parts.length - 1]; // Last part is the size descriptor (e.g., "300w", "2x")

                  // Use the SAME new image URL for all sizes, just change the size descriptor
                  newSrcsetEntries.push(newImageUrl + ' ' + sizeDescriptor);
                }
              });

              if (newSrcsetEntries.length > 0) {
                var newSrcset = newSrcsetEntries.join(', ');
                console.log('✅ ADMIN: Generated new srcset (same URL, original sizes):', newSrcset);
                return newSrcset;
              }

              console.log('⚠️ ADMIN: Could not generate valid srcset');
              return null;

            } catch (error) {
              console.error('❌ ADMIN: Error generating srcset:', error);
              return null;
            }
          }



          // Smart selector resolution for nth-of-type selectors (same as visitor script)
          function trySmartSelectorResolution(originalSelector, fieldType) {
            console.log('🔍 Smart selector resolution for:', originalSelector);

            // Strategy 1: Look for elements with data-id attributes
            var $allElements = jQuery('*[data-id]');
            console.log('📊 Found', $allElements.length, 'elements with data-id');

            // Determine what type of element we're looking for
            var targetElement = '';
            if (fieldType === 'image') {
              targetElement = 'img';
            } else if (fieldType === 'text') {
              if (originalSelector.includes('h1')) targetElement = 'h1';
              else if (originalSelector.includes('h2')) targetElement = 'h2';
              else if (originalSelector.includes('h3')) targetElement = 'h3';
              else if (originalSelector.includes('h4')) targetElement = 'h4';
              else if (originalSelector.includes('h5')) targetElement = 'h5';
              else if (originalSelector.includes('h6')) targetElement = 'h6';
              else if (originalSelector.includes('p')) targetElement = 'p';
              else if (originalSelector.includes('span')) targetElement = 'span';
              else if (originalSelector.includes('div')) targetElement = 'div';
            } else if (fieldType === 'button' || fieldType === 'link') {
              targetElement = 'a';
            } else if (fieldType === 'iframe') {
              targetElement = 'iframe';
            } else if (fieldType === 'video') {
              targetElement = 'video';
            }

            console.log('🎯 Looking for target element:', targetElement);

            if (targetElement) {
              // Look for the target element within data-id containers
              var $candidates = jQuery('[data-id] ' + targetElement + ', [data-id]' + targetElement);
              console.log('🔍 Found', $candidates.length, 'candidate elements');

              if ($candidates.length === 1) {
                // Perfect! Only one candidate, generate data-id selector
                var $candidate = $candidates.first();
                var $dataIdContainer = $candidate.closest('[data-id]');
                if ($dataIdContainer.length > 0) {
                  var dataId = $dataIdContainer.attr('data-id');
                  var smartSelector = '[data-id="' + dataId + '"] ' + targetElement;
                  console.log('✨ Generated smart selector:', smartSelector);
                  return smartSelector;
                }
              } else if ($candidates.length > 1) {
                console.log('⚠️ Multiple candidates found, cannot determine unique selector');
              } else {
                console.log('❌ No candidates found for target element');
              }
            }

            console.log('❌ Smart selector resolution failed');
            return null;
          }

          // Function to extract data-id from DOM element using selector
          function extractDataIdFromElement(selector) {
            try {
              var $element = jQuery(selector);
              if ($element.length === 0) {
                return null;
              }

              // Check if the element itself has data-id
              var dataId = $element.attr('data-id');
              if (dataId) {
                return dataId;
              }

              // Check parent elements for data-id (common in Elementor)
              var $parent = $element.closest('[data-id]');
              if ($parent.length > 0) {
                dataId = $parent.attr('data-id');
                return dataId;
              }

              return null;
            } catch (error) {
              return null;
            }
          }

          // Function to save customer edit as draft
          function saveCustomerEdit(selector, value, fieldType, fieldSubtype) {
            // Extract data-id from the actual DOM element for reliable visitor selectors
            var dataId = extractDataIdFromElement(selector);

            jQuery.ajax({
              url: '<?php echo admin_url('admin-ajax.php'); ?>',
              type: 'POST',
              data: {
                action: 'save_customer_edit',
                post_id: <?php echo get_the_ID(); ?>,
                selector: selector,
                field_type: fieldType,
                field_subtype: fieldSubtype,
                value: value,
                data_id: dataId, // Send extracted data-id
                nonce: '<?php echo wp_create_nonce('designer_tagging_nonce'); ?>'
              },
              success: function(response) {
                if (response.success) {
                  // Update UI to show draft status
                  showDraftStatus(selector, true);
                } else {
                  showDraftStatus(selector, false);
                }
              },
              error: function(xhr, status, error) {
                showDraftStatus(selector, false);
              }
            });
          }

          // Function to show draft status indicator
          function showDraftStatus(selector, success) {
            // Find the input field for this selector
            var $input = jQuery('.customer-editable-field[data-selector="' + selector + '"]');

            // Remove existing status indicators
            $input.removeClass('draft-saved draft-error');

            if (success) {
              $input.addClass('draft-saved');
              // Remove the indicator after 2 seconds
              setTimeout(function() {
                $input.removeClass('draft-saved');
              }, 2000);
            } else {
              $input.addClass('draft-error');
              setTimeout(function() {
                $input.removeClass('draft-error');
              }, 3000);
            }
          }

          // Simple function to add trial overlays
          function addTrialOverlays() {
            var upgradeUrl = '<?php echo MAIN_WEBSITE_DOMAIN; ?>/customer/subscription/';

            // Check if sidebars exist
            var $mySidebar = jQuery('#mySidebar');
            var $seoSidebar = jQuery('#seoSidebar');

            // Add overlay to main sidebar (#mySidebar)
            if ($mySidebar.length > 0) {
              $mySidebar.append(`
                <div class="trial-overlay" style="
                  position: fixed;
                  top: 56px;
                  left: 0;
                  width: 400px;
                  height: calc(100vh - 56px);
                  background: rgba(255, 255, 255, 0.7);
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  z-index: 999999;
                  pointer-events: auto;
                ">
                  <a href="${upgradeUrl}" style="
                    background: #007cba;
                    color: white;
                    padding: 12px 24px;
                    text-decoration: none;
                    border-radius: 8px;
                    font-weight: 500;
                    font-size: 14px;
                  ">Upgrade Now</a>
                </div>
              `);
            } else {
            }

            // Add overlay to SEO sidebar (#seoSidebar)
            if ($seoSidebar.length > 0) {
              $seoSidebar.append(`
                <div class="trial-overlay" style="
                  position: fixed;
                  top: 56px;
                  left: 56px;
                  width: 400px;
                  height: calc(100vh - 56px);
                  background: rgba(255, 255, 255, 0.7);
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  z-index: 999999;
                  pointer-events: auto;
                ">
                  <a href="${upgradeUrl}" style="
                    background: #007cba;
                    color: white;
                    padding: 12px 24px;
                    text-decoration: none;
                    border-radius: 8px;
                    font-weight: 500;
                    font-size: 14px;
                  ">Upgrade Now</a>
                </div>
              `);
            } else {
            }

          }

          // SEO Settings Functions
          function saveSeoSettings() {

            // Get form values
            var seoData = {
              page_title: jQuery('#seo-page-title').val(),
              page_slug: jQuery('#seo-page-slug').val(),
              meta_description: jQuery('#seo-page-description').val(),
              meta_keywords: jQuery('#seo-meta-keywords').val(),
              header_code: jQuery('#seo-header-code').val(),
              footer_code: jQuery('#seo-footer-code').val(),
              hide_from_search: jQuery('#seo-hide-search').is(':checked')
            };


            // Show loading state
            var saveButton = jQuery('#seoSidebar button[onclick="saveSeoSettings()"]');
            var originalText = saveButton.text();
            saveButton.text('Saving...').prop('disabled', true);

            // Send AJAX request
            jQuery.ajax({
              url: designerTagging.ajax_url,
              type: 'POST',
              data: {
                action: 'save_seo_settings',
                nonce: designerTagging.nonce,
                post_id: designerTagging.post_id,
                seo_data: seoData
              },
              success: function(response) {

                // Show success message
                showNotification('SEO settings saved successfully!', 'success');

                // Update page title in browser if changed
                if (seoData.page_title) {
                  document.title = seoData.page_title;
                }

                // Reset button
                saveButton.text(originalText).prop('disabled', false);
              },
              error: function(xhr, status, error) {
                showNotification('Error saving SEO settings. Please try again.', 'error');

                // Reset button
                saveButton.text(originalText).prop('disabled', false);
              }
            });
          }

          // Auto-save on input change (debounced)
          function setupSeoAutoSave() {
            var autoSaveTimeout;

            jQuery('#seoSidebar input, #seoSidebar textarea, #seoSidebar select').on('input change', function() {
              clearTimeout(autoSaveTimeout);
              autoSaveTimeout = setTimeout(function() {
                saveSeoSettings();
              }, 2000); // Auto-save after 2 seconds of inactivity
            });

            // Setup character counter for meta description
            setupMetaDescriptionCounter();
          }

          // Character counter for meta description
          function setupMetaDescriptionCounter() {
            var $description = jQuery('#seo-page-description');
            var $counter = jQuery('#description-counter');

            function updateCounter() {
              var length = $description.val().length;
              var maxLength = 160;
              $counter.text(length + '/' + maxLength + ' characters');

              // Color coding based on length
              if (length > maxLength) {
                $counter.css('color', '#ef4444'); // Red - too long
              } else if (length > 140) {
                $counter.css('color', '#f59e0b'); // Orange - getting long
              } else if (length > 120) {
                $counter.css('color', '#10b981'); // Green - good length
              } else {
                $counter.css('color', '#6b7280'); // Gray - default
              }
            }

            // Update counter on input
            $description.on('input', updateCounter);

            // Initialize counter
            updateCounter();
          }

          // Simple notification function
          function showNotification(message, type) {
            var bgColor = type === 'success' ? '#10b981' : '#ef4444';
            var notification = jQuery(`
              <div style="
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${bgColor};
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                z-index: 999999;
                font-size: 14px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
              ">${message}</div>
            `);

            jQuery('body').append(notification);

            // Auto-remove after 3 seconds
            setTimeout(function() {
              notification.fadeOut(300, function() {
                notification.remove();
              });
            }, 3000);
          }

          // Function to log current SEO data when sidebar opens
          function logCurrentSeoData() {
          }

          // Initialize customer values on page load
          jQuery(document).ready(function() {

            // Initialize default sidebar (Site Content) as open
            openSidebar("mySidebar", "openNavSC");

            // Simple trial overlay for Trial plan
            if (typeof designerTagging !== 'undefined' && designerTagging.is_trial) {
              // Add small delay to ensure sidebars are rendered
              setTimeout(function() {
                addTrialOverlays();
              }, 500);
            } else {
            }

            // Initialize SEO auto-save functionality
            setupSeoAutoSave();

            // Apply all customer values to site layout on page load
            initializeCustomerValues();

            // Add click handler for field labels to scroll to elements
            jQuery('.field-label').on('click', function() {
              var $fieldContainer = jQuery(this).closest('.field-container');
              var $input = $fieldContainer.find('.customer-editable-field').first();
              if ($input.length > 0) {
                var selector = $input.attr('data-selector');
                if (selector) {
                  scrollToElement(selector);
                }
              }
            });

            // Add drag & drop functionality to image upload areas
            jQuery('.image-upload-area').each(function() {
              var $uploadArea = jQuery(this);
              var $fileInput = $uploadArea.find('.hidden-file-input');

              // Prevent default drag behaviors
              $uploadArea.on('dragenter dragover dragleave drop', function(e) {
                e.preventDefault();
                e.stopPropagation();
              });

              // Add visual feedback for drag over
              $uploadArea.on('dragenter dragover', function() {
                $uploadArea.addClass('border-blue-500 bg-blue-50');
              });

              $uploadArea.on('dragleave drop', function() {
                $uploadArea.removeClass('border-blue-500 bg-blue-50');
              });

              // Handle file drop
              $uploadArea.on('drop', function(e) {
                var files = e.originalEvent.dataTransfer.files;
                if (files.length > 0) {
                  $fileInput[0].files = files;
                  handleFileUpload($fileInput[0]);
                }
              });
            });

            // Save as Draft button
            jQuery('#btn-save-customer-updates').on('click', function(e) {
              e.preventDefault();
              updateCustomerStatus('draft');
            });

            // Preview button - shows current state (draft or published)
            jQuery('#btn-preview-customer-updates').on('click', function(e) {
              e.preventDefault();
              // Preview functionality - just refresh to show current state
              showPreviewMessage();
            });

            // Publish button
            jQuery('#btn-publish-customer-updates').on('click', function(e) {
              e.preventDefault();
              if (confirm('Are you sure you want to publish all changes? This will make them live on the website.')) {
                updateCustomerStatus('published');
              }
            });
          });

          // VISITOR REAL-TIME LOADING: Apply published customer edits for visitors
          function applyVisitorCustomerEdits() {

            if (!designerTagging.customer_edits || designerTagging.customer_edits.length === 0) {
              return;
            }


            let appliedCount = 0;
            let failedCount = 0;

            designerTagging.customer_edits.forEach(function(edit, index) {

              // Check if element exists before applying
              var $element = jQuery(edit.selector);

              try {
                // Use the same updateSiteElement function that works for logged-in users
                if (updateSiteElement(edit.selector, edit.value, edit.field_type, edit.field_subtype)) {
                  appliedCount++;
                  // Verify the change was applied
                  var $updatedElement = jQuery(edit.selector);
                } else {
                  failedCount++;
                }
              } catch (error) {
                failedCount++;
              }
            });
          }

          // Function to update customer edit status
          function updateCustomerStatus(status) {
            var $button = jQuery('#btn-' + (status === 'published' ? 'publish' : 'save') + '-customer-updates');
            var originalText = $button.text();

            // Show loading state
            $button.text(status === 'published' ? 'Publishing...' : 'Saving...').prop('disabled', true);

            jQuery.ajax({
              url: '<?php echo admin_url('admin-ajax.php'); ?>',
              type: 'POST',
              data: {
                action: 'update_customer_status',
                post_id: <?php echo get_the_ID(); ?>,
                status: status,
                nonce: '<?php echo wp_create_nonce('designer_tagging_nonce'); ?>'
              },
              success: function(response) {
                if (response.success) {
                  // Log debug information for page builder detection
                  if (status === 'published' && response.data.page_builder) {
                  }

                  // Show success message with debug info
                  var message = response.data.message;
                  if (status === 'published' && response.data.page_builder) {
                    message += ' (Builder: ' + response.data.page_builder + ', Updates: ' + response.data.updates_made + ')';
                  }
                  showStatusMessage(message, 'success');

                  // If publishing, apply all current values directly to layout
                  // This makes publish feel immediate without waiting for page reload
                  if (status === 'published') {
                    applyAllCurrentValuesToLayout();
                  }

                  // Reset button
                  $button.text(originalText).prop('disabled', false);
                } else {
                  showStatusMessage('Error: ' + response.data, 'error');
                  $button.text(originalText).prop('disabled', false);
                }
              },
              error: function(xhr, status, error) {
                showStatusMessage('AJAX Error: ' + error, 'error');
                $button.text(originalText).prop('disabled', false);
              }
            });
          }

          // Function to show status messages
          function showStatusMessage(message, type) {
            // Remove existing messages
            jQuery('.status-message').remove();

            // Create message element
            var messageClass = type === 'success' ? 'bg-green-100 text-green-800 border-green-200' : 'bg-red-100 text-red-800 border-red-200';
            var iconClass = type === 'success' ? '✓' : '✕';
            var messageHtml = '<div class="status-message fixed bottom-4 right-4 z-50 px-4 py-3 rounded-lg border ' + messageClass + ' shadow-lg max-w-sm">' +
                             '<div class="flex items-center">' +
                             '<span class="text-lg mr-2">' + iconClass + '</span>' +
                             '<span class="text-sm font-medium flex-1">' + message + '</span>' +
                             '<button class="ml-3 text-lg leading-none hover:opacity-70 border-none" onclick="jQuery(this).parent().parent().remove()">&times;</button>' +
                             '</div></div>';

            // Add to page with slide-in animation
            var $message = jQuery(messageHtml);
            $message.css({
              'transform': 'translateX(100%)',
              'transition': 'transform 0.3s ease-out'
            });

            jQuery('body').append($message);

            // Trigger slide-in animation
            setTimeout(function() {
              $message.css('transform', 'translateX(0)');
            }, 10);

            // Auto-remove after 5 seconds
            setTimeout(function() {
              $message.css('transform', 'translateX(100%)');
              setTimeout(function() {
                $message.remove();
              }, 300);
            }, 5000);
          }



          // Function to show preview message
          function showPreviewMessage() {
            showStatusMessage('Preview mode: You are seeing the current state of your edits (draft and published changes)', 'success');
          }

          // Function to initialize customer values on page load
          function initializeCustomerValues() {

            // Show loading overlay
            var $loading = jQuery('#customer-content-loading');
            if ($loading.length === 0) {
              // Create loading overlay if it doesn't exist
              jQuery('body').append('<div id="customer-content-loading" class="customer-content-loading"><div class="customer-loading-spinner"></div><div class="customer-loading-text">Loading your content...</div></div>');
              $loading = jQuery('#customer-content-loading');
            }
            $loading.show();

            // Small delay to ensure DOM is fully ready
            setTimeout(function() {
              var appliedCount = 0;

              // Loop through all customer-editable-field inputs and apply their values to site layout
              jQuery('.customer-editable-field').each(function() {
                var $input = jQuery(this);
                var value = $input.val();
                var selector = $input.attr('data-selector');
                var fieldType = $input.attr('data-field-type');
                var fieldSubtype = $input.attr('data-field-subtype') || '';

                // Only apply if there's a value and it's different from placeholder
                if (value && value.trim() !== '' && selector && fieldType) {
                  

                  // Apply the value to the site element
                  updateSiteElement(selector, value, fieldType, fieldSubtype);
                  appliedCount++;
                }
              });


              // Hide loading overlay with smooth fade out
              $loading.fadeOut(300, function() {
              });

            }, 100); // 100ms delay to ensure DOM is ready
          }

          // Function to apply all current values to layout (used after publish)
          function applyAllCurrentValuesToLayout() {

            var appliedCount = 0;
            var totalFields = 0;

            // Loop through all customer-editable-field inputs
            jQuery('.customer-editable-field').each(function() {
              totalFields++;
              var $input = jQuery(this);
              var value = $input.val();
              var selector = $input.attr('data-selector');
              var fieldType = $input.attr('data-field-type');
              var fieldSubtype = $input.attr('data-field-subtype') || '';

              // Apply value regardless of whether it's empty or not
              // This ensures published state matches exactly what's in sidebar
              if (selector && fieldType) {
              
                // Apply the value to the site element
                updateSiteElement(selector, value, fieldType, fieldSubtype);
                appliedCount++;
              }
            });

       
            // Show a subtle success indicator
            showStatusMessage('Published!', 'success');
          }

          // WordPress Media Library Integration
          function openWordPressMediaLibrary(uploadArea) {

            // Check if wp.media is available
            if (typeof wp === 'undefined' || typeof wp.media === 'undefined') {
              alert('WordPress media library not available. Please refresh the page and try again.');
              return;
            }

            // Create media frame
            var mediaFrame = wp.media({
              title: 'Select Image',
              button: {
                text: 'Use This Image'
              },
              multiple: false,
              library: {
                type: 'image'
              }
            });

            // Handle image selection
            mediaFrame.on('select', function() {
              var attachment = mediaFrame.state().get('selection').first().toJSON();

              // Find the input field in this upload area
              var $input = jQuery(uploadArea).find('.customer-editable-field');
              if ($input.length > 0) {
                // Set the image URL
                $input.val(attachment.url);

                // Trigger multiple events to ensure proper handling
                $input.trigger('input');
                $input.trigger('change');
                $input.trigger('keyup');

                // Force update the layout immediately
                var selector = $input.attr('data-selector');
                var fieldType = $input.attr('data-field-type');
                var fieldSubtype = $input.attr('data-field-subtype') || '';
                
                if (selector && fieldType) {
                  updateSiteElement(selector, attachment.url, fieldType, fieldSubtype);
                }

                showStatusMessage('Image selected successfully!', 'success');
              } else {
              }
            });

            // Open the media frame
            mediaFrame.open();
          }

          // Handle file upload via drag & drop or file input
          function handleFileUpload(fileInput) {
            var file = fileInput.files[0];
            if (!file) return;


            // Validate file type
            if (!file.type.startsWith('image/')) {
              alert('Please select an image file (JPG, PNG, GIF).');
              return;
            }

            // Validate file size (800KB = 800 * 1024 bytes)
            if (file.size > 800 * 1024) {
              alert('File size must be less than 800KB.');
              return;
            }

            // Create FormData for upload
            var formData = new FormData();
            formData.append('file', file);
            formData.append('action', 'upload_customer_image');
            formData.append('nonce', '<?php echo wp_create_nonce('customer_image_upload'); ?>');

            // Show uploading status
            showStatusMessage('Uploading image...', 'success');

            // Upload via AJAX
            jQuery.ajax({
              url: '<?php echo admin_url('admin-ajax.php'); ?>',
              type: 'POST',
              data: formData,
              processData: false,
              contentType: false,
              success: function(response) {
                if (response.success) {
                  // Find the input field
                  var $input = jQuery(fileInput).closest('.image-upload-area').find('.customer-editable-field');
                  if ($input.length > 0) {
                    $input.val(response.data.url);

                    // Trigger multiple events to ensure proper handling
                    $input.trigger('input');
                    $input.trigger('change');
                    $input.trigger('keyup');

                    // Force update the layout immediately
                    var selector = $input.attr('data-selector');
                    var fieldType = $input.attr('data-field-type');
                    
                    if (selector && fieldType) {
                      var fieldSubtype = $input.attr('data-field-subtype') || '';
                      updateSiteElement(selector, response.data.url, fieldType, fieldSubtype);
                    }

                    showStatusMessage('Image uploaded successfully!', 'success');
                  } else {
                  }
                } else {
                  showStatusMessage('Upload failed: ' + response.data, 'error');
                }
              },
              error: function() {
                showStatusMessage('Upload failed. Please try again.', 'error');
              }
            });
          }

          function loadWordPressPages() {

            // Show loading state
            document.getElementById('page_list_container').innerHTML = `
              <div class="text-center py-4">
                <i class="fa fa-spinner fa-spin"></i>
                <p class="mt-2">Loading pages...</p>
              </div>
            `;

            // Fetch WordPress pages via AJAX
            const currentUrl = window.location.href;
            jQuery.ajax({
              url: '<?php echo admin_url('admin-ajax.php'); ?>',
              type: 'POST',
              data: {
                action: 'ipt_get_wordpress_pages',
                current_url: currentUrl,
                security: '<?php echo wp_create_nonce('ipt_pages_nonce'); ?>'
              },
              success: function(response) {

                if (response.data && response.data.debug_info) {
                }

                if (response.success && response.data) {
                  renderPageList(response.data.pages, response.data.homepage_id, response.data.current_page_id);
                } else {
                  document.getElementById('page_list_container').innerHTML = `
                    <div class="text-center py-4">
                      <p class="text-muted">No pages found</p>
                    </div>
                  `;
                }
              },
              error: function(xhr, status, error) {
                document.getElementById('page_list_container').innerHTML = `
                  <div class="text-center py-4">
                    <p class="text-danger">Error loading pages</p>
                  </div>
                `;
              }
            });
          }

          function renderPageList(pages, homepageId, currentPageId) {
            let html = '';

            if (pages.length === 0) {
              html = `
                <div class="text-center py-4">
                  <p class="text-muted mb-0">No pages found</p>
                </div>
              `;
            } else {
              pages.forEach(function(page, index) {
                // Determine page icon and highlighting
                let pageIcon = 'fa-file-o';
                const isHomePage = (homepageId && page.id == homepageId);
                const isCurrentPage = (currentPageId && page.id == currentPageId);

                // Use home icon for homepage
                if (isHomePage) {
                  pageIcon = 'fa-home';
                }

                // Highlight current page (priority) or homepage
                const shouldHighlight = isCurrentPage || (isHomePage && !currentPageId);
                const itemClass = shouldHighlight ? 'bg-primary text-white' : '';
                const textClass = shouldHighlight ? 'text-white' : 'text-dark';

                // Hide menu button in approval mode
                const menuButton = isApprovalMode ? '' : `
                  <button class="btn btn-sm p-1 page-menu-btn" onclick="togglePageMenu(${page.id}, event)" style="border: none; background: none;">
                    <i class="fa fa-ellipsis-h ${textClass}" style="font-size: 12px;"></i>
                  </button>
                `;

                html += `
                  <div class="page-list-item position-relative border-bottom" data-page-id="${page.id}">
                    <div class="d-flex align-items-center p-3 ${itemClass}" style="cursor: pointer;" onclick="redirectToPage('${page.view_url}', event)">
                      <i class="fa ${pageIcon} me-3 ${textClass}" style="font-size: 16px; width: 20px;"></i>
                      <span class="flex-grow-1 ${textClass}" style="font-size: 14px;">${page.title}</span>
                      ${menuButton}
                    </div>

                    <!-- Context Menu -->
                    <div class="page-context-menu position-fixed bg-white border shadow-sm" id="page-menu-${page.id}" style="display: none; min-width: 150px; z-index: 9999; border-radius: 4px;">
                      <div class="py-1">
                        <a href="#" class="dropdown-item px-3 py-2 text-decoration-none d-block" style="font-size: 13px; color: #333;" onclick="renamePage(${page.id})">
                          Rename
                        </a>
                        <a href="#" class="dropdown-item px-3 py-2 text-decoration-none d-block" style="font-size: 13px; color: #333;" onclick="duplicatePage(${page.id})">
                          Duplicate
                        </a>
                        <a href="${page.edit_url}" target="_blank" class="dropdown-item px-3 py-2 text-decoration-none d-block" style="font-size: 13px; color: #333;">
                          Edit Page
                        </a>
                        <a href="#" class="dropdown-item px-3 py-2 text-decoration-none d-block" style="font-size: 13px; color: #dc3545;" onclick="deletePage(${page.id})">
                          Delete
                        </a>
                        <hr class="my-1">
                        <a href="#" class="dropdown-item px-3 py-2 text-decoration-none d-block" style="font-size: 13px; color: #333;" onclick="setAsHomepage(${page.id})">
                          Set as Homepage
                        </a>
                      </div>
                    </div>
                  </div>
                `;
              });
            }

            document.getElementById('page_list_container').innerHTML = html;

            // Add search functionality
            document.getElementById('page-search').addEventListener('input', function(e) {
              const searchTerm = e.target.value.toLowerCase();
              const pageItems = document.querySelectorAll('.page-list-item');

              pageItems.forEach(function(item) {
                const pageTitle = item.querySelector('span').textContent.toLowerCase();
                if (pageTitle.includes(searchTerm)) {
                  item.style.display = 'block';
                } else {
                  item.style.display = 'none';
                }
              });
            });
          }

          // Function to preserve URL parameters when redirecting
          function getUrlWithParams(newUrl) {
            const currentUrl = new URL(window.location.href);
            const newUrlObj = new URL(newUrl, window.location.origin);

            // Check if the new URL is a /designer/ page
            const isDesignerPage = newUrlObj.pathname.startsWith('/designer/');


            // Only preserve parameters if NOT going to a /designer/ page
            if (!isDesignerPage) {
              // Get current URL parameters
              const currentParams = currentUrl.searchParams;

              // Preserve specific parameters (excluding bypass_token)
              const paramsToPreserve = ['action', 'user_id', 'template_id'];
              const paramsToExclude = ['bypass_token'];


              paramsToPreserve.forEach(param => {
                if (currentParams.has(param)) {
                  newUrlObj.searchParams.set(param, currentParams.get(param));
                }
              });

              // Log excluded parameters
              paramsToExclude.forEach(param => {
                if (currentParams.has(param)) {
                }
              });

            } else {
            }


            return newUrlObj.href;
          }

          // Function to redirect with conditional parameter preservation
          function redirectWithParams(url) {
            const urlWithParams = getUrlWithParams(url);
            window.location.href = urlWithParams;
          }

          // Function to redirect to page (with parameter preservation)
          function redirectToPage(pageUrl, event) {
            // Only redirect if the click is not on the menu button
            if (event && event.target.closest('.page-menu-btn')) {
              return; // Don't redirect if clicking on menu button
            }


            // Use the parameter preservation function for external pages
            const finalUrl = getUrlWithParams(pageUrl);

            // Redirect to the page
            window.location.href = finalUrl;
          }

          // Page context menu functions
          function togglePageMenu(pageId, event) {
            event.stopPropagation();
            event.preventDefault(); // Prevent the parent click event

            // Disable context menu in approval mode
            if (isApprovalMode) {
              return;
            }

            // Close all other menus
            document.querySelectorAll('.page-context-menu').forEach(function(menu) {
              if (menu.id !== 'page-menu-' + pageId) {
                menu.style.display = 'none';
              }
            });

            // Get the menu element
            const menu = document.getElementById('page-menu-' + pageId);

            if (menu.style.display === 'none' || menu.style.display === '') {
              // Position the menu relative to the button
              const button = event.target.closest('.page-menu-btn');
              const buttonRect = button.getBoundingClientRect();
              const sidebarRect = document.getElementById('pageSectionSidebar').getBoundingClientRect();

              // Calculate position
              let top = buttonRect.bottom + 5; // 5px below the button
              let left = buttonRect.right - 150; // Align right edge of menu with button

              // Adjust if menu would go below viewport
              const menuHeight = 200; // Approximate menu height
              if (top + menuHeight > window.innerHeight) {
                top = buttonRect.top - menuHeight - 5; // Show above button
              }

              // Ensure menu doesn't go outside sidebar bounds
              if (left < sidebarRect.left) {
                left = sidebarRect.left + 10;
              }

              menu.style.top = top + 'px';
              menu.style.left = left + 'px';
              menu.style.display = 'block';
            } else {
              menu.style.display = 'none';
            }
          }

          // Close menus when clicking outside
          document.addEventListener('click', function(event) {
            if (!event.target.closest('.page-menu-btn') && !event.target.closest('.page-context-menu')) {
              document.querySelectorAll('.page-context-menu').forEach(function(menu) {
                menu.style.display = 'none';
              });
            }
          });

          function renamePage(pageId) {
            const newName = prompt('Enter new page name:');
            if (newName && newName.trim()) {

              // Show loading state
              const menuItem = document.querySelector(`[data-page-id="${pageId}"] span`);
              const originalText = menuItem.textContent;
              menuItem.textContent = 'Renaming...';

              jQuery.ajax({
                url: '<?php echo admin_url('admin-ajax.php'); ?>',
                type: 'POST',
                data: {
                  action: 'ipt_rename_page',
                  page_id: pageId,
                  new_title: newName,
                  security: '<?php echo wp_create_nonce('ipt_pages_nonce'); ?>'
                },
                success: function(response) {
                  if (response.success) {
                    // Update the page title in the list
                    menuItem.textContent = response.data.new_title;
                  } else {
                    alert('Error: ' + response.data.message);
                    menuItem.textContent = originalText;
                  }
                },
                error: function(xhr, status, error) {
                  alert('Failed to rename page. Please try again.');
                  menuItem.textContent = originalText;
                }
              });
            }
            document.getElementById('page-menu-' + pageId).style.display = 'none';
          }

          function duplicatePage(pageId) {

            if (confirm('Are you sure you want to duplicate this page?')) {
              jQuery.ajax({
                url: '<?php echo admin_url('admin-ajax.php'); ?>',
                type: 'POST',
                data: {
                  action: 'ipt_duplicate_page',
                  page_id: pageId,
                  security: '<?php echo wp_create_nonce('ipt_pages_nonce'); ?>'
                },
                success: function(response) {
                  if (response.success) {
                    var successMessage = 'Page duplicated successfully! The new page is published and accessible.';
                    if (response.data.is_elementor_page) {
                      successMessage += ' (Elementor page detected)';
                    }
                    alert(successMessage);

                    // Optionally reload the page list to show the new page
                    loadWordPressPages();

                    // Optionally open the new page for editing
                    var editPrompt = response.data.is_elementor_page
                      ? 'Would you like to edit the duplicated page in Elementor now?'
                      : 'Would you like to edit the duplicated page now?';

                    if (confirm(editPrompt)) {
                      window.open(response.data.edit_url, '_blank');
                    }
                  } else {
                    alert('Error: ' + response.data.message);
                  }
                },
                error: function(xhr, status, error) {
                  alert('Failed to duplicate page. Please try again.');
                }
              });
            }
            document.getElementById('page-menu-' + pageId).style.display = 'none';
          }

          function deletePage(pageId) {
            if (confirm('Are you sure you want to delete this page? This action cannot be undone.')) {

              jQuery.ajax({
                url: '<?php echo admin_url('admin-ajax.php'); ?>',
                type: 'POST',
                data: {
                  action: 'ipt_delete_page',
                  page_id: pageId,
                  security: '<?php echo wp_create_nonce('ipt_pages_nonce'); ?>'
                },
                success: function(response) {
                  if (response.success) {
                    alert('Page deleted successfully!');

                    // Remove the page from the list
                    const pageItem = document.querySelector(`[data-page-id="${pageId}"]`);
                    if (pageItem) {
                      pageItem.remove();
                    }
                  } else {
                    alert('Error: ' + response.data.message);
                  }
                },
                error: function(xhr, status, error) {
                  alert('Failed to delete page. Please try again.');
                }
              });
            }
            document.getElementById('page-menu-' + pageId).style.display = 'none';
          }

          function setAsHomepage(pageId) {
            if (confirm('Set this page as homepage?')) {

              jQuery.ajax({
                url: '<?php echo admin_url('admin-ajax.php'); ?>',
                type: 'POST',
                data: {
                  action: 'ipt_set_homepage',
                  page_id: pageId,
                  security: '<?php echo wp_create_nonce('ipt_pages_nonce'); ?>'
                },
                success: function(response) {
                  if (response.success) {
                    alert('Homepage set successfully!');

                    // Reload the page list to update the homepage styling
                    loadWordPressPages();
                  } else {
                    alert('Error: ' + response.data.message);
                  }
                },
                error: function(xhr, status, error) {
                  alert('Failed to set homepage. Please try again.');
                }
              });
            }
            document.getElementById('page-menu-' + pageId).style.display = 'none';
          }

          // Approval mode functions
          function handleApproval(action) {

            if (confirm(`Are you sure you want to ${action} this page?`)) {
              // Get current page information
              const currentUrl = window.location.href;
              const urlParams = new URLSearchParams(window.location.search);
              const templateId = urlParams.get('template_id');


              if (!templateId) {
                alert('Error: Template ID not found in URL parameters.');
                return;
              }

              // Show loading state
              const approveBtn = document.getElementById('approve-btn');
              const rejectBtn = document.getElementById('reject-btn');

              if (action === 'approve') {
                approveBtn.disabled = true;
                approveBtn.innerHTML = '<i class="fa fa-spinner fa-spin me-1"></i> Approving...';
              } else {
                rejectBtn.disabled = true;
                rejectBtn.innerHTML = '<i class="fa fa-spinner fa-spin me-1"></i> Rejecting...';
              }

              // For approve action, call GraphQL mutation first using ipt_home_graphql
              if (action === 'approve') {

                // Prepare GraphQL mutation
                const mutation = `
                  mutation Webhooks_templates_change_status($id: Int!, $status_id: Int!) {
                    webhooks_templates_change_status(
                      id: $id
                      body: {
                        status_id: $status_id
                      }
                    )
                  }
                `;

                const variables = {
                  id: parseInt(templateId),
                  status_id: 2
                };


                // Call GraphQL via ipt_home_graphql action
                jQuery.ajax({
                  url: '<?php echo admin_url('admin-ajax.php'); ?>',
                  type: 'POST',
                  dataType: 'json',
                  data: {
                    action: 'ipt_home_graphql',
                    query: mutation,
                    variables: JSON.stringify(variables)
                  },
                  success: function(response) {

                    if (response.errors && response.errors.length > 0) {
                      alert('Error updating template: ' + response.errors[0].message);
                      resetButtonStates();
                      return;
                    }

                    if (response.data) {
                      // GraphQL success, now call WordPress AJAX
                      callWordPressApproval();
                    } else {
                      alert('Invalid response from GraphQL server');
                      resetButtonStates();
                    }
                  },
                  error: function(xhr, status, error) {
                    alert('Failed to connect to GraphQL server: ' + error);
                    resetButtonStates();
                  }
                });
              } else {
                // For reject action, show modal for reason input
                showRejectModal();
              }

              // Function to call WordPress AJAX
              function callWordPressApproval() {

                jQuery.ajax({
                  url: '<?php echo admin_url('admin-ajax.php'); ?>',
                  type: 'POST',
                  data: {
                    action: 'ipt_handle_approval',
                    approval_action: action,
                    template_id: templateId,
                    page_url: currentUrl,
                    page_params: urlParams.toString(),
                    security: '<?php echo wp_create_nonce('ipt_approval_nonce'); ?>'
                  },
                  success: function(response) {
                    if (response.success) {
                      alert(`Template ${action}d successfully!`);

                      // Stay in approval mode - just reload the page to keep action=approve

                      // Small delay before reload
                      setTimeout(() => {
                        window.location.reload();
                      }, 500);
                    } else {
                      alert('Error: ' + response.data.message);
                    }
                  },
                  error: function(xhr, status, error) {
                    alert('Failed to process approval. Please try again.');
                    resetButtonStates();
                  }
                });
              }

              // Function to reset button states
              function resetButtonStates() {
                if (approveBtn) {
                  approveBtn.disabled = false;
                  approveBtn.innerHTML = '<i class="fa fa-check me-1"></i> Approve';
                }
                if (rejectBtn) {
                  rejectBtn.disabled = false;
                  rejectBtn.innerHTML = '<i class="fa fa-times me-1"></i> Reject';
                }
              }

              // Function to show reject modal
              function showRejectModal() {

                // Reset modal form
                document.getElementById('rejectionReason').value = '';
                document.getElementById('selectedFiles').innerHTML = '';
                document.getElementById('attachmentFiles').value = '';

                // Reset modal submit button
                const submitBtn = document.getElementById('submitRejection');
                if (submitBtn) {
                  submitBtn.disabled = false;
                  submitBtn.innerHTML = 'Submit';
                }

                // Show modal using Bootstrap
                const modal = new bootstrap.Modal(document.getElementById('rejectTemplateModal'));
                modal.show();
              }
            }
          }

          // Helper function to remove URL parameters
          function removeUrlParameter(url, parameter) {
            const urlParts = url.split('?');
            if (urlParts.length >= 2) {
              const prefix = encodeURIComponent(parameter) + '=';
              const pars = urlParts[1].split(/[&;]/g);

              for (let i = pars.length; i-- > 0;) {
                if (pars[i].lastIndexOf(prefix, 0) !== -1) {
                  pars.splice(i, 1);
                }
              }

              return urlParts[0] + (pars.length > 0 ? '?' + pars.join('&') : '');
            }
            return url;
          }

          // File upload handlers
          jQuery(document).ready(function($) {
            // File drop zone click handler
            $('#fileDropZone').on('click', function() {
              $('#attachmentFiles').click();
            });

            // File input change handler
            $('#attachmentFiles').on('change', function() {
              handleFileSelection(this.files);
            });

            // Drag and drop handlers
            $('#fileDropZone').on('dragover', function(e) {
              e.preventDefault();
              $(this).addClass('border-primary');
            });

            $('#fileDropZone').on('dragleave', function(e) {
              e.preventDefault();
              $(this).removeClass('border-primary');
            });

            $('#fileDropZone').on('drop', function(e) {
              e.preventDefault();
              $(this).removeClass('border-primary');
              handleFileSelection(e.originalEvent.dataTransfer.files);
            });

            // Submit rejection handler
            $('#submitRejection').on('click', function() {
              submitRejection();
            });

            // Modal close event handler
            $('#rejectTemplateModal').on('hidden.bs.modal', function() {

              // Reset reject button in topbar when modal is closed
              const rejectBtn = document.getElementById('reject-btn');
              if (rejectBtn) {
                rejectBtn.disabled = false;
                rejectBtn.innerHTML = '<i class="fa fa-times me-1"></i> Reject';
              }

              // Reset modal form
              document.getElementById('rejectionReason').value = '';
              document.getElementById('selectedFiles').innerHTML = '';
              document.getElementById('attachmentFiles').value = '';

              // Reset modal submit button
              const submitBtn = document.getElementById('submitRejection');
              if (submitBtn) {
                submitBtn.disabled = false;
                submitBtn.innerHTML = 'Submit';
              }
            });
          });

          function handleFileSelection(files) {
            const selectedFilesDiv = document.getElementById('selectedFiles');
            selectedFilesDiv.innerHTML = '';

            if (files.length === 0) return;

            for (let i = 0; i < files.length; i++) {
              const file = files[i];

              // Validate file size (10MB max)
              if (file.size > 10 * 1024 * 1024) {
                alert(`File "${file.name}" is too large. Maximum size is 10MB.`);
                continue;
              }

              // Create file item
              const fileItem = document.createElement('div');
              fileItem.className = 'border rounded p-2 mb-2 d-flex justify-content-between align-items-center';
              fileItem.innerHTML = `
                <div>
                  <i class="fa fa-file me-2"></i>
                  <span>${file.name}</span>
                  <small class="text-muted ms-2">(${(file.size / 1024 / 1024).toFixed(2)} MB)</small>
                </div>
                <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeFile(this)">
                  <i class="fa fa-times"></i>
                </button>
              `;

              selectedFilesDiv.appendChild(fileItem);
            }
          }

          function removeFile(button) {
            button.closest('.border').remove();
          }

          function submitRejection() {
            const reason = document.getElementById('rejectionReason').value.trim();
            const files = document.getElementById('attachmentFiles').files;


            if (!reason) {
              alert('Please enter a reason for rejection.');
              return;
            }

            // Show loading state
            const submitBtn = document.getElementById('submitRejection');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fa fa-spinner fa-spin me-1"></i> Submitting...';

            // Get template ID and other data
            const urlParams = new URLSearchParams(window.location.search);
            const templateId = urlParams.get('template_id');
            const currentUrl = window.location.href;

            if (!templateId) {
              alert('Error: Template ID not found in URL parameters.');
              resetRejectButton();
              return;
            }

            // First call GraphQL API to update template status to rejected (status_id = 3)

            const mutation = `
              mutation Webhooks_templates_change_status($id: Int!, $status_id: Int!) {
                webhooks_templates_change_status(
                  id: $id
                  body: {
                    status_id: $status_id
                  }
                )
              }
            `;

            const variables = {
              id: parseInt(templateId),
              status_id: 3
            };


            // Call GraphQL via ipt_home_graphql action
            jQuery.ajax({
              url: '<?php echo admin_url('admin-ajax.php'); ?>',
              type: 'POST',
              dataType: 'json',
              data: {
                action: 'ipt_home_graphql',
                query: mutation,
                variables: JSON.stringify(variables)
              },
              success: function(response) {

                if (response.errors && response.errors.length > 0) {
                  alert('Error updating template status: ' + response.errors[0].message);
                  resetRejectButton();
                  return;
                }

                if (response.data) {
                  // GraphQL success, now call WordPress AJAX with files
                  callWordPressRejection();
                } else {
                  alert('Invalid response from GraphQL server');
                  resetRejectButton();
                }
              },
              error: function(xhr, status, error) {
                alert('Failed to connect to GraphQL server: ' + error);
                resetRejectButton();
              }
            });

            // Function to call WordPress AJAX with file upload
            function callWordPressRejection() {

              // Prepare form data for file upload
              const formData = new FormData();
              formData.append('action', 'ipt_handle_approval');
              formData.append('approval_action', 'reject');
              formData.append('template_id', templateId);
              formData.append('rejection_reason', reason);
              formData.append('page_url', currentUrl);
              formData.append('security', '<?php echo wp_create_nonce('ipt_approval_nonce'); ?>');

              // Add files to form data
              for (let i = 0; i < files.length; i++) {
                formData.append('attachment_files[]', files[i]);
              }

              // Submit rejection via AJAX
              jQuery.ajax({
                url: '<?php echo admin_url('admin-ajax.php'); ?>',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {

                  if (response.success) {
                    alert('Template rejected successfully!');

                    // Reset reject button text to normal
                    const rejectBtn = document.getElementById('reject-btn');
                    if (rejectBtn) {
                      rejectBtn.disabled = false;
                      rejectBtn.innerHTML = '<i class="fa fa-times me-1"></i> Reject';
                    }

                    // Close modal
                    const modal = bootstrap.Modal.getInstance(document.getElementById('rejectTemplateModal'));
                    modal.hide();

                    // Stay in approval mode - just reload the page to keep action=approve

                    // Small delay to ensure modal closes properly before reload
                    setTimeout(() => {
                      window.location.reload();
                    }, 500);
                  } else {
                    alert('Error: ' + response.data.message);
                  }
                },
                error: function(xhr, status, error) {
                  alert('Failed to submit rejection. Please try again.');
                },
                complete: function() {
                  resetRejectButton();
                }
              });
            }

            // Function to reset reject button state
            function resetRejectButton() {
              // Reset modal submit button
              submitBtn.disabled = false;
              submitBtn.innerHTML = 'Submit';

              // Reset main reject button in topbar
              const rejectBtn = document.getElementById('reject-btn');
              if (rejectBtn) {
                rejectBtn.disabled = false;
                rejectBtn.innerHTML = '<i class="fa fa-times me-1"></i> Reject';
              }
            }
          }

          // Helper function to remove multiple URL parameters
          function removeUrlParameters(url, parametersToRemove) {
            let cleanUrl = url;
            parametersToRemove.forEach(param => {
              cleanUrl = removeUrlParameter(cleanUrl, param);
            });
            return cleanUrl;
          }
        </script>
        <script>
          // Sử dụng event delegation để xử lý các element được tạo qua AJAX

          // OLD ACCORDION EVENT LISTENERS - COMMENTED OUT (using new tagging-item design)
          /*
          // Xử lý click vào accordion title
          jQuery(document).on('click', '.accordion-title', function(e) {
            e.stopPropagation();

            // Lấy phần tử accordion cha
            var accordion = this.parentElement;

            // Scroll đến phần tử tương ứng
            var selector = accordion.getAttribute('data-selector');
            if (selector) {
              scrollToElement(selector);
            }
          });
          */

          // Xử lý click vào nút mũi tên
          jQuery(document).on('click', '.accordion-arrow', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // Lấy phần tử accordion cha
            var accordion = this.closest('.accordion');

            // Toggle class active
            accordion.classList.toggle("active");

            // Mở/đóng panel
            var panel = accordion.nextElementSibling;

            // Toggle class active cho panel
            panel.classList.toggle("active");
          });

          // Xử lý click vào nút xóa
          jQuery(document).on('click', '.accordion-delete', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // Lấy phần tử accordion cha
            var accordion = this.closest('.accordion');
            var index = accordion.getAttribute('data-index');
            var selector = accordion.getAttribute('data-selector');

            if (confirm('Are you sure you want to delete this tag?')) {
              // Gửi AJAX để xóa tag
              jQuery.post(designerTagging.ajax_url, {
                action: 'designer_remove_editable',
                nonce: designerTagging.nonce,
                post_id: designerTagging.post_id,
                index: index
              }, function(response) {
                if (response.success) {
                  // Xóa accordion và panel khỏi DOM
                  var panel = accordion.nextElementSibling;
                  accordion.remove();
                  panel.remove();

                  // Xóa highlight và overlay trên phần tử
                  try {
                    var $el = jQuery(selector);
                    if ($el.length) {
                      $el.removeClass('designer-highlight');
                      $el.removeAttr('data-designer-editable');

                      // Chỉ xóa overlay nếu là iframe hoặc video
                      if ($el.is('iframe') || $el.is('video')) {
                        $el.removeAttr('data-overlay-applied');
                        // Xóa overlay nếu có
                        var $parent = $el.parent();
                        $parent.find('.tagged-media-overlay').remove();
                      }
                    }
                  } catch(e) {
                  }

                  // alert('Tag deleted successfully!');
                  showToast('Tag deleted successfully!', 'success');
                } else {
                  // alert('Error: ' + (response.data || 'Unable to delete tag'));
                  showToast('Error: ' + (response.data || 'Unable to delete tag'), 'error');
                }
              });
            }
          });

          function scrollToElement(selector) {
            try {
              console.log('🔍 Attempting to scroll to selector:', selector);
              var $el = jQuery(selector);
              console.log('📍 Found elements:', $el.length);

              if ($el.length) {
                console.log('✅ Element found, scrolling...');
                // Remove previous highlights immediately
                jQuery('.designer-highlight-active').removeClass('designer-highlight-active');

                // Add highlight to current element
                $el.addClass('designer-highlight-active');

                // Much faster scroll - reduced from 500ms to 200ms
                jQuery('html, body').animate({
                  scrollTop: $el.offset().top - 100
                }, 200, function() {
                  console.log('✅ Scroll completed');
                  // Quick flash - reduced from 2000ms to 600ms total
                  setTimeout(function() {
                    $el.removeClass('designer-highlight-active');
                  }, 600);
                });
              } else {
                console.log('❌ No elements found for selector:', selector);
                // Try alternative selectors if the main one fails
                var alternativeSelectors = [
                  selector.replace(/\[data-id="([^"]+)"\]\s*/, '[data-id="$1"] '),
                  selector.replace(/\s+/g, ' ').trim(),
                  selector.split(' ').pop() // Try just the last part
                ];

                for (var i = 0; i < alternativeSelectors.length; i++) {
                  var altSelector = alternativeSelectors[i];
                  if (altSelector !== selector) {
                    console.log('🔄 Trying alternative selector:', altSelector);
                    var $altEl = jQuery(altSelector);
                    if ($altEl.length) {
                      console.log('✅ Alternative selector worked!');
                      $altEl.addClass('designer-highlight-active');
                      jQuery('html, body').animate({
                        scrollTop: $altEl.offset().top - 100
                      }, 200, function() {
                        setTimeout(function() {
                          $altEl.removeClass('designer-highlight-active');
                        }, 600);
                      });
                      return;
                    }
                  }
                }
                console.log('❌ All selector attempts failed');
              }
            } catch(e) {
              console.error('❌ Error in scrollToElement:', e);
            }
          }

          // Function to scroll to sidebar item and focus input
          function scrollToSidebarItem(selector) {
            try {

              // Find the input field with matching selector
              var $input = jQuery('.customer-editable-field[data-selector="' + selector + '"]');

              if ($input.length) {
                // Find the sidebar container and content area
                var $sidebar = jQuery('#mySidebar');
                var $contentForm = $sidebar.find('.content-form');

                // Find the tagging item container
                var $taggingItem = $input.closest('.tagging-item');

                if ($taggingItem.length) {
                  // Scroll the sidebar to show the item
                  if ($contentForm.length) {
                    // Calculate scroll position within sidebar content
                    var contentScrollTop = $contentForm.scrollTop();
                    var contentTop = $contentForm.offset().top;
                    var itemTop = $taggingItem.offset().top;
                    var targetScrollTop = contentScrollTop + (itemTop - contentTop) - 100;

                    // Scroll content area to the item
                    $contentForm.animate({
                      scrollTop: targetScrollTop
                    }, 300);
                  } else {
                    // Fallback: scroll the entire sidebar
                    var sidebarScrollTop = $sidebar.scrollTop();
                    var sidebarTop = $sidebar.offset().top;
                    var itemTop = $taggingItem.offset().top;
                    var targetScrollTop = sidebarScrollTop + (itemTop - sidebarTop) - 100;

                    $sidebar.animate({
                      scrollTop: targetScrollTop
                    }, 300);
                  }

                  // Highlight the tagging item briefly
                  $taggingItem.addClass('sidebar-item-highlight');
                  setTimeout(function() {
                    $taggingItem.removeClass('sidebar-item-highlight');
                  }, 1500);

                  // Focus on the input field after scroll animation
                  setTimeout(function() {
                    $input.focus().select();
                  }, 400);

                } else {
                }
              } else {
                // Debug: show all available selectors
                var allInputs = jQuery('.customer-editable-field');
                
              }
            } catch(e) {
            }
          }

          jQuery(function($){
            // Mapping field name <-> selector từ PHP sang JS
            var designerFieldMap = <?php
                $map = [];
                foreach ($fields as $f) {
                    $map['designer_' . md5($f['selector'])] = $f['selector'];
                }
                echo json_encode($map);
            ?>;

            // Highlight tất cả vùng đã đánh dấu khi load trang
            var selectors = Object.values(designerFieldMap);
            selectors.forEach(function(selector){
                try {
                    var $el = $(document).find(selector).first();
                    if ($el.length) {
                        $el.addClass('designer-highlight');
                        
                        // Kiểm tra nếu là video hoặc iframe
                        if ($el.is('iframe') || $el.is('video')) {
                            
                            // Đảm bảo parent có position relative
                            var $parent = $el.parent();
                            if ($parent.css('position') !== 'relative' && 
                                $parent.css('position') !== 'absolute' && 
                                $parent.css('position') !== 'fixed') {
                                $parent.css('position', 'relative');
                            }
                            
                            // Xóa overlay cũ nếu có
                            $parent.find('.tagged-media-overlay').remove();
                            
                            // Tạo overlay cố định với label "Already Tagged"
                            var $taggedOverlay = $('<div class="tagged-media-overlay"></div>');
                            
                            // Tính toán vị trí và kích thước
                            var elOffset = $el.position();
                            var width = $el.outerWidth();
                            var height = $el.outerHeight();
                            
                            // Đảm bảo kích thước hợp lệ
                            if (width <= 0) width = $el.width();
                            if (height <= 0) height = $el.height();
                            
                            // Nếu vẫn không có kích thước hợp lệ, thử lấy từ style
                            if (width <= 0) width = parseInt($el.css('width')) || 300;
                            if (height <= 0) height = parseInt($el.css('height')) || 150;
                            
                            
                            $taggedOverlay.css({
                                position: 'absolute',
                                top: elOffset.top,
                                left: elOffset.left,
                                width: width,
                                height: height,
                                background: 'rgba(37, 99, 235, 0.2)',
                                border: '2px solid #2563eb',
                                zIndex: 9990,
                                display: 'flex',
                                justifyContent: 'center',
                                alignItems: 'center',
                                pointerEvents: 'all' // Chặn tương tác với phần tử bên dưới
                            });
                            
                            // Thêm label "Already Tagged"
                            var $tagLabel = $('<div class="tagged-label"></div>').text('Already Tagged');
                            $tagLabel.css({
                                background: 'rgba(0, 0, 0, 0.7)',
                                color: 'white',
                                padding: '5px 10px',
                                borderRadius: '4px',
                                fontWeight: 'bold'
                            });
                            
                            $taggedOverlay.append($tagLabel);
                            $parent.append($taggedOverlay);
                            
                            // Lưu tham chiếu đến phần tử gốc
                            $taggedOverlay.data('target-element', $el[0]);
                            
                            // Đánh dấu đã xử lý
                            $el.attr('data-overlay-applied', 'true');
                        }
                    }
                } catch(e) {
                }
            });

            // Hàm highlight và scroll tới vùng
            function highlightAndScroll(selector) {
                var $el = null;
                try {
                    $el = $(document).find(selector).first();
                } catch(e) {}
                if ($el && $el.length) {
                    // Xóa highlight cũ
                    $('.designer-highlight').removeClass('designer-highlight');
                    // Thêm highlight mới
                    $el.addClass('designer-highlight');
                    // Scroll tới vùng
                    $('html, body').animate({
                        scrollTop: $el.offset().top - 100 // trừ đi header nếu có
                    }, 500);
                    // Tự động bỏ highlight sau 2s (nếu muốn)
                    setTimeout(function(){
                        $el.removeClass('designer-highlight');
                    }, 2000);
                }
            }

            // Lắng nghe click vào input của form
            $('#designer-sidebar .acf-field input, #designer-sidebar .acf-field textarea, #designer-sidebar .acf-field select').on('focus click', function(){
                var fieldName = $(this).attr('name');
                if (designerFieldMap[fieldName]) {
                    highlightAndScroll(designerFieldMap[fieldName]);
                }
            });

            // Add click handlers to highlighted elements for bidirectional navigation
            // This allows clicking on highlighted elements in layout to focus sidebar inputs
            $(document).on('click', '.designer-highlight', function(e) {
                e.preventDefault();
                e.stopPropagation();


                // Find the selector for this element
                var clickedElement = this;
                var foundSelector = null;

                // Method 1: Check all selectors to find which one matches this element
                selectors.forEach(function(selector) {
                    if (foundSelector) return; // Already found

                    try {
                        var $elements = $(selector);
                        $elements.each(function() {
                            if (this === clickedElement) {
                                foundSelector = selector;
                                return false; // break out of each loop
                            }
                        });
                    } catch(e) {
                        // Ignore selector errors
                    }
                });

                // Method 2: If not found, try to get selector from data attribute or other methods
                if (!foundSelector) {
                    // Check if element has a data-selector attribute
                    var dataSelector = $(clickedElement).attr('data-selector');
                    if (dataSelector) {
                        foundSelector = dataSelector;
                    }
                }

                if (foundSelector) {
                    scrollToSidebarItem(foundSelector);
                } else {
                }
            });

            // Also add click handlers to tagged media overlays
            $(document).on('click', '.tagged-media-overlay', function(e) {
                e.preventDefault();
                e.stopPropagation();


                // Get the target element from the overlay
                var targetElement = $(this).data('target-element');

                if (targetElement) {
                    // Find the selector for this target element
                    var foundSelector = null;
                    selectors.forEach(function(selector) {
                        if (foundSelector) return; // Already found

                        try {
                            var $elements = $(selector);
                            $elements.each(function() {
                                if (this === targetElement) {
                                    foundSelector = selector;
                                    return false;
                                }
                            });
                        } catch(e) {
                            // Ignore selector errors
                        }
                    });

                    if (foundSelector) {
                        scrollToSidebarItem(foundSelector);
                    } else {
                    }
                } else {
                }
            });


          });
        </script>

    <?php
});


// include_once plugin_dir_path(__FILE__) . 'dashboard/designer_func.php';

// Thêm shortcode để test AJAX
add_shortcode('test_designer_ajax', function() {
    if (!current_user_can('edit_posts')) {
        return 'You need permission to test this.';
    }

    $nonce = wp_create_nonce('designer_tagging_nonce');
    $post_id = get_the_ID() ?: 1;

    ob_start();
    ?>
    <div id="ajax-test">
        <h3>Test Designer AJAX</h3>
        <button id="test-save-tag" class="button">Test Save Tag</button>
        <div id="ajax-result" style="margin-top: 10px; padding: 10px; border: 1px solid #ccc;"></div>
    </div>

    <script>
    jQuery(document).ready(function($) {
        $('#test-save-tag').click(function() {
            $('#ajax-result').html('Testing...');

            $.post('<?php echo admin_url('admin-ajax.php'); ?>', {
                action: 'save_designer_tag',
                nonce: '<?php echo $nonce; ?>',
                post_id: <?php echo $post_id; ?>,
                selector: 'h1.test-selector',
                label: 'Test Label',
                type: 'text',
                section: 'section1',
                value: 'Test Value',
                additional_data: '{}'
            })
            .done(function(response) {
                $('#ajax-result').html('<strong>Success:</strong><br><pre>' + JSON.stringify(response, null, 2) + '</pre>');
            })
            .fail(function(xhr, status, error) {
                $('#ajax-result').html('<strong>Error:</strong><br>Status: ' + xhr.status + '<br>Response: ' + xhr.responseText);
            });
        });
    });
    </script>
    <?php
    return ob_get_clean();
});

// Thêm hàm xử lý AJAX để xóa tất cả tag
add_action('wp_ajax_designer_clear_all_tags', function() {
    check_ajax_referer('designer_tagging_nonce', 'nonce');
    $post_id = intval($_POST['post_id']);

    // Xóa tất cả tag bằng cách xóa post meta
    delete_post_meta($post_id, '_designer_editable_fields');

    wp_send_json_success([
        'message' => 'All tags cleared successfully!',
        'tagged_fields_html' => '<p class="text-muted text-center py-3">No tags available. Start tagging elements on the page.</p>'
    ]);
});

add_action('wp_ajax_nopriv_designer_clear_all_tags', function() {
    check_ajax_referer('designer_tagging_nonce', 'nonce');
    $post_id = intval($_POST['post_id']);

    // Xóa tất cả tag bằng cách xóa post meta
    delete_post_meta($post_id, '_designer_editable_fields');

    wp_send_json_success([
        'message' => 'All tags cleared successfully!',
        'tagged_fields_html' => '<p class="text-muted text-center py-3">No tags available. Start tagging elements on the page.</p>'
    ]);
});

// AJAX handler để lưu thay đổi của customer
add_action('wp_ajax_save_customer_changes', function() {
    check_ajax_referer('designer_tagging_nonce', 'nonce');

    $post_id = intval($_POST['post_id']);
    $changes = json_decode(stripslashes($_POST['changes']), true);

    if (empty($changes)) {
        wp_send_json_error('No changes provided');
        return;
    }

    // Lấy các field hiện tại
    $fields = get_post_meta($post_id, '_designer_editable_fields', true);
    if (!$fields) {
        wp_send_json_error('No tagged fields found');
        return;
    }

    // Cập nhật các field với giá trị mới
    foreach ($changes as $change) {
        $selector = $change['selector'];
        $fieldType = $change['fieldType'];
        $fieldSubtype = isset($change['fieldSubtype']) ? $change['fieldSubtype'] : '';
        $newValue = $change['newValue'];

        // Tìm field tương ứng và cập nhật
        for ($i = 0; $i < count($fields); $i++) {
            if ($fields[$i]['selector'] === $selector) {
                if ($fieldType === 'button' && $fieldSubtype === 'url') {
                    // Cập nhật URL cho button
                    $additional_data = json_decode(isset($fields[$i]['additional_data']) ? $fields[$i]['additional_data'] : '{}', true);
                    $additional_data['url'] = $newValue;
                    $fields[$i]['additional_data'] = json_encode($additional_data);
                } else {
                    // Cập nhật value thông thường
                    $fields[$i]['value'] = $newValue;
                }
                break;
            }
        }
    }

    // Lưu lại vào database
    update_post_meta($post_id, '_designer_editable_fields', $fields);

    wp_send_json_success([
        'message' => 'Customer changes saved successfully!',
        'changes_count' => count($changes)
    ]);
});

add_action('wp_ajax_nopriv_save_customer_changes', function() {
    check_ajax_referer('designer_tagging_nonce', 'nonce');

    $post_id = intval($_POST['post_id']);
    $changes = json_decode(stripslashes($_POST['changes']), true);

    if (empty($changes)) {
        wp_send_json_error('No changes provided');
        return;
    }

    // Lấy các field hiện tại
    $fields = get_post_meta($post_id, '_designer_editable_fields', true);
    if (!$fields) {
        wp_send_json_error('No tagged fields found');
        return;
    }

    // Cập nhật các field với giá trị mới
    foreach ($changes as $change) {
        $selector = $change['selector'];
        $fieldType = $change['fieldType'];
        $fieldSubtype = isset($change['fieldSubtype']) ? $change['fieldSubtype'] : '';
        $newValue = $change['newValue'];

        // Tìm field tương ứng và cập nhật
        for ($i = 0; $i < count($fields); $i++) {
            if ($fields[$i]['selector'] === $selector) {
                if ($fieldType === 'button' && $fieldSubtype === 'url') {
                    // Cập nhật URL cho button
                    $additional_data = json_decode(isset($fields[$i]['additional_data']) ? $fields[$i]['additional_data'] : '{}', true);
                    $additional_data['url'] = $newValue;
                    $fields[$i]['additional_data'] = json_encode($additional_data);
                } else {
                    // Cập nhật value thông thường
                    $fields[$i]['value'] = $newValue;
                }
                break;
            }
        }
    }

    // Lưu lại vào database
    update_post_meta($post_id, '_designer_editable_fields', $fields);

    wp_send_json_success([
        'message' => 'Customer changes saved successfully!',
        'changes_count' => count($changes)
    ]);
});

// Thêm hàm xử lý AJAX để xóa tất cả section
add_action('wp_ajax_designer_clear_all_sections', function() {
    check_ajax_referer('designer_tagging_nonce', 'nonce');
    $post_id = intval($_POST['post_id']);

    global $wpdb;
    $table_name = $wpdb->prefix . 'designer_sections';

    // Xóa tất cả section của post hiện tại
    $wpdb->delete(
        $table_name,
        ['post_id' => $post_id],
        ['%d']
    );

    // Tạo section mặc định
    $wpdb->insert(
        $table_name,
        [
            'post_id' => $post_id,
            'section_key' => 'section_default',
            'section_name' => 'Section Default',
            'section_order' => 0
        ]
    );

    // Lấy danh sách section mới (chỉ có section mặc định)
    $sections = get_designer_sections($post_id);

    // Tạo HTML cho dropdown section
    $sections_html = '';
    foreach ($sections as $section) {
        $sections_html .= '<option value="' . esc_attr($section['section_key']) . '">' . esc_html($section['section_name']) . '</option>';
    }

    // Cập nhật tất cả tag hiện có sang section mặc định
    $fields = get_post_meta($post_id, '_designer_editable_fields', true);
    if (is_array($fields)) {
        foreach ($fields as &$field) {
            $field['section'] = 'section_default';
        }
        update_post_meta($post_id, '_designer_editable_fields', $fields);
    }

    wp_send_json_success([
        'message' => 'All sections cleared successfully! All tags moved to default section.',
        'sections_html' => $sections_html
    ]);
});

add_action('wp_ajax_nopriv_designer_clear_all_sections', function() {
    check_ajax_referer('designer_tagging_nonce', 'nonce');
    $post_id = intval($_POST['post_id']);
    
    global $wpdb;
    $table_name = $wpdb->prefix . 'designer_sections';
    
    // Xóa tất cả section của post hiện tại
    $wpdb->delete(
        $table_name,
        ['post_id' => $post_id],
        ['%d']
    );
    
    // Tạo section mặc định
    $wpdb->insert(
        $table_name,
        [
            'post_id' => $post_id,
            'section_key' => 'section_default',
            'section_name' => 'Section Default',
            'section_order' => 0
        ]
    );
    
    // Lấy danh sách section mới (chỉ có section mặc định)
    $sections = get_designer_sections($post_id);
    
    // Tạo HTML cho dropdown section
    $sections_html = '';
    foreach ($sections as $section) {
        $sections_html .= '<option value="' . esc_attr($section['section_key']) . '">' . esc_html($section['section_name']) . '</option>';
    }
    
    // Cập nhật tất cả tag hiện có sang section mặc định
    $fields = get_post_meta($post_id, '_designer_editable_fields', true);
    if (is_array($fields)) {
        foreach ($fields as &$field) {
            $field['section'] = 'section_default';
        }
        update_post_meta($post_id, '_designer_editable_fields', $fields);
    }
    
    wp_send_json_success([
        'message' => 'All sections cleared successfully! All tags moved to default section.',
        'sections_html' => $sections_html
    ]);
});

// Thêm hàm xử lý AJAX để lấy danh sách tag đã được cập nhật
add_action('wp_ajax_designer_get_tagged_fields', function() {
    check_ajax_referer('designer_tagging_nonce', 'nonce');
    $post_id = intval($_POST['post_id']);

    $fields = get_post_meta($post_id, '_designer_editable_fields', true);

    ob_start();

    if (!empty($fields)) {
        // Gom nhóm các tag theo section
        $grouped_fields = [];
        foreach ($fields as $i => $f) {
            $section_key = !empty($f['section']) ? $f['section'] : 'section_default';
            if (!isset($grouped_fields[$section_key])) {
                $grouped_fields[$section_key] = [];
            }
            $f['index'] = $i; // Lưu index gốc để xóa đúng
            $grouped_fields[$section_key][] = $f;
        }

        // Hiển thị các tag theo nhóm section
        foreach ($grouped_fields as $section_key => $section_fields) {
            $section_name = '';

            // Lấy tên section từ database
            global $wpdb;
            $table_name = $wpdb->prefix . 'designer_sections';
            $section_data = $wpdb->get_row(
                $wpdb->prepare(
                    "SELECT section_name FROM $table_name WHERE section_key = %s AND post_id = %d",
                    $section_key,
                    $post_id
                )
            );

            if ($section_data) {
                $section_name = $section_data->section_name;
            } else {
                // Fallback nếu không tìm thấy trong database
                $section_name = get_section_name_by_key($section_key, $post_id);
            }
            ?>
            <div class="section-header">
                <h4><?php echo esc_html($section_name); ?></h4>
            </div>
            <?php foreach ($section_fields as $f): ?>
              <div class="accordion" data-selector="<?php echo esc_attr($f['selector']); ?>" data-index="<?php echo $f['index']; ?>">
                <span class="accordion-title"><?php echo esc_html($f['label']); ?> (<?php echo esc_html($f['type']); ?>)</span>
                <span class="accordion-actions">
                  <span class="accordion-delete" title="Delete tag"><i class="fa fa-trash"></i></span>
                  <span class="accordion-arrow"><i class="fa fa-chevron-down"></i></span>
                </span>
              </div>
              <div class="panel">
                <div class="tag-head mb-3">
                  <div class="form-group">
                        <label for="title">Title:</label>
                        <input type="text" value="<?php echo esc_html($f['label']); ?>">
                    </div>
                    <div class="form-group">
                        <label for="title">Tooltip:</label>
                        <input type="text" value="Add your <?php echo esc_html($f['label']); ?>">
                    </div>
                    <?php
                    switch($f['type']){
                      case 'text':
                        ?>
                          <div class="form-group">
                              <label for="title">Value:</label>
                              <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> value">
                          </div>
                        <?php
                        break;
                      case 'image':
                        ?>
                          <div class="form-group">
                              <label for="tooltip">Value URL:</label>
                              <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> URL">
                          </div>
                        <?php
                        break;
                      case 'link':
                        ?>
                          <div class="form-group">
                              <label for="tooltip">Value URL:</label>
                              <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> URL">
                          </div>
                        <?php
                        break;
                      case 'button':
                        $additional = json_decode(isset($f['additional_data']) ? $f['additional_data'] : '{}', true);
                        $button_url = isset($additional['url']) ? $additional['url'] : '';
                        ?>
                          <div class="form-group">
                              <label for="title">Label:</label>
                              <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> value">
                          </div>
                          <div class="form-group">
                              <label for="title">URL:</label>
                              <input type="text" readonly value="<?php echo esc_attr($button_url); ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> URL">
                          </div>
                        <?php
                        break;
                      case 'video':
                        ?>
                          <div class="form-group">
                              <label for="tooltip">Value URL:</label>
                              <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> URL">
                          </div>
                        <?php
                        break;
                      case 'iframe':
                        ?>
                          <div class="form-group">
                              <label for="title">URL:</label>
                              <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> URL">
                          </div>
                        <?php
                        break;
                      case 'progress':
                        $percent = isset($f['value']) ? intval($f['value']) : 0;
                        ?>
                          <div class="form-group">
                              <label for="title">Label:</label>
                              <input type="text" readonly value="<?php echo isset($f['label']) ? esc_attr($f['label']) : ''; ?>">
                          </div>
                          <div class="form-group">
                              <label for="title">Percentage:</label>
                              <input type="text" readonly value="<?php echo $percent; ?>%">
                          </div>

                        <?php
                        break;
                      case 'default':
                        ?>
                          <div class="form-group">
                              <label for="title">Value:</label>
                              <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> value">
                          </div>
                        <?php
                        break;
                    }
                  ?>
                </div>
              </div>
          <?php endforeach; ?>
          <?php }
      } else {
          echo '<p class="text-muted text-center py-3">No tags available. Start tagging elements on the page.</p>';
      }

    $tagged_fields_html = ob_get_clean();

    wp_send_json_success([
        'tagged_fields_html' => $tagged_fields_html
    ]);
});

add_action('wp_ajax_nopriv_designer_get_tagged_fields', function() {
    check_ajax_referer('designer_tagging_nonce', 'nonce');
    $post_id = intval($_POST['post_id']);
    
    $fields = get_post_meta($post_id, '_designer_editable_fields', true);
    
    ob_start();
    
    if (!empty($fields)) {
        // Gom nhóm các tag theo section
        $grouped_fields = [];
        foreach ($fields as $i => $f) {
            $section_key = !empty($f['section']) ? $f['section'] : 'section_default';
            if (!isset($grouped_fields[$section_key])) {
                $grouped_fields[$section_key] = [];
            }
            $f['index'] = $i; // Lưu index gốc để xóa đúng
            $grouped_fields[$section_key][] = $f;
        }
        
        // Hiển thị các tag theo nhóm section
        foreach ($grouped_fields as $section_key => $section_fields) {
            $section_name = '';
            
            // Lấy tên section từ database
            global $wpdb;
            $table_name = $wpdb->prefix . 'designer_sections';
            $section = $wpdb->get_row(
                $wpdb->prepare(
                    "SELECT section_name FROM $table_name WHERE section_key = %s AND post_id = %d",
                    $section_key,
                    $post_id
                )
            );
            
            if ($section) {
                $section_name = $section->section_name;
            } else {
                // Fallback nếu không tìm thấy section trong database
                $section_name = get_section_name_by_key($section_key, $post_id);
            }
            ?>
            <div class="section-header">
                <h4><?php echo esc_html($section_name); ?></h4>
            </div>
            <?php foreach ($section_fields as $f): ?>
              <div class="accordion" data-selector="<?php echo esc_attr($f['selector']); ?>" data-index="<?php echo $f['index']; ?>">
                <span class="accordion-title"><?php echo esc_html($f['label']); ?> (<?php echo esc_html($f['type']); ?>)</span>
                <span class="accordion-actions">
                  <span class="accordion-delete" title="Delete tag"><i class="fa fa-trash"></i></span>
                  <span class="accordion-arrow"><i class="fa fa-chevron-down"></i></span>
                </span>
              </div>
              <div class="panel">
                <div class="tag-head mb-3">
                  <div class="form-group">
                        <label for="title">Title:</label>
                        <input type="text" value="<?php echo esc_html($f['label']); ?>">
                    </div>
                    <div class="form-group">
                        <label for="title">Tooltip:</label>
                        <input type="text" value="Add your <?php echo esc_html($f['label']); ?>">
                    </div>
                    <?php 
                    switch($f['type']){ 
                      case 'text':
                        ?>
                          <div class="form-group">
                              <label for="title">Value:</label>
                              <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> value">
                          </div>
                        <?php
                        break;
                      case 'image':
                        ?>
                          <div class="form-group">
                              <label for="tooltip">Value URL:</label>
                              <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> URL">
                          </div>
                        <?php
                        break;
                      case 'button':
                        $additional = json_decode(isset($f['additional_data']) ? $f['additional_data'] : '{}', true);
                        $button_url = isset($additional['url']) ? $additional['url'] : '';
                        ?>
                          <div class="form-group">
                              <label for="title">Label:</label>
                              <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> value">
                          </div>
                          <div class="form-group">
                              <label for="title">URL:</label>
                              <input type="text" readonly value="<?php echo esc_attr($button_url); ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> URL">
                          </div>
                        <?php
                        break;
                      case 'video':
                        ?>
                          <div class="form-group">
                              <label for="tooltip">Value URL:</label>
                              <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> URL">
                          </div>
                        <?php
                        break;
                      case 'iframe':
                        ?>
                          <div class="form-group">
                              <label for="title">URL:</label>
                              <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> URL">
                          </div>
                        <?php
                        break;
                      case 'progress':
                        $percent = isset($f['value']) ? intval($f['value']) : 0;
                        ?>
                          <div class="form-group">
                              <label for="title">Label:</label>
                              <input type="text" readonly value="<?php echo isset($f['label']) ? esc_attr($f['label']) : ''; ?>">
                          </div>
                          <div class="form-group">
                              <label for="title">Percentage:</label>
                              <input type="text" readonly value="<?php echo $percent; ?>%">
                          </div>
                        <?php
                        break;
                      case 'default':
                        ?>
                          <div class="form-group">
                              <label for="title">Value:</label>
                              <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> value">
                          </div>
                        <?php
                        break;
                    }
                  ?>
                </div>
              </div>
          <?php endforeach; ?>
        <?php }
    } else {
        echo '<p class="text-muted text-center py-3">No tags available. Start tagging elements on the page.</p>';
    }
    
    $tagged_fields_html = ob_get_clean();
    
    wp_send_json_success([
        'tagged_fields_html' => $tagged_fields_html
    ]);
});

/**
 * Hide admin toolbar for all user roles
 */
add_action('after_setup_theme', function() {
    // Hide admin toolbar for all users (including administrators)
    show_admin_bar(false);
});

// Additional filter to ensure admin bar is completely disabled
add_filter('show_admin_bar', '__return_false');

// Thêm AJAX endpoint để cập nhật thông tin người dùng WordPress
add_action('wp_ajax_update_user_profile', 'designer_update_user_profile');
add_action('wp_ajax_nopriv_update_user_profile', 'designer_update_user_profile');

function designer_update_user_profile() {
    // Kiểm tra nonce bảo mật
    check_ajax_referer('update_user_profile_nonce', 'security');
    
    // Lấy ID người dùng hiện tại
    $user_id = get_current_user_id();
    
    // Kiểm tra xem người dùng đã đăng nhập chưa
    if (!$user_id) {
        wp_send_json_error(['message' => 'User not logged in']);
        return;
    }
    
    // Cập nhật thông tin người dùng
    $first_name = isset($_POST['first_name']) ? sanitize_text_field($_POST['first_name']) : '';
    $last_name = isset($_POST['last_name']) ? sanitize_text_field($_POST['last_name']) : '';
    $phone = isset($_POST['phone']) ? sanitize_text_field($_POST['phone']) : '';
    $password = isset($_POST['password']) ? $_POST['password'] : '';
    
    // Cập nhật thông tin cơ bản
    if (!empty($first_name)) {
        update_user_meta($user_id, 'first_name', $first_name);
    }
    
    if (!empty($last_name)) {
        update_user_meta($user_id, 'last_name', $last_name);
    }
    
    if (!empty($phone)) {
        update_user_meta($user_id, 'phone', $phone);
    }
    
    // Cập nhật mật khẩu nếu có
    if (!empty($password)) {
        wp_update_user([
            'ID' => $user_id,
            'user_pass' => $password
        ]);
    }
    
    wp_send_json_success(['message' => 'User profile updated successfully']);
}

// // Thêm endpoint để kiểm tra quyền hạn của các role
// add_action('init', 'designer_tagging_add_permission_check_endpoint');
// function designer_tagging_add_permission_check_endpoint() {
//     add_rewrite_rule(
//         'designer-permission-check/?$',
//         'index.php?designer_permission_check=1',
//         'top'
//     );
//     add_rewrite_tag('%designer_permission_check%', '([^&]+)');
// }

// // Xử lý hiển thị trang kiểm tra quyền hạn
// add_action('template_redirect', 'designer_tagging_handle_permission_check');
// function designer_tagging_handle_permission_check() {
//     if (get_query_var('designer_permission_check')) {
//         // Kiểm tra xem người dùng có quyền quản trị không
//         if (!current_user_can('manage_options')) {
//             wp_die('Bạn không có quyền truy cập trang này.', 'Lỗi quyền truy cập', array('response' => 403));
//         }
        
//         // Hiển thị thông tin về quyền hạn
//         header('Content-Type: text/html; charset=utf-8');
        
//         echo '<!DOCTYPE html>
//         <html>
//         <head>
//             <meta charset="UTF-8">
//             <meta name="viewport" content="width=device-width, initial-scale=1.0">
//             <title>Kiểm tra quyền hạn WordPress</title>
//             <style>
//                 body {
//                     font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
//                     line-height: 1.6;
//                     color: #444;
//                     max-width: 1200px;
//                     margin: 0 auto;
//                     padding: 20px;
//                 }
//                 h1, h2, h3 {
//                     color: #23282d;
//                 }
//                 table {
//                     border-collapse: collapse;
//                     width: 100%;
//                     margin-bottom: 20px;
//                 }
//                 th, td {
//                     border: 1px solid #ddd;
//                     padding: 8px;
//                     text-align: left;
//                 }
//                 th {
//                     background-color: #f1f1f1;
//                 }
//                 tr:nth-child(even) {
//                     background-color: #f9f9f9;
//                 }
//                 .role-name {
//                     font-weight: bold;
//                     color: #0073aa;
//                 }
//                 .capability-true {
//                     color: green;
//                     font-weight: bold;
//                 }
//                 .capability-false {
//                     color: red;
//                 }
//                 .section {
//                     margin-bottom: 30px;
//                     padding: 20px;
//                     background-color: #fff;
//                     border: 1px solid #ddd;
//                     border-radius: 3px;
//                     box-shadow: 0 1px 3px rgba(0,0,0,0.1);
//                 }
//                 .current-user {
//                     background-color: #f7fcfe;
//                     border-left: 4px solid #00a0d2;
//                     padding: 12px;
//                     margin-bottom: 20px;
//                 }
//                 .security-note {
//                     background-color: #fff8e5;
//                     border-left: 4px solid #ffb900;
//                     padding: 12px;
//                     margin-top: 20px;
//                 }
//             </style>
//         </head>
//         <body>
//             <h1>Kiểm tra quyền hạn WordPress</h1>
            
//             <div class="current-user">
//                 <h2>Thông tin người dùng hiện tại</h2>';
                
//                 $current_user = wp_get_current_user();
//                 echo '<p><strong>Username:</strong> ' . esc_html($current_user->user_login) . '</p>';
//                 echo '<p><strong>Email:</strong> ' . esc_html($current_user->user_email) . '</p>';
//                 echo '<p><strong>Roles:</strong> ' . esc_html(implode(', ', $current_user->roles)) . '</p>';
                
//                 echo '<h3>Các quyền hạn của người dùng hiện tại:</h3>';
//                 echo '<ul>';
                
//                 $all_caps = array(
//                     'edit_posts' => 'Chỉnh sửa bài viết',
//                     'publish_posts' => 'Xuất bản bài viết',
//                     'edit_published_posts' => 'Chỉnh sửa bài viết đã xuất bản',
//                     'delete_posts' => 'Xóa bài viết',
//                     'manage_options' => 'Quản lý tùy chọn',
//                     'upload_files' => 'Tải lên tập tin',
//                     'edit_theme_options' => 'Chỉnh sửa tùy chọn giao diện',
//                     'install_plugins' => 'Cài đặt plugins',
//                     'activate_plugins' => 'Kích hoạt plugins',
//                     'edit_plugins' => 'Chỉnh sửa plugins',
//                     'edit_users' => 'Chỉnh sửa người dùng',
//                     'create_users' => 'Tạo người dùng',
//                     'delete_users' => 'Xóa người dùng',
//                     'list_users' => 'Liệt kê người dùng',
//                     'read' => 'Đọc',
//                     'read_private_posts' => 'Đọc bài viết riêng tư',
//                     'edit_private_posts' => 'Chỉnh sửa bài viết riêng tư',
//                     'delete_private_posts' => 'Xóa bài viết riêng tư',
//                     'manage_categories' => 'Quản lý chuyên mục',
//                     'moderate_comments' => 'Kiểm duyệt bình luận',
//                     'edit_others_posts' => 'Chỉnh sửa bài viết của người khác',
//                     'delete_others_posts' => 'Xóa bài viết của người khác',
//                     'delete_published_posts' => 'Xóa bài viết đã xuất bản',
//                     'publish_pages' => 'Xuất bản trang',
//                     'edit_pages' => 'Chỉnh sửa trang',
//                     'edit_others_pages' => 'Chỉnh sửa trang của người khác',
//                     'edit_published_pages' => 'Chỉnh sửa trang đã xuất bản',
//                     'delete_pages' => 'Xóa trang',
//                     'delete_others_pages' => 'Xóa trang của người khác',
//                     'delete_published_pages' => 'Xóa trang đã xuất bản',
//                     'delete_private_pages' => 'Xóa trang riêng tư',
//                     'edit_private_pages' => 'Chỉnh sửa trang riêng tư',
//                     'read_private_pages' => 'Đọc trang riêng tư',
//                     'unfiltered_html' => 'Sử dụng HTML không lọc',
//                     'edit_dashboard' => 'Chỉnh sửa bảng điều khiển',
//                     'update_plugins' => 'Cập nhật plugins',
//                     'delete_plugins' => 'Xóa plugins',
//                     'update_themes' => 'Cập nhật giao diện',
//                     'update_core' => 'Cập nhật WordPress',
//                     'export' => 'Xuất dữ liệu',
//                     'import' => 'Nhập dữ liệu',
//                 );
                
//                 foreach ($all_caps as $cap => $cap_name) {
//                     $has_cap = current_user_can($cap) ? 'capability-true' : 'capability-false';
//                     $status = current_user_can($cap) ? 'Có' : 'Không';
//                     echo '<li><span class="' . $has_cap . '">' . esc_html($cap_name) . ' (' . esc_html($cap) . '): ' . esc_html($status) . '</span></li>';
//                 }
                
//                 echo '</ul>
//             </div>
            
//             <div class="section">
//                 <h2>Tất cả các Role trong hệ thống</h2>';
                
//                 global $wp_roles;
//                 echo '<table>';
//                 echo '<tr><th>Role</th><th>Tên hiển thị</th><th>Số lượng quyền hạn</th><th>Hành động</th></tr>';
                
//                 foreach ($wp_roles->roles as $role_key => $role) {
//                     echo '<tr>';
//                     echo '<td class="role-name">' . esc_html($role_key) . '</td>';
//                     echo '<td>' . esc_html($role['name']) . '</td>';
//                     echo '<td>' . count($role['capabilities']) . '</td>';
//                     echo '<td><button onclick="toggleCapabilities(\'' . esc_attr($role_key) . '\')">Xem chi tiết</button></td>';
//                     echo '</tr>';
                    
//                     echo '<tr id="' . esc_attr($role_key) . '-capabilities" style="display: none;">';
//                     echo '<td colspan="4">';
//                     echo '<h4>Quyền hạn của ' . esc_html($role['name']) . ':</h4>';
//                     echo '<table style="width: 100%;">';
//                     echo '<tr><th>Quyền hạn</th><th>Mô tả</th><th>Trạng thái</th></tr>';
                    
//                     foreach ($all_caps as $cap => $cap_name) {
//                         $has_cap = isset($role['capabilities'][$cap]) && $role['capabilities'][$cap] ? 'capability-true' : 'capability-false';
//                         $status = isset($role['capabilities'][$cap]) && $role['capabilities'][$cap] ? 'Có' : 'Không';
//                         echo '<tr>';
//                         echo '<td>' . esc_html($cap) . '</td>';
//                         echo '<td>' . esc_html($cap_name) . '</td>';
//                         echo '<td class="' . $has_cap . '">' . esc_html($status) . '</td>';
//                         echo '</tr>';
//                     }
                    
//                     echo '</table>';
//                     echo '</td>';
//                     echo '</tr>';
//                 }
                
//                 echo '</table>
//             </div>
            
//             <div class="section">
//                 <h2>Kiểm tra quyền hạn cụ thể</h2>
//                 <p>Sử dụng công cụ này để kiểm tra xem một role cụ thể có quyền hạn nào đó hay không.</p>
                
//                 <form method="post">
//                     <label for="test-role">Chọn Role:</label>
//                     <select name="test_role" id="test-role">';
                    
//                     foreach ($wp_roles->roles as $role_key => $role) {
//                         echo '<option value="' . esc_attr($role_key) . '">' . esc_html($role['name']) . '</option>';
//                     }
                    
//                     echo '</select>
                    
//                     <label for="test-capability">Nhập quyền hạn cần kiểm tra:</label>
//                     <input type="text" name="test_capability" id="test-capability" placeholder="Ví dụ: edit_posts">
                    
//                     <button type="submit" name="check_capability">Kiểm tra</button>
//                 </form>';
                
//                 if (isset($_POST['check_capability']) && isset($_POST['test_role']) && isset($_POST['test_capability'])) {
//                     $test_role = sanitize_text_field($_POST['test_role']);
//                     $test_capability = sanitize_text_field($_POST['test_capability']);
                    
//                     if (isset($wp_roles->roles[$test_role])) {
//                         $role_obj = get_role($test_role);
//                         $has_cap = $role_obj->has_cap($test_capability);
                        
//                         echo '<div style="margin-top: 20px; padding: 10px; border: 1px solid ' . ($has_cap ? 'green' : 'red') . ';">';
//                         echo '<p>Role <strong>' . esc_html($wp_roles->roles[$test_role]['name']) . '</strong> ';
//                         echo $has_cap ? '<span class="capability-true">CÓ</span>' : '<span class="capability-false">KHÔNG CÓ</span>';
//                         echo ' quyền <strong>' . esc_html($test_capability) . '</strong></p>';
//                         echo '</div>';
//                     }
//                 }
                
//                 echo '
//             </div>
            
//             <div class="security-note">
//                 <h3>Lưu ý bảo mật</h3>
//                 <p>Trang này hiển thị thông tin nhạy cảm về cấu trúc quyền hạn của trang web. Chỉ quản trị viên mới có thể truy cập.</p>
//                 <p>Sau khi hoàn tất việc kiểm tra, bạn nên vô hiệu hóa trang này bằng cách:</p>
//                 <ol>
//                     <li>Vô hiệu hóa plugin này, hoặc</li>
//                     <li>Chỉnh sửa code để tắt tính năng kiểm tra quyền hạn</li>
//                 </ol>
//             </div>
            
//             <script>
//                 function toggleCapabilities(roleKey) {
//                     var capabilitiesRow = document.getElementById(roleKey + "-capabilities");
//                     if (capabilitiesRow.style.display === "none") {
//                         capabilitiesRow.style.display = "table-row";
//                     } else {
//                         capabilitiesRow.style.display = "none";
//                     }
//                 }
//             </script>
//         </body>
//         </html>';
        
//         exit;
//     }
// }

// AJAX handler cho Save Template
add_action('wp_ajax_save_designer_template', function() {
    check_ajax_referer('designer_tagging_nonce', 'nonce');

    $post_id = intval($_POST['post_id']);
    $user_id = intval($_POST['user_id']);
    $template_id = intval($_POST['template_id']);
    $page_slug = sanitize_text_field($_POST['page_slug']);

    // Validate required fields
    if (!$post_id || !$user_id || !$template_id || !$page_slug) {
        wp_send_json_error('Missing required parameters');
        return;
    }

    // Get current tagging data
    $fields = get_post_meta($post_id, '_designer_editable_fields', true);
    if (!$fields) {
        wp_send_json_error('No tagging data found');
        return;
    }

    // Create meta key: {page_slug}_{user_id}_{template_id}
    $meta_key = $page_slug . '_' . $user_id . '_' . $template_id;

    // Prepare data to save
    $template_data = [
        'post_id' => $post_id,
        'user_id' => $user_id,
        'template_id' => $template_id,
        'page_slug' => $page_slug,
        'fields' => $fields,
        'created_at' => current_time('mysql'),
        'updated_at' => current_time('mysql')
    ];

    // Save to post meta
    $result = update_post_meta($post_id, '_designer_template_' . $meta_key, $template_data);

    if ($result !== false) {
        wp_send_json_success([
            'message' => 'Template saved successfully!',
            'meta_key' => $meta_key,
            'template_data' => $template_data
        ]);
    } else {
        wp_send_json_error('Failed to save template');
    }
});

add_action('wp_ajax_nopriv_save_designer_template', function() {
    check_ajax_referer('designer_tagging_nonce', 'nonce');

    $post_id = intval($_POST['post_id']);
    $user_id = intval($_POST['user_id']);
    $template_id = intval($_POST['template_id']);
    $page_slug = sanitize_text_field($_POST['page_slug']);

    // Validate required fields
    if (!$post_id || !$user_id || !$template_id || !$page_slug) {
        wp_send_json_error('Missing required parameters');
        return;
    }

    // Get current tagging data
    $fields = get_post_meta($post_id, '_designer_editable_fields', true);
    if (!$fields) {
        wp_send_json_error('No tagging data found');
        return;
    }

    // Create meta key: {page_slug}_{user_id}_{template_id}
    $meta_key = $page_slug . '_' . $user_id . '_' . $template_id;

    // Prepare data to save
    $template_data = [
        'post_id' => $post_id,
        'user_id' => $user_id,
        'template_id' => $template_id,
        'page_slug' => $page_slug,
        'fields' => $fields,
        'created_at' => current_time('mysql'),
        'updated_at' => current_time('mysql')
    ];

    // Save to post meta
    $result = update_post_meta($post_id, '_designer_template_' . $meta_key, $template_data);

    if ($result !== false) {
        wp_send_json_success([
            'message' => 'Template saved successfully!',
            'meta_key' => $meta_key,
            'template_data' => $template_data
        ]);
    } else {
        wp_send_json_error('Failed to save template');
    }
});

// AJAX handler cho Load Template
add_action('wp_ajax_load_designer_template', function() {
    check_ajax_referer('designer_tagging_nonce', 'nonce');

    $post_id = intval($_POST['post_id']);
    $user_id = intval($_POST['user_id']);
    $template_id = intval($_POST['template_id']);
    $page_slug = sanitize_text_field($_POST['page_slug']);

    // Validate required fields
    if (!$post_id || !$user_id || !$template_id || !$page_slug) {
        wp_send_json_error('Missing required parameters');
        return;
    }

    // Create meta key: {page_slug}_{user_id}_{template_id}
    $meta_key = $page_slug . '_' . $user_id . '_' . $template_id;

    // Load template data
    $template_data = get_post_meta($post_id, '_designer_template_' . $meta_key, true);

    if ($template_data && isset($template_data['fields'])) {
        // Update current tagging data with template data
        update_post_meta($post_id, '_designer_editable_fields', $template_data['fields']);

        wp_send_json_success([
            'message' => 'Template loaded successfully!',
            'meta_key' => $meta_key,
            'template_data' => $template_data
        ]);
    } else {
        wp_send_json_error('Template not found');
    }
});

add_action('wp_ajax_nopriv_load_designer_template', function() {
    check_ajax_referer('designer_tagging_nonce', 'nonce');

    $post_id = intval($_POST['post_id']);
    $user_id = intval($_POST['user_id']);
    $template_id = intval($_POST['template_id']);
    $page_slug = sanitize_text_field($_POST['page_slug']);

    // Validate required fields
    if (!$post_id || !$user_id || !$template_id || !$page_slug) {
        wp_send_json_error('Missing required parameters');
        return;
    }

    // Create meta key: {page_slug}_{user_id}_{template_id}
    $meta_key = $page_slug . '_' . $user_id . '_' . $template_id;

    // Load template data
    $template_data = get_post_meta($post_id, '_designer_template_' . $meta_key, true);

    if ($template_data && isset($template_data['fields'])) {
        // Update current tagging data with template data
        update_post_meta($post_id, '_designer_editable_fields', $template_data['fields']);

        wp_send_json_success([
            'message' => 'Template loaded successfully!',
            'meta_key' => $meta_key,
            'template_data' => $template_data
        ]);
    } else {
        wp_send_json_error('Template not found');
    }
});

// AJAX handler để lấy serialized meta value
add_action('wp_ajax_get_serialized_meta_value', function() {
    check_ajax_referer('designer_tagging_nonce', 'nonce');

    $post_id = intval($_POST['post_id']);
    $meta_key = sanitize_text_field($_POST['meta_key']);

    if (!$post_id || !$meta_key) {
        wp_send_json_error('Missing required parameters');
        return;
    }

    // Get the template data
    $template_data = get_post_meta($post_id, '_designer_template_' . $meta_key, true);

    if (!$template_data) {
        wp_send_json_error('Template data not found');
        return;
    }

    // Serialize the data (same as WordPress does)
    $serialized_value = serialize($template_data);

    wp_send_json_success([
        'serialized_value' => $serialized_value,
        'meta_key' => $meta_key
    ]);
});

add_action('wp_ajax_nopriv_get_serialized_meta_value', function() {
    check_ajax_referer('designer_tagging_nonce', 'nonce');

    $post_id = intval($_POST['post_id']);
    $meta_key = sanitize_text_field($_POST['meta_key']);

    if (!$post_id || !$meta_key) {
        wp_send_json_error('Missing required parameters');
        return;
    }

    // Get the template data
    $template_data = get_post_meta($post_id, '_designer_template_' . $meta_key, true);

    if (!$template_data) {
        wp_send_json_error('Template data not found');
        return;
    }

    // Serialize the data (same as WordPress does)
    $serialized_value = serialize($template_data);

    wp_send_json_success([
        'serialized_value' => $serialized_value,
        'meta_key' => $meta_key
    ]);
});

// Function để enable cache clearing
function designer_tagging_enable_cache_clear() {
    update_option('designer_tagging_clear_cache_enabled', true);
    return true;
}

// Function để disable cache clearing
function designer_tagging_disable_cache_clear() {
    update_option('designer_tagging_clear_cache_enabled', false);
    return true;
}

// Function để toggle cache clearing
function designer_tagging_toggle_cache_clear() {
    $current = get_option('designer_tagging_clear_cache_enabled', false);
    update_option('designer_tagging_clear_cache_enabled', !$current);
    return !$current;
}

// Function để check cache clearing status
function designer_tagging_is_cache_clear_enabled() {
    return get_option('designer_tagging_clear_cache_enabled', false);
}

// AJAX handler để toggle cache clearing
add_action('wp_ajax_designer_toggle_cache_clear', function() {
    check_ajax_referer('designer_tagging_nonce', 'nonce');

    $new_status = designer_tagging_toggle_cache_clear();
    $message = $new_status ? 'Cache clearing enabled' : 'Cache clearing disabled';

    wp_send_json_success([
        'message' => $message,
        'enabled' => $new_status
    ]);
});

// AJAX handler để get cache status
add_action('wp_ajax_designer_get_cache_status', function() {
    check_ajax_referer('designer_tagging_nonce', 'nonce');

    $enabled = designer_tagging_is_cache_clear_enabled();

    wp_send_json_success([
        'enabled' => $enabled,
        'message' => $enabled ? 'Cache clearing is enabled' : 'Cache clearing is disabled'
    ]);
});

// AJAX handler to save site theme
add_action('wp_ajax_save_site_theme', 'handle_save_site_theme');
add_action('wp_ajax_nopriv_save_site_theme', 'handle_save_site_theme');

function handle_save_site_theme() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'designer_tagging_nonce')) {
        wp_send_json_error('Invalid nonce');
        return;
    }

    $theme_name = sanitize_text_field($_POST['theme_name']);
    $theme_colors = sanitize_text_field($_POST['theme_colors']);
    $post_id = get_the_ID();

    if (!$post_id) {
        $post_id = intval(isset($_POST['post_id']) ? $_POST['post_id'] : 0);
    }

    if (!$post_id) {
        wp_send_json_error('No post ID found');
        return;
    }

    // Save theme settings to post meta
    update_post_meta($post_id, '_site_theme_name', $theme_name);
    update_post_meta($post_id, '_site_theme_colors', $theme_colors);

    // Also save as site-wide option for consistency
    update_option('site_theme_name', $theme_name);
    update_option('site_theme_colors', $theme_colors);

    wp_send_json_success([
        'message' => 'Theme saved successfully',
        'theme_name' => $theme_name
    ]);
}

// AJAX handler to save custom color theme
add_action('wp_ajax_save_custom_color_theme', 'handle_save_custom_color_theme');
add_action('wp_ajax_nopriv_save_custom_color_theme', 'handle_save_custom_color_theme');

function handle_save_custom_color_theme() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'designer_tagging_nonce')) {
        wp_send_json_error('Invalid nonce');
        return;
    }

    $base_color = sanitize_text_field($_POST['base_color']);
    $base_color_value = sanitize_text_field($_POST['base_color_value']);
    $accent_color = sanitize_text_field($_POST['accent_color']);
    $accent_color_value = sanitize_text_field($_POST['accent_color_value']);
    $post_id = get_the_ID();

    if (!$post_id) {
        $post_id = intval(isset($_POST['post_id']) ? $_POST['post_id'] : 0);
    }

    if (!$post_id) {
        wp_send_json_error('No post ID found');
        return;
    }

    // Save color theme settings to post meta
    update_post_meta($post_id, '_custom_color_theme', [
        'base_color' => $base_color,
        'base_color_value' => $base_color_value,
        'accent_color' => $accent_color,
        'accent_color_value' => $accent_color_value
    ]);

    // Also save as site-wide option for consistency
    update_option('custom_color_theme', [
        'base_color' => $base_color,
        'base_color_value' => $base_color_value,
        'accent_color' => $accent_color,
        'accent_color_value' => $accent_color_value
    ]);

    wp_send_json_success([
        'message' => 'Custom color theme saved successfully',
        'base_color' => $base_color,
        'accent_color' => $accent_color
    ]);
}

// AJAX handler to save advanced settings
add_action('wp_ajax_save_advanced_settings', 'handle_save_advanced_settings');
add_action('wp_ajax_nopriv_save_advanced_settings', 'handle_save_advanced_settings');

function handle_save_advanced_settings() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'designer_tagging_nonce')) {
        wp_send_json_error('Invalid nonce');
        return;
    }

    $settings = sanitize_text_field($_POST['settings']);
    $post_id = get_the_ID();

    if (!$post_id) {
        $post_id = intval(isset($_POST['post_id']) ? $_POST['post_id'] : 0);
    }

    if (!$post_id) {
        wp_send_json_error('No post ID found');
        return;
    }

    // Save advanced settings to post meta
    update_post_meta($post_id, '_advanced_settings', $settings);

    // Also save as site-wide option for consistency
    update_option('advanced_settings', $settings);

    wp_send_json_success([
        'message' => 'Advanced settings saved successfully'
    ]);
}

// Output JavaScript in the footer
add_action('wp_footer', function() {
    if (!is_singular()) return;
    ?>
    <script>
// Site Theme Functions
function openSiteTheme() {
    openSidebar("siteThemeSidebar", "openSiteTheme");
}

function closeSiteThemeSidebar() {
    closeAllSidebars();
}

// Color Theme Functions
function closeColorThemeSidebar() {
    closeAllSidebars();
}

// Color Theme Variables
let currentColorTheme = {
    baseColor: 'dark',
    baseColorValue: '#212529',
    accentColor: 'teal',
    accentColorValue: '#20b2aa',
    accentColors: {
        teal: '#20b2aa',
        lime: '#cddc39',
        lightblue: '#87ceeb',
        maroon: '#a0306e'
    }
};

// Base Color Selection
function selectBaseColor(colorType) {
    // Remove previous selection
    document.querySelectorAll('.color-option').forEach(option => {
        option.classList.remove('selected-base');
        const preview = option.querySelector('.color-preview');
        if (preview) {
            preview.style.border = '2px solid #e9ecef';
        }
    });

    // Add selection to clicked option
    const selectedOption = document.querySelector(`[onclick="selectBaseColor('${colorType}')"]`);
    if (selectedOption) {
        selectedOption.classList.add('selected-base');
        const preview = selectedOption.querySelector('.color-preview');
        if (preview) {
            preview.style.border = '2px solid #00bcd4';
        }
    }

    // Update current theme
    currentColorTheme.baseColor = colorType;
    if (colorType === 'light') {
        currentColorTheme.baseColorValue = '#f8f9fa';
    } else {
        currentColorTheme.baseColorValue = '#212529';
    }

    // Update slider background based on selection
    updateSliderBackground();
}

// Accent Color Selection
function selectAccentColor(colorType) {
    // Remove previous selection
    document.querySelectorAll('.accent-color-option').forEach(option => {
        option.classList.remove('selected-accent');
        const preview = option.querySelector('.color-preview');
        if (preview) {
            preview.style.border = '2px solid #e9ecef';
        }
    });

    // Add selection to clicked option
    const selectedOption = document.querySelector(`[onclick="selectAccentColor('${colorType}')"]`);
    if (selectedOption) {
        selectedOption.classList.add('selected-accent');
        const preview = selectedOption.querySelector('.color-preview');
        if (preview) {
            preview.style.border = '2px solid #00bcd4';
        }
    }

    // Update current theme
    currentColorTheme.accentColor = colorType;
    currentColorTheme.accentColorValue = currentColorTheme.accentColors[colorType];
}

// Update slider background based on base color selection
function updateSliderBackground() {
    const slider = document.getElementById('baseColorSlider');
    if (slider) {
        if (currentColorTheme.baseColor === 'light') {
            slider.style.background = 'linear-gradient(to right, #ffffff, #f8f9fa, #e9ecef)';
        } else {
            slider.style.background = 'linear-gradient(to right, #495057, #343a40, #212529)';
        }
    }
}

// Apply Color Theme
function applyColorTheme() {
    showStatusMessage('Applying custom color theme...', 'info');

    // Apply CSS custom properties to root
    const root = document.documentElement;
    root.style.setProperty('--theme-primary', currentColorTheme.baseColorValue);
    root.style.setProperty('--theme-secondary', currentColorTheme.accentColorValue);
    root.style.setProperty('--theme-accent', currentColorTheme.accentColorValue);

    // Set text color based on base color
    if (currentColorTheme.baseColor === 'light') {
        root.style.setProperty('--theme-text', '#212529');
        root.style.setProperty('--theme-background', '#ffffff');
    } else {
        root.style.setProperty('--theme-text', '#ffffff');
        root.style.setProperty('--theme-background', '#212529');
    }

    // Apply colors to page elements
    applyCustomColorTheme();

    // Save theme to database
    saveCustomColorTheme();

    showStatusMessage('Custom color theme applied successfully!', 'success');
}

// Apply custom color theme to page elements
function applyCustomColorTheme() {
    // Apply to buttons
    const buttons = document.querySelectorAll('button:not(.w3-button), .btn, input[type="submit"], input[type="button"]');
    buttons.forEach(btn => {
        btn.style.backgroundColor = currentColorTheme.baseColorValue;
        btn.style.color = currentColorTheme.baseColor === 'light' ? '#212529' : '#ffffff';
        btn.style.borderColor = currentColorTheme.baseColorValue;
    });

    // Apply to links
    const links = document.querySelectorAll('a:not(.w3-button)');
    links.forEach(link => {
        if (!link.closest('.w3-sidebar')) {
            link.style.color = currentColorTheme.accentColorValue;
        }
    });

    // Apply to headings
    const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
    headings.forEach(heading => {
        if (!heading.closest('.w3-sidebar')) {
            heading.style.color = currentColorTheme.baseColor === 'light' ? '#212529' : '#ffffff';
        }
    });
}

// Reset Color Theme
function resetColorTheme() {
    // Reset to default values
    currentColorTheme = {
        baseColor: 'dark',
        baseColorValue: '#212529',
        accentColor: 'teal',
        accentColorValue: '#20b2aa',
        accentColors: {
            teal: '#20b2aa',
            lime: '#cddc39',
            lightblue: '#87ceeb',
            maroon: '#a0306e'
        }
    };

    // Reset UI selections
    selectBaseColor('dark');
    selectAccentColor('teal');

    // Reset slider
    const slider = document.getElementById('baseColorSlider');
    if (slider) {
        slider.value = 50;
    }
    updateSliderBackground();

    // Apply reset theme
    applyColorTheme();

    showStatusMessage('Color theme reset to default', 'info');
}

// Save custom color theme to database
function saveCustomColorTheme() {
    jQuery.ajax({
        url: ajaxurl,
        type: 'POST',
        data: {
            action: 'save_custom_color_theme',
            base_color: currentColorTheme.baseColor,
            base_color_value: currentColorTheme.baseColorValue,
            accent_color: currentColorTheme.accentColor,
            accent_color_value: currentColorTheme.accentColorValue,
            post_id: designerTagging.post_id,
            nonce: designerTagging.nonce
        },
        success: function(response) {
            if (response.success) {
                // Theme saved successfully
            } else {
                showStatusMessage('Failed to save color theme: ' + response.data, 'error');
            }
        },
        error: function() {
            showStatusMessage('Error saving color theme settings', 'error');
        }
    });
}

// Advanced Settings Functions
function closeAdvancedSidebar() {
    closeAllSidebars();
}

// Advanced Settings Variables
let advancedSettings = {
    generalColors: {
        primaryBg: '#e3f2fd',
        secondaryBg: '#f5f5f5',
        dividerColor: '#e0e0e0'
    },
    textColors: {
        titleColor: '#000000',
        subtitleColor: '#666666',
        bodyTextColor: '#333333',
        secondaryTextColor: '#2e7d32',
        linkActionColor: '#4dd0e1'
    },
    buttonSettings: {
        buttonType: 'primary',
        regular: {
            fillColor: '#2196f3',
            borderColor: '#2196f3',
            textColor: '#2196f3'
        },
        hover: {
            fillColor: '#e3f2fd',
            borderColor: '#e3f2fd',
            textColor: '#e3f2fd'
        }
    },
    textTheme: {
        headingFont: 'arial',
        paragraphFont: ''
    }
};

// Toggle Advanced Section
function toggleAdvancedSection(sectionId) {
    const content = document.getElementById(sectionId + '-content');
    const arrow = document.getElementById(sectionId + '-arrow');

    if (content.style.display === 'none') {
        content.style.display = 'block';
        arrow.classList.remove('fa-chevron-right');
        arrow.classList.add('fa-chevron-down');
    } else {
        content.style.display = 'none';
        arrow.classList.remove('fa-chevron-down');
        arrow.classList.add('fa-chevron-right');
    }
}

// Switch Button Tab (Regular/Hover)
function switchButtonTab(tabType) {
    // Update tab buttons
    document.getElementById('regularTab').classList.remove('active');
    document.getElementById('hoverTab').classList.remove('active');
    document.getElementById(tabType + 'Tab').classList.add('active');

    // Update tab button styles
    document.getElementById('regularTab').style.background = tabType === 'regular' ? '#f5f5f5' : '#ffffff';
    document.getElementById('hoverTab').style.background = tabType === 'hover' ? '#f5f5f5' : '#ffffff';

    // Show/hide tab content
    document.getElementById('regularTabContent').style.display = tabType === 'regular' ? 'block' : 'none';
    document.getElementById('hoverTabContent').style.display = tabType === 'hover' ? 'block' : 'none';
}

// Open Color Picker
function openColorPicker(colorType, currentColor) {
    // Create a simple color input
    const colorInput = document.createElement('input');
    colorInput.type = 'color';
    colorInput.value = currentColor;
    colorInput.style.position = 'absolute';
    colorInput.style.left = '-9999px';
    document.body.appendChild(colorInput);

    colorInput.addEventListener('change', function() {
        updateAdvancedColor(colorType, this.value);
        document.body.removeChild(colorInput);
    });

    colorInput.click();
}

// Update Advanced Color
function updateAdvancedColor(colorType, newColor) {
    // Update the color picker box
    const colorBox = document.querySelector(`[onclick="openColorPicker('${colorType}', '${advancedSettings.generalColors[colorType] || advancedSettings.textColors[colorType] || advancedSettings.buttonSettings.regular[colorType] || advancedSettings.buttonSettings.hover[colorType]}')"]`);
    if (colorBox) {
        colorBox.style.background = newColor;
        colorBox.setAttribute('onclick', `openColorPicker('${colorType}', '${newColor}')`);
    }

    // Update settings object
    if (colorType.includes('Bg') || colorType === 'dividerColor') {
        advancedSettings.generalColors[colorType] = newColor;
    } else if (colorType.includes('Color') && !colorType.includes('button') && !colorType.includes('hover')) {
        advancedSettings.textColors[colorType] = newColor;
    } else if (colorType.includes('button') && !colorType.includes('hover')) {
        advancedSettings.buttonSettings.regular[colorType] = newColor;
    } else if (colorType.includes('hover')) {
        advancedSettings.buttonSettings.hover[colorType] = newColor;
    }
}

// Apply Advanced Settings
function applyAdvancedSettings() {
    showStatusMessage('Applying advanced settings...', 'info');

    // Apply CSS custom properties to root
    const root = document.documentElement;

    // Apply general colors
    root.style.setProperty('--advanced-primary-bg', advancedSettings.generalColors.primaryBg);
    root.style.setProperty('--advanced-secondary-bg', advancedSettings.generalColors.secondaryBg);
    root.style.setProperty('--advanced-divider-color', advancedSettings.generalColors.dividerColor);

    // Apply text colors
    root.style.setProperty('--advanced-title-color', advancedSettings.textColors.titleColor);
    root.style.setProperty('--advanced-subtitle-color', advancedSettings.textColors.subtitleColor);
    root.style.setProperty('--advanced-body-text-color', advancedSettings.textColors.bodyTextColor);
    root.style.setProperty('--advanced-secondary-text-color', advancedSettings.textColors.secondaryTextColor);
    root.style.setProperty('--advanced-link-action-color', advancedSettings.textColors.linkActionColor);

    // Apply button colors
    root.style.setProperty('--advanced-button-fill-color', advancedSettings.buttonSettings.regular.fillColor);
    root.style.setProperty('--advanced-button-border-color', advancedSettings.buttonSettings.regular.borderColor);
    root.style.setProperty('--advanced-button-text-color', advancedSettings.buttonSettings.regular.textColor);
    root.style.setProperty('--advanced-button-hover-fill-color', advancedSettings.buttonSettings.hover.fillColor);
    root.style.setProperty('--advanced-button-hover-border-color', advancedSettings.buttonSettings.hover.borderColor);
    root.style.setProperty('--advanced-button-hover-text-color', advancedSettings.buttonSettings.hover.textColor);

    // Apply fonts
    const headingFont = document.getElementById('headingFont').value;
    const paragraphFont = document.getElementById('paragraphFont').value;

    if (headingFont) {
        root.style.setProperty('--advanced-heading-font', headingFont);
        advancedSettings.textTheme.headingFont = headingFont;
    }

    if (paragraphFont) {
        root.style.setProperty('--advanced-paragraph-font', paragraphFont);
        advancedSettings.textTheme.paragraphFont = paragraphFont;
    }

    // Apply advanced settings to page elements
    applyAdvancedSettingsToElements();

    // Save settings to database
    saveAdvancedSettings();

    showStatusMessage('Advanced settings applied successfully!', 'success');
}

// Apply advanced settings to page elements
function applyAdvancedSettingsToElements() {
    // Apply to headings
    const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
    headings.forEach(heading => {
        if (!heading.closest('.w3-sidebar')) {
            heading.style.color = advancedSettings.textColors.titleColor;
            if (advancedSettings.textTheme.headingFont) {
                heading.style.fontFamily = advancedSettings.textTheme.headingFont;
            }
        }
    });

    // Apply to paragraphs
    const paragraphs = document.querySelectorAll('p');
    paragraphs.forEach(p => {
        if (!p.closest('.w3-sidebar')) {
            p.style.color = advancedSettings.textColors.bodyTextColor;
            if (advancedSettings.textTheme.paragraphFont) {
                p.style.fontFamily = advancedSettings.textTheme.paragraphFont;
            }
        }
    });

    // Apply to links
    const links = document.querySelectorAll('a:not(.w3-button)');
    links.forEach(link => {
        if (!link.closest('.w3-sidebar')) {
            link.style.color = advancedSettings.textColors.linkActionColor;
        }
    });

    // Apply to buttons
    const buttons = document.querySelectorAll('button:not(.w3-button), .btn, input[type="submit"], input[type="button"]');
    buttons.forEach(btn => {
        btn.style.backgroundColor = advancedSettings.buttonSettings.regular.fillColor;
        btn.style.borderColor = advancedSettings.buttonSettings.regular.borderColor;
        btn.style.color = advancedSettings.buttonSettings.regular.textColor;

        // Add hover effects
        btn.addEventListener('mouseenter', function() {
            this.style.backgroundColor = advancedSettings.buttonSettings.hover.fillColor;
            this.style.borderColor = advancedSettings.buttonSettings.hover.borderColor;
            this.style.color = advancedSettings.buttonSettings.hover.textColor;
        });

        btn.addEventListener('mouseleave', function() {
            this.style.backgroundColor = advancedSettings.buttonSettings.regular.fillColor;
            this.style.borderColor = advancedSettings.buttonSettings.regular.borderColor;
            this.style.color = advancedSettings.buttonSettings.regular.textColor;
        });
    });
}

// Reset Advanced Settings
function resetAdvancedSettings() {
    // Reset to default values
    advancedSettings = {
        generalColors: {
            primaryBg: '#e3f2fd',
            secondaryBg: '#f5f5f5',
            dividerColor: '#e0e0e0'
        },
        textColors: {
            titleColor: '#000000',
            subtitleColor: '#666666',
            bodyTextColor: '#333333',
            secondaryTextColor: '#2e7d32',
            linkActionColor: '#4dd0e1'
        },
        buttonSettings: {
            buttonType: 'primary',
            regular: {
                fillColor: '#2196f3',
                borderColor: '#2196f3',
                textColor: '#2196f3'
            },
            hover: {
                fillColor: '#e3f2fd',
                borderColor: '#e3f2fd',
                textColor: '#e3f2fd'
            }
        },
        textTheme: {
            headingFont: 'arial',
            paragraphFont: ''
        }
    };

    // Reset UI elements
    document.getElementById('headingFont').value = 'arial';
    document.getElementById('paragraphFont').value = '';
    document.getElementById('buttonType').value = 'primary';

    // Reset color picker boxes
    document.querySelectorAll('.color-picker-box').forEach(box => {
        const onclick = box.getAttribute('onclick');
        if (onclick) {
            const colorType = onclick.match(/'([^']+)'/)[1];
            const defaultColor = getDefaultColor(colorType);
            box.style.background = defaultColor;
            box.setAttribute('onclick', `openColorPicker('${colorType}', '${defaultColor}')`);
        }
    });

    // Apply reset settings
    applyAdvancedSettings();

    showStatusMessage('Advanced settings reset to default', 'info');
}

// Get default color for reset
function getDefaultColor(colorType) {
    const defaults = {
        primaryBg: '#e3f2fd',
        secondaryBg: '#f5f5f5',
        dividerColor: '#e0e0e0',
        titleColor: '#000000',
        subtitleColor: '#666666',
        bodyTextColor: '#333333',
        secondaryTextColor: '#2e7d32',
        linkActionColor: '#4dd0e1',
        buttonFillColor: '#2196f3',
        buttonBorderColor: '#2196f3',
        buttonTextColor: '#2196f3',
        hoverFillColor: '#e3f2fd',
        hoverBorderColor: '#e3f2fd',
        hoverTextColor: '#e3f2fd'
    };
    return defaults[colorType] || '#000000';
}

// Save advanced settings to database
function saveAdvancedSettings() {
    jQuery.ajax({
        url: ajaxurl,
        type: 'POST',
        data: {
            action: 'save_advanced_settings',
            settings: JSON.stringify(advancedSettings),
            post_id: designerTagging.post_id,
            nonce: designerTagging.nonce
        },
        success: function(response) {
            if (response.success) {
                // Settings saved successfully
            } else {
                showStatusMessage('Failed to save advanced settings: ' + response.data, 'error');
            }
        },
        error: function() {
            showStatusMessage('Error saving advanced settings', 'error');
        }
    });
}

// Theme color palettes
const themeColors = {
    'energetic': {
        primary: '#4285f4',
        secondary: '#ea4335',
        accent: '#c5a3ff',
        text: '#333333',
        background: '#ffffff',
        name: 'Energetic',
        description: 'Vibrant & joyful'
    },
    'retro-vibrant': {
        primary: '#1e3a8a',
        secondary: '#eab308',
        accent: '#c084fc',
        highlight: '#be185d',
        text: '#1f2937',
        background: '#ffffff',
        name: 'Retro vibrant',
        description: 'Hip, mod & stylish'
    },
    'eclectic': {
        primary: '#1a1a1a',
        secondary: '#ef4444',
        accent: '#f97316',
        highlight: '#10b981',
        text: '#f8fafc',
        background: '#1a1a1a',
        name: 'Eclectic',
        description: 'Diverse & refreshing'
    },
    'dynamic': {
        primary: '#84cc16',
        secondary: '#7c3aed',
        accent: '#a855f7',
        text: '#1f2937',
        background: '#ffffff',
        name: 'Dynamic',
        description: 'Lively, active & bright'
    },
    'technical': {
        primary: '#1e40af',
        secondary: '#06b6d4',
        accent: '#d1fae5',
        text: '#1f2937',
        background: '#ffffff',
        name: 'Technical',
        description: 'Modern & efficient'
    },
    'nostalgic': {
        primary: '#dc2626',
        secondary: '#94a3b8',
        accent: '#475569',
        text: '#1f2937',
        background: '#ffffff',
        name: 'Nostalgic',
        description: 'Cozy & comfortable'
    },
    'natural': {
        primary: '#065f46',
        secondary: '#059669',
        accent: '#ea580c',
        text: '#1f2937',
        background: '#ffffff',
        name: 'Natural',
        description: 'Earthy & verdant'
    }
};

// Select theme function with visual feedback
function selectTheme(element, themeName) {
    // Remove selected class from all theme options
    document.querySelectorAll('.theme-option').forEach(option => {
        option.classList.remove('selected');
    });

    // Add selected class to clicked element
    element.classList.add('selected');

    // Apply the theme
    applyTheme(themeName);
}

// Apply theme function
function applyTheme(themeName) {
    const theme = themeColors[themeName];
    if (!theme) return;

    // Show loading state
    // showStatusMessage('Applying ' + theme.name + ' theme...', 'info');

    // Apply CSS custom properties to root
    const root = document.documentElement;
    root.style.setProperty('--theme-primary', theme.primary);
    root.style.setProperty('--theme-secondary', theme.secondary);
    root.style.setProperty('--theme-accent', theme.accent);
    root.style.setProperty('--theme-text', theme.text);
    root.style.setProperty('--theme-background', theme.background);

    if (theme.highlight) {
        root.style.setProperty('--theme-highlight', theme.highlight);
    }

    // Apply colors to common elements
    applyThemeToElements(theme);

    // Update current theme display
    updateCurrentThemeDisplay(theme);

    // Save theme to database
    saveThemeSettings(themeName, theme);

    // showStatusMessage(theme.name + ' theme applied successfully!', 'success');
}

// Apply theme colors to page elements
function applyThemeToElements(theme) {
    // Apply to buttons
    const buttons = document.querySelectorAll('button:not(.w3-button), .btn, input[type="submit"], input[type="button"]');
    buttons.forEach(btn => {
        btn.style.backgroundColor = theme.primary;
        btn.style.color = theme.background;
        btn.style.borderColor = theme.primary;
    });

    // Apply to links
    const links = document.querySelectorAll('a:not(.w3-button)');
    links.forEach(link => {
        if (!link.closest('.w3-sidebar')) {
            link.style.color = theme.primary;
        }
    });

    // Apply to headings
    const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
    headings.forEach(heading => {
        if (!heading.closest('.w3-sidebar')) {
            heading.style.color = theme.text;
        }
    });
}

// Update current theme display
function updateCurrentThemeDisplay(theme) {
    const currentThemeContainer = document.querySelector('.current-theme-container');
    if (currentThemeContainer) {
        const themeInfo = currentThemeContainer.querySelector('.theme-info strong');
        const themeDesc = currentThemeContainer.querySelector('.theme-info .w3-small');
        const colorDots = currentThemeContainer.querySelectorAll('.color-dot');

        if (themeInfo) themeInfo.textContent = theme.name;
        if (themeDesc) themeDesc.textContent = theme.description;

        // Update color dots
        if (colorDots.length >= 2) {
            colorDots[0].style.backgroundColor = theme.primary;
            colorDots[1].style.backgroundColor = theme.secondary;
        }
    }
}

// Save theme settings to database
function saveThemeSettings(themeName, theme) {
    jQuery.ajax({
        url: ajaxurl,
        type: 'POST',
        data: {
            action: 'save_site_theme',
            theme_name: themeName,
            theme_colors: JSON.stringify(theme),
            post_id: designerTagging.post_id,
            nonce: designerTagging.nonce
        },
        success: function(response) {
            if (response.success) {
                // Theme saved successfully
            } else {
                showStatusMessage('Failed to save theme: ' + response.data, 'error');
            }
        },
        error: function() {
            showStatusMessage('Error saving theme settings', 'error');
        }
    });
}
</script>
<?php
}); // Close the wp_footer action