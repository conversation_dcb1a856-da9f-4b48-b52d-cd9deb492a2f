<?php
// Define backup API endpoint
define('BACKUP_UPLOAD_API_URL', 'https://api-weaveform.ip-tribe.com/files/upload');

// Define backup file size limit (in bytes) - 950MB to account for overhead
define('BACKUP_FILE_SIZE_LIMIT', 950 * 1024 * 1024);

// AWS API
define('AWS_API_URL', 'https://ipt-wp-api.weaveform.com/');
define('AWS_API_USERNAME', 'admin');
define('AWS_API_PASSWORD', '6ZsfKmzG+C6mzfDzETnXGA==');

// Register REST API endpoints
add_action('rest_api_init', 'register_backup_approval_api_endpoints');

function register_backup_approval_api_endpoints() {
    // Main backup and approval endpoint
    register_rest_route('customer-edit/v1', '/backup-site', [
        'methods' => 'POST',
        'callback' => 'ipt_handle_api_backup_and_approval_rest',
        'permission_callback' => 'ipt_backup_approval_permission_check'
    ]);
}

/**
 * Permission callback for backup approval endpoint
 */
function ipt_backup_approval_permission_check($request) {
    // Get Authorization header
    $auth_header = $request->get_header('authorization');

    if (empty($auth_header)) {
        return new WP_Error('rest_forbidden', 'Authorization header is required.', ['status' => 401]);
    }

    // Check if it's a Bearer token
    if (!preg_match('/Bearer\s+(.*)$/i', $auth_header, $matches)) {
        return new WP_Error('rest_forbidden', 'Bearer token is required.', ['status' => 401]);
    }

    $provided_token = trim($matches[1]);

    // Validate against GRAPHQL_TOKEN
    if (!defined('GRAPHQL_TOKEN') || $provided_token !== GRAPHQL_TOKEN) {
        return new WP_Error('rest_forbidden', 'Invalid bearer token.', ['status' => 403]);
    }

    return true;
}

/**
 * REST API handler to create API backup and then handle approval
 */
function ipt_handle_api_backup_and_approval_rest($request) {
    // // Step 1: Login to API service
    // $login_response = ipt_api_login();
    // if (!$login_response['success']) {
    //     return new WP_Error('api_login_failed', 'Failed to login to API service: ' . $login_response['message'], [
    //         'status' => 500,
    //         'error_code' => 'API_LOGIN_FAILED'
    //     ]);
    // }

    // $token = $login_response['token'];

    // // Step 2: Create backup via API
    // $backup_response = ipt_api_create_backup($token);
    // if (!$backup_response['success']) {
    //     return new WP_Error('api_backup_failed', 'Failed to create backup via API: ' . $backup_response['message'], [
    //         'status' => 500,
    //         'error_code' => 'API_BACKUP_FAILED'
    //     ]);
    // }

    // Step 3: Now proceed with the original approval process
    // Call the existing approval handler with REST-compatible data
    $approval_result = ipt_handle_approval_rest();

    if (is_wp_error($approval_result)) {
        return $approval_result;
    }

    return new WP_REST_Response($approval_result, 200);
}

/**
 * Helper function to login to API service
 */
function ipt_api_login() {
    $login_url = AWS_API_URL . 'login';

    $login_data = array(
        'username' => AWS_API_USERNAME,
        'password' => AWS_API_PASSWORD
    );

    $args = array(
        'method' => 'POST',
        'headers' => array(
            'Content-Type' => 'application/json'
        ),
        'body' => json_encode($login_data),
        'timeout' => 30
    );

    $response = wp_remote_post($login_url, $args);

    if (is_wp_error($response)) {
        return array(
            'success' => false,
            'message' => $response->get_error_message()
        );
    }

    $response_code = wp_remote_retrieve_response_code($response);
    $response_body = wp_remote_retrieve_body($response);

    if ($response_code !== 200) {
        return array(
            'success' => false,
            'message' => "HTTP {$response_code}: {$response_body}"
        );
    }

    $data = json_decode($response_body, true);
    if (!$data || !isset($data['token'])) {
        return array(
            'success' => false,
            'message' => 'No token received from API'
        );
    }

    return array(
        'success' => true,
        'token' => $data['token']
    );
}

/**
 * Helper function to create backup via API
 */
function ipt_api_create_backup($token) {
    $backup_url = AWS_API_URL . 'wpcli';

    // Get current domain
    $current_domain = $_SERVER['HTTP_HOST'];

    $backup_data = array(
        'domain_name' => $current_domain,
        'command' => 'ai1wm backup'
    );

    $args = array(
        'method' => 'POST',
        'headers' => array(
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . $token
        ),
        'body' => json_encode($backup_data),
        'timeout' => 300 // 5 minutes timeout for backup creation
    );

    $response = wp_remote_post($backup_url, $args);

    if (is_wp_error($response)) {
        return array(
            'success' => false,
            'message' => $response->get_error_message()
        );
    }

    $response_code = wp_remote_retrieve_response_code($response);
    $response_body = wp_remote_retrieve_body($response);

    if ($response_code !== 200) {
        return array(
            'success' => false,
            'message' => "HTTP {$response_code}: {$response_body}"
        );
    }

    return array(
        'success' => true,
        'response' => $response_body
    );
}

/**
 * REST-compatible approval handler (used by both REST and legacy AJAX)
 */
function ipt_handle_approval_rest() {

    // Initialize code_file_id for approval process
    $code_file_id = null;

    // Handle backup upload for approval action
    $backup_result = handle_backup_upload_for_approval();

    if (!$backup_result['success']) {
        // Return error with code for approval failure
        return new WP_Error('backup_failed', 'Approval failed during backup process', [
            'status' => 500,
            'error_code' => $backup_result['error_code'],
            'step' => $backup_result['step'],
            'details' => isset($backup_result['details']) ? $backup_result['details'] : null
        ]);
    }

    // Get code_file_id from successful backup upload
    $code_file_id = $backup_result['code_file_id'];
    

    // Determine redirect URL (remove approval and bypass_token parameters)

    $response_data = array(
        'timestamp' => current_time('mysql'),
    );

    // Add approval-specific data
    if ($code_file_id) {
        $response_data['file_id'] = $code_file_id;
        $response_data['backup_uploaded'] = true;

        // Delete backup file after successful approval
        if (isset($backup_result['backup_file']['filepath'])) {
            $delete_result = delete_backup_file_after_approval($backup_result['backup_file']['filepath']);
            $response_data['backup_deleted'] = $delete_result['success'];
            if (!$delete_result['success']) {
                $response_data['delete_warning'] = $delete_result['error_code'];
            }
        }
    }

    return $response_data;
}


/**
 * Handle backup upload process for approval
 * @return array Returns result with success/error info and code_file_id
 */
function handle_backup_upload_for_approval() {
    // Step 1: Get latest backup file
    $latest_file = get_latest_backup_file();
    if ($latest_file === false) {
        return array(
            'success' => false,
            'error_code' => 'BACKUP_001',
            'message' => 'No backup files found',
            'step' => 'finding_backup'
        );
    }

    // Step 2: Upload to API
    $upload_result = upload_backup_to_api($latest_file['filepath']);
    if (!$upload_result['success']) {
        return array(
            'success' => false,
            'error_code' => $upload_result['error_code'],
            'message' => $upload_result['message'],
            'step' => 'uploading',
            'details' => isset($upload_result['api_error']) ? $upload_result['api_error'] : null
        );
    }

    // Step 3: Return success with code_file_id
    return array(
        'success' => true,
        'code_file_id' => $upload_result['code_file_id'],
        'upload_info' => $upload_result['upload_info'],
        'backup_file' => $latest_file,
        'step' => 'upload_complete'
    );
}


/**
 * Delete backup file after successful approval
 * @param string $file_path Full path to the backup file
 * @return array Returns result with success/error info
 */
function delete_backup_file_after_approval($file_path) {
    if (!file_exists($file_path)) {
        return array(
            'success' => true,
            'message' => 'File already deleted or not found'
        );
    }

    if (unlink($file_path)) {

        return array(
            'success' => true,
            'message' => 'Backup file deleted successfully'
        );
    } else {

        return array(
            'success' => false,
            'error_code' => 'BACKUP_005',
            'message' => 'Failed to delete backup file after approval'
        );
    }
}

/**
 * Get the latest backup file from AI1WM backups directory
 * @return array|false Returns file info array or false if no file found
 */
function get_latest_backup_file() {
    $backup_dir = WP_CONTENT_DIR . '/ai1wm-backups';

    // Check if backup directory exists
    if (!is_dir($backup_dir)) {
        return false;
    }

    // Get all .wpress files
    $files = scandir($backup_dir);
    if ($files === false) {
        return false;
    }

    $backup_files = array();
    foreach ($files as $file) {
        if (pathinfo($file, PATHINFO_EXTENSION) === 'wpress') {
            $file_path = $backup_dir . '/' . $file;
            if (is_file($file_path) && is_readable($file_path)) {
                $backup_files[] = array(
                    'filename' => $file,
                    'filepath' => $file_path,
                    'modified_time' => filemtime($file_path),
                    'size' => filesize($file_path)
                );
            }
        }
    }

    // Return false if no backup files found
    if (empty($backup_files)) {
        return false;
    }

    // Sort by modification time (newest first)
    usort($backup_files, function($a, $b) {
        return $b['modified_time'] - $a['modified_time'];
    });

    // Return the latest file
    $latest_file = $backup_files[0];


    return $latest_file;
}

/**
 * Upload backup file to API
 * @param string $file_path Full path to the backup file
 * @return array Returns response array with success/error info
 */
function upload_backup_to_api($file_path) {
    // Validate file exists and is readable
    if (!file_exists($file_path) || !is_readable($file_path)) {
        return array(
            'success' => false,
            'error_code' => 'BACKUP_002',
            'message' => 'Backup file not readable'
        );
    }

    // Check file size
    $file_size = filesize($file_path);
    if ($file_size > BACKUP_FILE_SIZE_LIMIT) {
        return array(
            'success' => false,
            'error_code' => 'BACKUP_003',
            'message' => 'File too large for upload',
            'file_size' => size_format($file_size),
            'limit' => size_format(BACKUP_FILE_SIZE_LIMIT)
        );
    }

    // Prepare file for upload
    $filename = basename($file_path);

    // Create cURL file upload
    if (class_exists('CURLFile')) {
        $file_data = new CURLFile($file_path, 'application/octet-stream', $filename);
    } else {
        // Fallback for older PHP versions
        $file_data = '@' . $file_path;
    }

    // Prepare POST data
    $post_data = array(
        'file' => $file_data
    );

    // Initialize cURL
    $ch = curl_init();
    curl_setopt_array($ch, array(
        CURLOPT_URL => BACKUP_UPLOAD_API_URL,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => $post_data,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 300, // 5 minutes timeout for large files
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_SSL_VERIFYPEER => false, // For development - should be true in production
        CURLOPT_USERAGENT => 'WordPress/Designer-Tagging-Plugin'
    ));

    // Execute request
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curl_error = curl_error($ch);
    curl_close($ch);

    // Handle cURL errors
    if ($response === false || !empty($curl_error)) {
        return array(
            'success' => false,
            'error_code' => 'BACKUP_004',
            'message' => 'Upload request failed',
            'curl_error' => $curl_error
        );
    }

    // Parse JSON response
    $response_data = json_decode($response, true);
    if (json_last_error() !== JSON_ERROR_NONE) {

        return array(
            'success' => false,
            'error_code' => 'BACKUP_004',
            'message' => 'Invalid API response format'
        );
    }

    // Check for API errors
    if (isset($response_data['errors']) && !empty($response_data['errors'])) {
        $error = $response_data['errors'][0];

        return array(
            'success' => false,
            'error_code' => 'BACKUP_004',
            'message' => 'API upload failed',
            'api_error' => $error
        );
    }

    // Check for successful upload
    if (isset($response_data['upload']) && isset($response_data['upload']['id'])) {
        $upload_info = $response_data['upload'];


        return array(
            'success' => true,
            'code_file_id' => $upload_info['id'],
            'upload_info' => $upload_info
        );
    }

    // Unexpected response format
    return array(
        'success' => false,
        'error_code' => 'BACKUP_004',
        'message' => 'Unexpected API response format'
    );
}