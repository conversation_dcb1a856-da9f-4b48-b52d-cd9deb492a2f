<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Designer Login</title>
    <!-- Favicon -->
    <!-- <link rel="icon" type="image/png" href="<?php echo plugin_dir_url(dirname(__FILE__)); ?>dashboard/img/logow.png"> -->

    <link rel="icon" href="<?php echo plugin_dir_url(dirname(__FILE__)); ?>assets/ico/cropped-logo-32x32.png" sizes="32x32">
    <link rel="icon" href="<?php echo plugin_dir_url(dirname(__FILE__)); ?>assets/ico/cropped-logo-192x192.png" sizes="192x192">
    <link rel="apple-touch-icon" href="<?php echo plugin_dir_url(dirname(__FILE__)); ?>assets/ico/cropped-logo-180x180.png">
    <meta name="msapplication-TileImage" content="<?php echo plugin_dir_url(dirname(__FILE__)); ?>assets/ico/cropped-logo-270x270.png">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-image: url('https://images.unsplash.com/photo-1470071459604-3b5ec3a7fe05?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1974&q=80');
            background-size: cover;
            background-position: center;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .logo {
            width: 60px;
            margin-bottom: 20px;
        }
        .login-container {
            background-color: white;
            border-radius: 10px;
            padding: 40px;
            width: 100%;
            max-width: 450px;
            text-align: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        .form-control {
            padding: 12px;
            margin-bottom: 15px;
            border-radius: 5px;
            border-color: #ced4da;
        }
        .btn-login {
            background-color: #48c9b0;
            border: none;
            color: white;
            padding: 12px;
            width: 100%;
            font-weight: 500;
            border-radius: 5px;
            margin-top: 10px;
        }
        .btn-login:hover {
            background-color: #3dbeb5;
        }
        .forgot-password {
            text-align: right;
            display: block;
            margin-bottom: 20px;
            color: #6c757d;
            text-decoration: none;
            font-size: 14px;
        }
        .forgot-password:hover {
            color: #48c9b0;
        }
        .login-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 15px;
        }
        .login-subtitle {
            color: #6c757d;
            font-size: 14px;
            margin-bottom: 25px;
        }
        /* Thêm style cho error messages */
        .hidden {
            display: none;
        }
        .text-danger {
            color: #dc3545;
        }
        /* Style cho nút hiển thị/ẩn mật khẩu */
        .password-toggle {
            cursor: pointer;
            border-left: none;
            border-color: #ced4da;
            background-color: #fff;
            height: 50px; /* Đảm bảo chiều cao bằng với input */
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .password-toggle:focus {
            box-shadow: none;
            background-color: #fff;
            border-color: #ced4da;
        }
        .password-toggle:hover {
             border-color: #ced4da;
            background-color: #fff;
        }
        /* Đảm bảo input-group có border đồng nhất */
        .input-group {
            border-radius: 5px;
            overflow: hidden;
        }
        .input-group .form-control {
            margin-bottom: 0;
            border-right: none;
        }
        /* Đảm bảo icon có kích thước phù hợp */
        .password-toggle i {
            font-size: 16px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <!-- Thêm Font Awesome từ CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    
    <div class="login-container">
        <!-- Logo -->
        <img src="<?php echo plugin_dir_url(dirname(__FILE__)); ?>dashboard/img/logow.png" alt="Designer Weaveform" class="logo">
        
        <!-- Login Form -->
        <h1 class="login-title">Designer Login</h1>
        <!-- <p class="login-subtitle">Make a new doc to bring your words, data, and teams together</p> -->
        
        <form id="loginForm">
            <div class="mb-3">
                <input type="email" class="form-control" id="email" placeholder="Your email" required>
                <div id="email-error" class="text-danger mt-1 small hidden">Please enter your email</div>
            </div>
            <div class="mb-3 position-relative">
                <div class="input-group">
                    <input type="password" class="form-control" id="password" placeholder="Your password" required>
                    <button type="button" class="btn password-toggle" aria-label="Toggle password visibility">
                        <i class="fa-solid fa-eye"></i>
                    </button>
                </div>
                <div id="password-error" class="text-danger mt-1 small hidden">Please enter your password</div>
            </div>
            <!-- <div class="d-flex justify-content-between align-items-center mb-3">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="rememberMe">
                    <label class="form-check-label" for="rememberMe" style="font-size: 14px; color: #6c757d;">
                        Remember account
                    </label>
                </div>
                <a href="#" class="forgot-password">Forgot Password</a>
            </div> -->
            <div id="login-general-error" class="alert alert-danger mb-3 hidden" role="alert"></div>
            <?php wp_nonce_field('ipt_login_nonce', 'ipt_login_nonce'); ?>
            <button type="button" id="loginFormBtn" class="btn btn-login">Sign In</button>
        </form>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>

    <!-- Localize script for AJAX -->
    <script type="text/javascript">
        var iptHomeAjax = {
            ajax_url: '<?php echo admin_url('admin-ajax.php'); ?>'
        };
    </script>

    <script>
        jQuery(document).ready(function($) {
            // Toggle password visibility
            $('.password-toggle').on('click', function() {
                const passwordInput = $('#password');
                const icon = $(this).find('i');
                
                if (passwordInput.attr('type') === 'password') {
                    // Hiển thị mật khẩu
                    passwordInput.attr('type', 'text');
                    icon.removeClass('fa-eye').addClass('fa-eye-slash');
                    $(this).attr('aria-label', 'Hide password');
                } else {
                    // Ẩn mật khẩu
                    passwordInput.attr('type', 'password');
                    icon.removeClass('fa-eye-slash').addClass('fa-eye');
                    $(this).attr('aria-label', 'Show password');
                }
            });
            
            // Function to show error for input fields
            function showError(fieldId, show, message = null) {
                const errorElement = document.getElementById(fieldId + '-error');
                if (errorElement) {
                    if (show) {
                        if (message) {
                            errorElement.textContent = message;
                        }
                        errorElement.classList.remove('hidden');
                    } else {
                        errorElement.classList.add('hidden');
                        // Restore default messages
                        if (fieldId === 'email') {
                            errorElement.textContent = 'Please enter your email';
                        } else if (fieldId === 'password') {
                            errorElement.textContent = 'Please enter your password';
                        }
                    }
                }
            }
            
            // Function to show general error message
            function showGeneralError(show, message = null) {
                const errorElement = document.getElementById('login-general-error');
                if (errorElement) {
                    if (show) {
                        if (message) {
                            errorElement.textContent = message;
                        }
                        errorElement.classList.remove('hidden');
                    } else {
                        errorElement.classList.add('hidden');
                    }
                }
            }
            
            // Function to validate email format
            function isValidEmail(email) {
                const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
                return emailRegex.test(email);
            }
            
            // Handle input changes to hide error messages
            $('#email, #password').on('input', function() {
                const fieldId = this.id;
                if (this.value.trim() !== '') {
                    showError(fieldId, false);
                    // Hide general error when user starts typing again
                    showGeneralError(false);
                } else {
                    // Show error if field is empty
                    showError(fieldId, true);
                }
            });
            
            // Handle input blur (focus lost)
            $('#email, #password').on('blur', function() {
                const fieldId = this.id;
                const value = this.value.trim();
                
                if (value === '') {
                    showError(fieldId, true);
                } else if (fieldId === 'email' && !isValidEmail(value)) {
                    showError(fieldId, true, 'Please enter a valid email address');
                }
            });
            
            // Handle login button click
            $('#loginFormBtn').on('click', function(e) {
                // Hide general error
                showGeneralError(false);

                // Get form data
                const email = document.getElementById('email').value.trim();
                const password = document.getElementById('password').value.trim();

                // Validate and show errors
                let hasError = false;

                if (email === '') {
                    showError('email', true);
                    hasError = true;
                } else if (!isValidEmail(email)) {
                    showError('email', true, 'Please enter a valid email address');
                    hasError = true;
                } else {
                    showError('email', false);
                }

                if (password === '') {
                    showError('password', true);
                    hasError = true;
                } else {
                    showError('password', false);
                }

                if (hasError) {
                    return;
                }

                // Show loading
                $('#loginFormBtn').prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Authenticating...');

                // First call GraphQL API for authentication
                const mutation = `
                    mutation Auth_login($email: String!, $password: String!, $role_id: Int!) {
                        auth_login(
                            body: {
                                email: $email,
                                password: $password,
                                role_id: $role_id
                            }
                        ) {
                            id
                        }
                    }
                `;

                const variables = {
                    email: email,
                    password: password,
                    role_id: 3
                };

                console.log('=== GRAPHQL LOGIN ATTEMPT ===');
                console.log('Email:', email);
                console.log('Role ID:', 3);

                $.ajax({
                    url: iptHomeAjax.ajax_url,
                    type: 'POST',
                    dataType: 'json',
                    data: {
                        action: 'ipt_home_graphql',
                        query: mutation,
                        variables: JSON.stringify(variables)
                    },
                    success: function(response) {
                        console.log('=== GRAPHQL LOGIN RESPONSE ===');
                        console.log(response);

                        if (response.errors && response.errors.length > 0) {
                            console.error('GraphQL Login Errors:', response.errors);
                            showGeneralError(true, response.errors[0].message || 'Login failed. Please check your credentials.');
                            $('#loginFormBtn').prop('disabled', false).html('Sign In');
                        } else if (response.data && response.data.auth_login && response.data.auth_login.id) {
                            const designerId = response.data.auth_login.id;
                            console.log('✅ GraphQL Login successful! Designer ID:', designerId);

                            // Now call WordPress AJAX to save designer_id
                            performWordPressLogin(email, password, designerId);
                        } else {
                            console.error('Unexpected GraphQL response:', response);
                            showGeneralError(true, 'Login failed. Please check your credentials.');
                            $('#loginFormBtn').prop('disabled', false).html('Sign In');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('GraphQL Login AJAX Error:', error);
                        console.error('Response:', xhr.responseText);
                        showGeneralError(true, 'Connection error. Please try again later.');
                        $('#loginFormBtn').prop('disabled', false).html('Sign In');
                    }
                });
            });

            // Function to perform WordPress login with designer_id
            function performWordPressLogin(email, password, designerId) {
                console.log('=== WORDPRESS LOGIN ===');
                console.log('Designer ID:', designerId);

                $('#loginFormBtn').html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Logging in...');

                $.ajax({
                    url: '<?php echo admin_url('admin-ajax.php'); ?>',
                    type: 'POST',
                    data: {
                        action: 'ipt_designer_login',
                        email: email,
                        password: password,
                        designer_id: designerId,
                        security: $('#ipt_login_nonce').val()
                    },
                    success: function(response) {
                        console.log('=== WORDPRESS LOGIN RESPONSE ===');
                        console.log(response);

                        // Check response before accessing properties
                        if (response && typeof response === 'object') {
                            if (response.success) {
                                console.log('✅ WordPress login successful!');
                                // Login successful - preserve URL parameters when redirecting
                                const redirectUrl = response.data.redirect || '<?php echo home_url(); ?>/designer/manage-templates';
                                redirectWithParams(redirectUrl);
                            } else {
                                // Show error message
                                showGeneralError(true, response.data.message || 'WordPress login failed.');
                            }
                        } else {
                            // Handle non-object response
                            console.error('Invalid WordPress response format:', response);
                            showGeneralError(true, 'An unexpected error occurred. Please try again.');
                        }
                    },
                    error: function(xhr, status, error) {
                        // Handle Ajax error
                        console.error('WordPress Login AJAX Error:', status, error);
                        showGeneralError(true, 'Connection error. Please try again later.');
                    },
                    complete: function() {
                        // Restore login button
                        $('#loginFormBtn').prop('disabled', false).html('Sign In');
                    }
                });
            }

            // Function to preserve URL parameters when redirecting
            function getUrlWithParams(newUrl) {
                const currentUrl = new URL(window.location.href);
                const newUrlObj = new URL(newUrl, window.location.origin);

                // Check if the new URL is a /designer/ page
                const isDesignerPage = newUrlObj.pathname.startsWith('/designer/');

                console.log('=== URL PARAMETER PRESERVATION ===');
                console.log('Original URL:', currentUrl.href);
                console.log('New URL:', newUrl);
                console.log('Is Designer Page:', isDesignerPage);

                // Only preserve parameters if NOT going to a /designer/ page
                if (!isDesignerPage) {
                    // Get current URL parameters
                    const currentParams = currentUrl.searchParams;

                    // Preserve specific parameters
                    const paramsToPreserve = ['action', 'bypass_token', 'user_id', 'template_id'];

                    paramsToPreserve.forEach(param => {
                        if (currentParams.has(param)) {
                            newUrlObj.searchParams.set(param, currentParams.get(param));
                        }
                    });

                    console.log('Parameters preserved for non-designer page');
                } else {
                    console.log('No parameters preserved for designer page');
                }

                console.log('Final URL:', newUrlObj.href);

                return newUrlObj.href;
            }

            // Function to redirect with conditional parameter preservation
            function redirectWithParams(url) {
                const urlWithParams = getUrlWithParams(url);
                window.location.href = urlWithParams;
            }

            // Handle Enter key press in form
            $('#loginForm').on('keypress', function(e) {
                if (e.which === 13) { // Enter key
                    e.preventDefault();
                    $('#loginFormBtn').click();
                }
            });
        });
    </script>
</body>
</html>
