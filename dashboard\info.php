<?php 
include plugin_dir_path(dirname(__FILE__)) . 'dashboard/designer_header.php'; 
include plugin_dir_path(dirname(__FILE__)) . 'dashboard/designer_breadcrumb.php';
?>
<div class="container-fluid px-0">
  <div class="row mx-0">
    <?php include plugin_dir_path(dirname(__FILE__)) . 'dashboard/designer_sidebar.php'; ?>
    <div id="dash" class="tabcontent">
      <?php 
      // Display breadcrumb
      designer_breadcrumb(
        'Edit Account Information', 
        // array(
        //   array(
        //     'title' => 'Account management',
        //     'link' => home_url('/designer/manage-templates')
        //   )
        // )
      );
      ?>
      
      <div class="account-info-container">
        <div class="row">
          <div class="col-md-7">
            <form id="account-info-form">
              <div class="mb-4">
                <label for="first-name" class="form-label">First name</label>
                <input type="text" class="form-control" id="first-name" placeholder="Placeholder" 
                  value="<?php echo esc_attr(wp_get_current_user()->first_name); ?>">
                <div class="error-message" id="first-name-error"></div>
              </div>
              
              <div class="mb-4">
                <label for="last-name" class="form-label">Last name</label>
                <input type="text" class="form-control" id="last-name" placeholder="Placeholder"
                  value="<?php echo esc_attr(wp_get_current_user()->last_name); ?>">
                <div class="error-message" id="last-name-error"></div>
              </div>
              
              <div class="mb-4">
                <label for="email" class="form-label">Email Address</label>
                <input type="email" class="form-control" id="email" placeholder="Email address" disabled readonly
                  value="<?php echo esc_attr(wp_get_current_user()->user_email); ?>" readonly>
              </div>
              
              <div class="mb-4">
                <label for="phone" class="form-label">Phone Number</label>
                <input type="tel" class="form-control" id="phone" placeholder="Phone number"
                  value="<?php echo esc_attr(get_user_meta(wp_get_current_user()->ID, 'phone', true)); ?>">
                <div class="error-message" id="phone-error"></div>
              </div>
              
              
              <div class="d-flex justify-content-start gap-3">
                <!-- <button type="button" class="btn btn-outline-secondary" id="cancel-btn">Cancel</button> -->
                <button type="submit" class="btn btn-success" id="save-btn">Update</button>
              </div>
              <hr class="my-4">
              
              <h5 class="mb-3">Change Password</h5>
              
              <div class="mb-4">
                <label for="new-password" class="form-label">New Password</label>
                <div class="password-field-wrapper">
                  <input type="password" class="form-control" id="new-password" placeholder="Enter new password">
                  <button type="button" class="password-toggle" aria-label="Toggle password visibility">
                    <i class="dashicons dashicons-visibility"></i>
                  </button>
                </div>
                <div class="password-requirements mt-2">
                  <small class="text-muted">Password must be at least 12 characters and include uppercase, lowercase, number, and special character.</small>
                </div>
                <div class="error-message" id="new-password-error"></div>
              </div>
              
              <div class="mb-4">
                <label for="confirm-password" class="form-label">Confirm New Password</label>
                <div class="password-field-wrapper">
                  <input type="password" class="form-control" id="confirm-password" placeholder="Confirm new password">
                  <button type="button" class="password-toggle" aria-label="Toggle password visibility">
                    <i class="dashicons dashicons-visibility"></i>
                  </button>
                </div>
                <div class="error-message" id="confirm-password-error"></div>
              </div>
              
              <!-- <hr class="my-5"> -->
              
              <div class="d-flex justify-content-start gap-3">
                <!-- <button type="button" class="btn btn-outline-secondary" id="cancel-btn">Cancel</button> -->
                <button type="submit" class="btn btn-success" id="save-btn-password">Change Password</button>
              </div>
            </form>
          </div>
          
          <div class="col-md-5">
            <!-- <div class="avatar-container text-center">
              <h5 class="mb-4">Avatar</h5>
              
              <?php 
                $user_id = wp_get_current_user()->ID;
                $avatar_url = get_avatar_url($user_id, array('size' => 150));
              ?>
              
              <div class="avatar-wrapper mb-3">
                <img src="<?php echo esc_url($avatar_url); ?>" alt="User Avatar" class="rounded-circle" style="width: 120px; height: 120px; object-fit: cover;">
              </div>
              
              <button type="button" class="btn btn-light" id="change-avatar-btn">Change avatar</button>
            </div> -->
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  .account-info-container {
    background-color: #fff;
    border-radius: 8px;
    padding: 30px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  }
  
  .form-control {
    background-color: #f0f5ff;
    border: none;
    padding: 12px 15px;
    height: auto;
  }
  
  .form-control.is-invalid {
    border: 1px solid #dc3545;
    background-color: #fff8f8;
  }
  
  .form-label {
    font-weight: 500;
    margin-bottom: 8px;
  }
  
  #cancel-btn {
    padding: 8px 25px;
  }
  
  #save-btn, #save-btn-password {
    padding: 8px 25px;
    background-color: #4DCDB4;
    border-color: #4DCDB4;
  }
  
  #change-avatar-btn {
    background-color: #fff2e9;
    color: #ff8c42;
    border: none;
    padding: 8px 15px;
  }
  
  .avatar-container {
    padding-top: 20px;
  }
  
  /* Password field styles */
  .password-field-wrapper {
    position: relative;
  }
  
  .password-toggle {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
    color: #6c757d;
  }
  
  .password-toggle:hover {
    color: #4DCDB4;
  }
  
  .password-requirements {
    font-size: 12px;
    color: #6c757d;
  }
  
  .error-message {
    color: #dc3545;
    font-size: 12px;
    margin-top: 5px;
    min-height: 18px;
  }
</style>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Function to show error message
    function showError(fieldId, message) {
      const field = document.getElementById(fieldId);
      const errorElement = document.getElementById(fieldId + '-error');
      
      if (field && errorElement) {
        field.classList.add('is-invalid');
        errorElement.textContent = message;
      }
    }
    
    // Function to clear error message
    function clearError(fieldId) {
      const field = document.getElementById(fieldId);
      const errorElement = document.getElementById(fieldId + '-error');
      
      if (field && errorElement) {
        field.classList.remove('is-invalid');
        errorElement.textContent = '';
      }
    }
    
    // Function to clear all errors
    function clearAllErrors() {
      const errorElements = document.querySelectorAll('.error-message');
      const invalidFields = document.querySelectorAll('.is-invalid');
      
      errorElements.forEach(element => {
        element.textContent = '';
      });
      
      invalidFields.forEach(field => {
        field.classList.remove('is-invalid');
      });
    }
    
    // Password validation function
    function validatePassword(password) {
      // Check minimum length
      if (password.length < 12) {
        return {
          valid: false,
          message: 'Password must be at least 12 characters long.'
        };
      }
      
      // Check for uppercase letter
      if (!/[A-Z]/.test(password)) {
        return {
          valid: false,
          message: 'Password must include at least one uppercase letter.'
        };
      }
      
      // Check for lowercase letter
      if (!/[a-z]/.test(password)) {
        return {
          valid: false,
          message: 'Password must include at least one lowercase letter.'
        };
      }
      
      // Check for number
      if (!/\d/.test(password)) {
        return {
          valid: false,
          message: 'Password must include at least one number.'
        };
      }
      
      // Check for special character
      if (!/[^a-zA-Z0-9]/.test(password)) {
        return {
          valid: false,
          message: 'Password must include at least one special character.'
        };
      }
      
      return {
        valid: true
      };
    }
    
    // Function to show success message
    function showSuccessMessage(message) {
      const successMessage = document.createElement('div');
      successMessage.className = 'alert alert-success mt-3';
      successMessage.textContent = message;
      
      // Insert the success message at the top of the form
      const form = document.getElementById('account-info-form');
      form.parentNode.insertBefore(successMessage, form);
      
      // Remove the success message after 3 seconds
      setTimeout(() => {
        successMessage.remove();
      }, 3000);
    }
    
    // Function to show error message (for API errors)
    function showApiError(message) {
      const errorMessage = document.createElement('div');
      errorMessage.className = 'alert alert-danger mt-3';
      errorMessage.textContent = message;
      
      // Insert the error message at the top of the form
      const form = document.getElementById('account-info-form');
      form.parentNode.insertBefore(errorMessage, form);
      
      // Remove the error message after 5 seconds
      setTimeout(() => {
        errorMessage.remove();
      }, 5000);
    }
    
    // Function to execute GraphQL mutation via PHP
    function executeGraphQLMutation(mutation, variables = {}) {
      return new Promise((resolve, reject) => {
        jQuery.ajax({
          url: '<?php echo admin_url('admin-ajax.php'); ?>',
          type: 'POST',
          dataType: 'json',
          data: {
            action: 'ipt_home_graphql',
            query: mutation,
            variables: JSON.stringify(variables)
          },
          success: function(response) {
            if (response.errors && response.errors.length > 0) {
              reject(new Error(response.errors[0].message || 'GraphQL error occurred'));
            } else if (response.data) {
              resolve(response.data);
            } else {
              reject(new Error('Invalid response from server'));
            }
          },
          error: function(xhr, status, error) {
            console.error('AJAX Error:', error);
            reject(new Error('Failed to connect to server'));
          }
        });
      });
    }
    
    // Function to update WordPress user data
    async function updateWordPressUser(userData) {
      return new Promise((resolve, reject) => {
        jQuery.ajax({
          url: '<?php echo admin_url('admin-ajax.php'); ?>',
          type: 'POST',
          dataType: 'json',
          data: {
            action: 'update_user_profile',
            security: '<?php echo wp_create_nonce("update_user_profile_nonce"); ?>',
            ...userData
          },
          success: function(response) {
            if (response.success) {
              resolve(response.data);
            } else {
              reject(new Error(response.data?.message || 'Failed to update WordPress user data'));
            }
          },
          error: function(xhr, status, error) {
            console.error('AJAX Error:', error);
            reject(new Error('Failed to connect to server'));
          }
        });
      });
    }
    
    // Add input event listeners to clear errors when user typesbe
    document.querySelectorAll('input').forEach(input => {
      input.addEventListener('input', function() {
        clearError(this.id);
      });
    });
    
    // Prevent default form submission and handle button clicks separately
    document.getElementById('account-info-form').addEventListener('submit', function(e) {
      e.preventDefault();
    });
    
    // Handle profile info update (save-btn)
    document.getElementById('save-btn').addEventListener('click', async function(e) {
      e.preventDefault();
      
      // Clear all previous errors
      clearAllErrors();
      
      // Get form values for profile info
      const firstName = document.getElementById('first-name').value;
      const lastName = document.getElementById('last-name').value;
      const phone = document.getElementById('phone').value;
      const email = document.getElementById('email').value;
      
      let hasErrors = false;
      
      // Validate first name
      if (!firstName.trim()) {
        showError('first-name', 'First name is required.');
        hasErrors = true;
      }
      
      // Validate last name
      if (!lastName.trim()) {
        showError('last-name', 'Last name is required.');
        hasErrors = true;
      }
      
      // Validate phone (optional validation example)
      if (phone && !/^[0-9+\-\s()]*$/.test(phone)) {
        showError('phone', 'Please enter a valid phone number.');
        hasErrors = true;
      }
      
      // If there are errors, stop form submission
      if (hasErrors) {
        return;
      }
      
      // Show loading state
      const originalButtonText = this.textContent;
      this.textContent = 'Updating...';
      this.disabled = true;
      
      try {
        // GraphQL mutation for updating user info - Sử dụng biến thay vì nhúng trực tiếp
        const updateUserMutation = `
          mutation Webhooks_users_update($email: String!, $firstName: String!, $lastName: String!, $phone: String!) {
            webhooks_users_update(
              body: { 
                email: $email, 
                role_id: 3, 
                first_name: $firstName, 
                last_name: $lastName, 
                phone: $phone 
              }
            ) {
              email
              id
            }
          }
        `;
        
        // Biến cho mutation
        const variables = {
          email: email,
          firstName: firstName,
          lastName: lastName,
          phone: phone
        };
        
        // First update via GraphQL
        const result = await executeGraphQLMutation(updateUserMutation, variables);
        
        if (result && result.webhooks_users_update) {
          // If GraphQL update is successful, update WordPress user data
          await updateWordPressUser({
            first_name: firstName,
            last_name: lastName,
            phone: phone
          });
          
          // Update displayed values in the form
          document.getElementById('first-name').value = firstName;
          document.getElementById('last-name').value = lastName;
          document.getElementById('phone').value = phone;
          
          showSuccessMessage('Profile information updated successfully!');
        } else {
          showApiError('Failed to update profile information.');
        }
      } catch (error) {
        showApiError(error.message || 'An error occurred while updating profile information.');
      } finally {
        // Restore button state
        this.textContent = originalButtonText;
        this.disabled = false;
      }
    });
    
    // Handle password update (save-btn-password)
    document.getElementById('save-btn-password').addEventListener('click', async function(e) {
      e.preventDefault();
      
      // Clear all previous errors
      clearAllErrors();
      
      // Get password values
      const newPassword = document.getElementById('new-password').value;
      const confirmPassword = document.getElementById('confirm-password').value;
      const email = document.getElementById('email').value;
      
      let hasErrors = false;
      
      // Check if both fields are filled
      if (!newPassword) {
        showError('new-password', 'Please enter a new password.');
        hasErrors = true;
      }
      
      if (!confirmPassword) {
        showError('confirm-password', 'Please confirm your new password.');
        hasErrors = true;
      }
      
      // Check if passwords match
      if (newPassword && confirmPassword && newPassword !== confirmPassword) {
        showError('confirm-password', 'Passwords do not match.');
        hasErrors = true;
      }
      
      // Validate password against rules
      if (newPassword) {
        const passwordValidation = validatePassword(newPassword);
        if (!passwordValidation.valid) {
          showError('new-password', passwordValidation.message);
          hasErrors = true;
        }
      }
      
      // If there are errors, stop form submission
      if (hasErrors) {
        return;
      }
      
      // Show loading state
      const originalButtonText = this.textContent;
      this.textContent = 'Updating...';
      this.disabled = true;
      
      try {
        // GraphQL mutation for updating password - Sử dụng biến thay vì nhúng trực tiếp
        const updatePasswordMutation = `
          mutation Webhooks_users_update($email: String!, $password: String!) {
            webhooks_users_update(
              body: { 
                email: $email, 
                role_id: 3, 
                password: $password 
              }
            ) {
              email
              id
            }
          }
        `;
        
        // Biến cho mutation
        const variables = {
          email: email,
          password: newPassword
        };
        
        // First update via GraphQL
        const result = await executeGraphQLMutation(updatePasswordMutation, variables);
        
        if (result && result.webhooks_users_update) {
          // If GraphQL update is successful, update WordPress user password
          await updateWordPressUser({
            password: newPassword
          });
          
          showSuccessMessage('Password changed successfully!');
          
          // Clear password fields after successful update
          document.getElementById('new-password').value = '';
          document.getElementById('confirm-password').value = '';
        } else {
          showApiError('Failed to update password.');
        }
      } catch (error) {
        showApiError(error.message || 'An error occurred while updating password.');
      } finally {
        // Restore button state
        this.textContent = originalButtonText;
        this.disabled = false;
      }
    });
    
    // Toggle password visibility
    document.querySelectorAll('.password-toggle').forEach(function(button) {
      button.addEventListener('click', function() {
        const input = this.previousElementSibling;
        const icon = this.querySelector('i');
        
        if (input.type === 'password') {
          input.type = 'text';
          icon.classList.remove('dashicons-visibility');
          icon.classList.add('dashicons-hidden');
        } else {
          input.type = 'password';
          icon.classList.remove('dashicons-hidden');
          icon.classList.add('dashicons-visibility');
        }
      });
    });
  });
</script>

<?php include plugin_dir_path(dirname(__FILE__)) . 'dashboard/designer_footer.php'; ?>
