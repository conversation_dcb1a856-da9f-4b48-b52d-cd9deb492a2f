/**
 * Visitor Customer Edits - Lightweight script for applying published customer edits
 * This script runs for visitors (non-logged-in users) to apply real-time customer edits
 */

// Smart responsive image update function
function updateResponsiveImage($element, newImageUrl) {
    try {
        console.log('🖼️ VISITOR: Updating responsive image with URL:', newImageUrl);

        // Get current srcset and sizes to understand the responsive setup
        var currentSrcset = $element.attr('srcset');
        var currentSizes = $element.attr('sizes');

        console.log('📊 VISITOR: Current srcset:', currentSrcset);
        console.log('📊 VISITOR: Current sizes:', currentSizes);

        // Update the main src attribute
        $element.attr('src', newImageUrl);

        // If there was a srcset, generate a new one based on the new image
        if (currentSrcset && currentSrcset.trim() !== '') {
            var newSrcset = generateResponsiveSrcset(newImageUrl, currentSrcset);
            if (newSrcset) {
                $element.attr('srcset', newSrcset);
                console.log('✅ VISITOR: Updated srcset:', newSrcset);
            } else {
                // If we can't generate proper srcset, remove it to prevent conflicts
                $element.removeAttr('srcset');
                console.log('⚠️ VISITOR: Removed srcset (could not generate proper responsive set)');
            }
        }

        // Keep sizes attribute if it exists (it defines responsive behavior)
        if (currentSizes && currentSizes.trim() !== '') {
            console.log('✅ VISITOR: Preserved sizes attribute:', currentSizes);
        }

        // Add cache-busting parameter to force reload
        var cacheBustUrl = newImageUrl + (newImageUrl.includes('?') ? '&' : '?') + 'cb=' + Date.now();
        $element.attr('src', cacheBustUrl);

        console.log('✅ VISITOR: Responsive image update completed');
        return true;

    } catch (error) {
        console.error('❌ VISITOR: Error updating responsive image:', error);
        // Fallback to simple src update
        $element.attr('src', newImageUrl);
        return false;
    }
}

// Generate responsive srcset from new image URL - keep same URL with original size descriptors
function generateResponsiveSrcset(newImageUrl, originalSrcset) {
    try {
        // Parse the original srcset to understand the size pattern
        var srcsetEntries = originalSrcset.split(',').map(function(entry) {
            return entry.trim();
        });

        console.log('🔍 VISITOR: Parsing srcset entries:', srcsetEntries);

        var newSrcsetEntries = [];

        srcsetEntries.forEach(function(entry) {
            var parts = entry.split(' ');
            if (parts.length >= 2) {
                var sizeDescriptor = parts[parts.length - 1]; // Last part is the size descriptor (e.g., "300w", "2x")

                // Use the SAME new image URL for all sizes, just change the size descriptor
                newSrcsetEntries.push(newImageUrl + ' ' + sizeDescriptor);
            }
        });

        if (newSrcsetEntries.length > 0) {
            var newSrcset = newSrcsetEntries.join(', ');
            console.log('✅ VISITOR: Generated new srcset (same URL, original sizes):', newSrcset);
            return newSrcset;
        }

        console.log('⚠️ VISITOR: Could not generate valid srcset');
        return null;

    } catch (error) {
        console.error('❌ VISITOR: Error generating srcset:', error);
        return null;
    }
}



// Smart selector resolution for nth-of-type selectors
function trySmartSelectorResolution(originalSelector, fieldType) {


    // Strategy 1: Look for elements with data-id attributes
    var $allElements = jQuery('*[data-id]');


    // Try to find the target element type in elements with data-id
    var targetElement = '';
    if (fieldType === 'image') {
        targetElement = 'img';
    } else if (fieldType === 'text') {
        if (originalSelector.includes('h1')) targetElement = 'h1';
        else if (originalSelector.includes('h2')) targetElement = 'h2';
        else if (originalSelector.includes('h3')) targetElement = 'h3';
        else if (originalSelector.includes('h4')) targetElement = 'h4';
        else if (originalSelector.includes('h5')) targetElement = 'h5';
        else if (originalSelector.includes('h6')) targetElement = 'h6';
        else if (originalSelector.includes('p')) targetElement = 'p';
    } else if (fieldType === 'button' || fieldType === 'link') {
        targetElement = 'a';
    }

    if (targetElement) {
        // Look for the target element within data-id containers
        var $candidates = jQuery('[data-id] ' + targetElement + ', [data-id]' + targetElement);


        if ($candidates.length === 1) {
            // Perfect! Found exactly one candidate
            var $element = $candidates.first();
            var dataId = $element.closest('[data-id]').attr('data-id');
            var smartSelector = '[data-id="' + dataId + '"] ' + targetElement;

            return smartSelector;
        } else if ($candidates.length > 1) {
            // Multiple candidates - try to find the best match

            // For now, return the first one
            var $element = $candidates.first();
            var dataId = $element.closest('[data-id]').attr('data-id');
            var smartSelector = '[data-id="' + dataId + '"] ' + targetElement;

            return smartSelector;
        }
    }


    return null;
}

// Function to update site element (simplified version for visitors)
function updateSiteElement(selector, value, fieldType, fieldSubtype) {
    try {
        var $element = jQuery(selector);
        if ($element.length === 0) {

            return false;
        }



        switch (fieldType) {
            case 'text':
                if (fieldSubtype === 'heading') {
                    // For headings, update text content
                    $element.text(value);
                } else {
                    // For other text elements, update text content
                    $element.text(value);
                }
                break;

            case 'image':
                // Smart responsive image handling - preserve layout integrity
                updateResponsiveImage($element, value);
                break;

            case 'button':
            case 'link':
                if (fieldSubtype === 'url') {
                    // Update href attribute
                    $element.attr('href', value);
                } else {
                    // Update link text
                    $element.text(value);
                }
                break;

            case 'iframe':
            case 'video':
                // Update src attribute for iframes and videos
                $element.attr('src', value);
                break;

            case 'icon':
                // For icons, might be updating src or content
                if (value.startsWith('http') || value.startsWith('/')) {
                    $element.attr('src', value);
                } else {
                    $element.html(value);
                }
                break;

            default:

                return false;
        }


        return true;

    } catch (error) {

        return false;
    }
}

// VISITOR REAL-TIME LOADING: Apply published customer edits for visitors
function applyVisitorCustomerEdits() {
    console.log('🔍 VISITOR: Starting applyVisitorCustomerEdits');
    console.log('🔍 VISITOR: designerTagging available:', typeof designerTagging !== 'undefined');

    if (typeof designerTagging !== 'undefined') {
        console.log('🔍 VISITOR: customer_edits available:', !!designerTagging.customer_edits);
        console.log('🔍 VISITOR: customer_edits length:', designerTagging.customer_edits ? designerTagging.customer_edits.length : 0);
        console.log('🔍 VISITOR: customer_edits data:', designerTagging.customer_edits);
    }

    if (!designerTagging.customer_edits || designerTagging.customer_edits.length === 0) {
        console.log('❌ VISITOR: No customer edits found or empty array');
        return;
    }

    let appliedCount = 0;
    let failedCount = 0;

    console.log('🚀 VISITOR: Processing', designerTagging.customer_edits.length, 'customer edits');

    designerTagging.customer_edits.forEach(function(edit, index) {
        console.log('📝 VISITOR: Processing edit', index + 1, '/', designerTagging.customer_edits.length);
        console.log('  - Selector:', edit.selector);
        console.log('  - Visitor Selector:', edit.visitor_selector);
        console.log('  - Value:', edit.value);
        console.log('  - Field Type:', edit.field_type);
        console.log('  - Field Subtype:', edit.field_subtype);


        // Check if element exists before applying
        var $element = jQuery(edit.selector);
        console.log('  - Original selector elements found:', $element.length);


        try {
            // Multi-tier selector strategy for maximum reliability
            var success = false;
            var selectorUsed = edit.selector;
            var strategyUsed = '';

            // First priority: Use visitor_selector if available (most reliable)
            if (edit.visitor_selector && edit.visitor_selector !== edit.selector) {
                console.log('🎯 VISITOR: Trying visitor_selector:', edit.visitor_selector);
                var $visitorElement = jQuery(edit.visitor_selector);
                console.log('  - Visitor selector elements found:', $visitorElement.length);

                if (updateSiteElement(edit.visitor_selector, edit.value, edit.field_type, edit.field_subtype)) {
                    success = true;
                    selectorUsed = edit.visitor_selector;
                    strategyUsed = 'VISITOR_SELECTOR';
                    console.log('✅ VISITOR: Success with visitor_selector');
                } else {
                    console.log('❌ VISITOR: Failed with visitor_selector');
                }
            }

            // Second priority: Try original selector
            if (!success) {
                console.log('🎯 VISITOR: Trying original selector:', edit.selector);
                if (updateSiteElement(edit.selector, edit.value, edit.field_type, edit.field_subtype)) {
                    success = true;
                    selectorUsed = edit.selector;
                    strategyUsed = 'ORIGINAL_SELECTOR';
                    console.log('✅ VISITOR: Success with original selector');
                } else {
                    console.log('❌ VISITOR: Failed with original selector');
                }
            }

            // Third priority: Smart selector resolution for nth-of-type selectors
            if (!success && edit.selector.includes('nth-of-type')) {
                console.log('🔄 VISITOR: Trying smart selector resolution for nth-of-type');
                var smartSelector = trySmartSelectorResolution(edit.selector, edit.field_type);
                if (smartSelector && smartSelector !== edit.selector) {
                    console.log('✨ VISITOR: Smart selector found:', smartSelector);
                    var $smartElement = jQuery(smartSelector);
                    console.log('  - Smart selector elements found:', $smartElement.length);

                    if (updateSiteElement(smartSelector, edit.value, edit.field_type, edit.field_subtype)) {
                        success = true;
                        selectorUsed = smartSelector;
                        strategyUsed = 'SMART_RESOLUTION';
                        console.log('✅ VISITOR: Success with smart selector');
                    } else {
                        console.log('❌ VISITOR: Failed with smart selector');
                    }
                } else {
                    console.log('❌ VISITOR: No smart selector found');
                }
            }

            if (success) {
                appliedCount++;
                console.log('✅ VISITOR: Edit applied successfully using', strategyUsed);
            } else {
                failedCount++;
                console.log('❌ VISITOR: Edit failed - no strategy worked');
            }
        } catch (error) {
            failedCount++;
            console.error('❌ VISITOR: Error processing edit:', error, edit);
        }
    });

    console.log('📊 VISITOR: Final Results:');
    console.log('  ✅ Successfully applied:', appliedCount);
    console.log('  ❌ Failed to apply:', failedCount);
    console.log('  📈 Success rate:', appliedCount + '/' + (appliedCount + failedCount));
}

// Initialize visitor customer edits when jQuery and DOM are ready
function initVisitorCustomerEdits() {

    
    // Apply customer edits if available
    if (typeof designerTagging !== 'undefined' && designerTagging.customer_edits) {

        applyVisitorCustomerEdits();
    } else {

    }
}

// jQuery Safety Wrapper - Initialize when jQuery is available
function safeInitVisitorCustomerEdits() {
    if (typeof jQuery !== 'undefined') {
        jQuery(document).ready(initVisitorCustomerEdits);
    } else if (typeof $ !== 'undefined') {
        $(document).ready(initVisitorCustomerEdits);
    } else {

        setTimeout(function() {
            if (typeof jQuery !== 'undefined') {
                jQuery(document).ready(initVisitorCustomerEdits);
            } else if (typeof $ !== 'undefined') {
                $(document).ready(initVisitorCustomerEdits);
            } else {

                // Try to run without jQuery for basic functionality
                if (document.readyState === 'loading') {
                    document.addEventListener('DOMContentLoaded', initVisitorCustomerEdits);
                } else {
                    initVisitorCustomerEdits();
                }
            }
        }, 1000);
    }
}

// Initialize immediately
safeInitVisitorCustomerEdits();
