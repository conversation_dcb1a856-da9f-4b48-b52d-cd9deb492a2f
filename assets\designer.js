// Master tags object - will be populated from GraphQL API
let masterTags = {};

// Function to fetch master tags from GraphQL API
function fetchMasterTags() {
    return new Promise((resolve, reject) => {
        // Check if designerTagging is available
        if (typeof designerTagging === 'undefined') {
            console.error('designerTagging is not defined - cannot fetch master tags');
            reject(new Error('designerTagging is not defined'));
            return;
        }

        const query = `
            query Webhooks_master_tags_all {
                webhooks_master_tags_all {
                    id
                    name
                    type
                    display_name
                    tool_tip
                }
            }
        `;

        // Use existing GraphQL function via AJAX
        jQuery.ajax({
            url: designerTagging.ajax_url, // WordPress AJAX URL
            type: 'POST',
            dataType: 'json',
            data: {
                action: 'ipt_home_graphql',
                query: query,
                variables: JSON.stringify({})
            },
            success: function(response) {
                console.log('Master Tags GraphQL Response:', response);

                if (response.errors && response.errors.length > 0) {
                    console.error('GraphQL Error:', response.errors[0].message);
                    reject(new Error(response.errors[0].message));
                    return;
                }

                if (response.data && response.data.webhooks_master_tags_all) {
                    // Convert API response to masterTags format (name: type)
                    const apiTags = response.data.webhooks_master_tags_all;
                    const newMasterTags = {};

                    apiTags.forEach(tag => {
                        newMasterTags[tag.name] = tag.type;
                    });

                    // Update global masterTags object
                    masterTags = newMasterTags;
                    console.log('Master Tags loaded from API:', masterTags);
                    resolve(masterTags);
                } else {
                    console.error('Invalid GraphQL response structure');
                    reject(new Error('Invalid response from GraphQL server'));
                }
            },
            error: function(xhr, status, error) {
                console.error('Failed to fetch master tags:', error);
                reject(new Error('Failed to connect to GraphQL server: ' + error));
            }
        });
    });
}

// Initialize master tags on page load
function initializeMasterTags() {
    fetchMasterTags()
        .then(tags => {
            console.log('Master tags initialized successfully');
            // Refresh any existing master tags UI if needed
            refreshMasterTagsUI();
        })
        .catch(error => {
            console.error('Failed to initialize master tags:', error);
            // Fallback to empty object if API fails
            masterTags = {};
            refreshMasterTagsUI();
        });
}

// Function to refresh master tags UI after loading from API
function refreshMasterTagsUI() {
    // Update master tags submenu if it exists
    const $masterTagsSubmenu = jQuery('#master-tagging-submenu');
    if ($masterTagsSubmenu.length > 0) {
        // Get current filter type if any
        const currentFilterType = $masterTagsSubmenu.data('current-filter') || null;
        const filteredHtml = createMasterTagsHtml(currentFilterType);
        $masterTagsSubmenu.html(filteredHtml);
    }

    // Update any other master tags displays
    const $masterTagsContainer = jQuery('.master-tags-container');
    if ($masterTagsContainer.length > 0) {
        const allTagsHtml = createMasterTagsHtml();
        $masterTagsContainer.html(allTagsHtml);
    }
}

// Hàm tạo HTML cho master tags dựa trên type
function createMasterTagsHtml(filterType = null) {
    let tagsHtml = '';

    // Check if master tags are loaded
    if (Object.keys(masterTags).length === 0) {
        return '<div class="text-center py-4"><i class="fa fa-spinner fa-spin"></i> Loading master tags...</div>';
    }

    // Loop qua mảng masterTags
    for (const tagName in masterTags) {
        const tagType = masterTags[tagName];

        // Nếu có filterType, chỉ hiển thị tags có type phù hợp
        if (filterType && tagType !== filterType) {
            continue;
        }

        // Kiểm tra xem tag đã được sử dụng chưa
        if (typeof isTagNameAlreadyUsed === 'function' && isTagNameAlreadyUsed(tagName)) {
            // Nếu đã được sử dụng, hiển thị với style disabled
            tagsHtml += `<div class="master-tag-item mb-2 p-2 rounded master-tag-disabled"
                             data-tag-type="${tagType}" data-tag-name="${tagName}" title="This tag is already used">
                            ${tagName} <span class="tag-used-indicator">(Used)</span>
                        </div>`;
        } else {
            // Nếu chưa được sử dụng, hiển thị bình thường
            tagsHtml += `<div class="master-tag-item mb-2 cursor-pointer hover:bg-gray-100 p-2 rounded"
                             data-tag-type="${tagType}" data-tag-name="${tagName}">
                            ${tagName}
                        </div>`;
        }
    }

    // If no tags match the filter, show appropriate message
    if (tagsHtml === '' && filterType) {
        tagsHtml = `<div class="text-center py-4 text-gray-500">No ${filterType} tags available</div>`;
    } else if (tagsHtml === '') {
        tagsHtml = '<div class="text-center py-4 text-gray-500">No master tags available</div>';
    }

    return tagsHtml;
}

// Hàm cập nhật master tags submenu
function updateMasterTagsForType(elementType) {
    const filteredHtml = createMasterTagsHtml(elementType);
    const $submenu = jQuery('#master-tagging-submenu');
    $submenu.html(filteredHtml);
    // Store current filter type for refresh purposes
    $submenu.data('current-filter', elementType);
}

/**
 * Quét tất cả iframe và video tags trên trang và thêm class auto-detect-tag
 */
function detectMediaElements() {
    // Kiểm tra xem có đang ở trong Elementor Editor không
    if (typeof designerTagging !== 'undefined' && designerTagging.is_elementor_editor === true) {
        return; // Không quét phần tử trong Elementor Editor
    }

    // Disable media detection in approval mode
    if (typeof window.isApprovalMode !== 'undefined' && window.isApprovalMode === true) {

        return;
    }

    // Disable media detection in customer edit mode - customers only edit existing tagged content
    if (typeof designerTagging !== 'undefined' && designerTagging.customer_edit_mode === true) {

        return;
    }
    

    
    // Kiểm tra xem có dữ liệu tagged fields không
    let taggedSelectors = [];
    if (typeof designerTagging !== 'undefined' && designerTagging.tagged_fields) {
        try {
            // Lấy danh sách các selector đã được tag
            const taggedFields = JSON.parse(designerTagging.tagged_fields);
            taggedSelectors = taggedFields.map(field => field.selector);

        } catch (e) {

        }
    }
    
    // Tìm tất cả iframe trên trang
    jQuery('iframe').each(function(index) {

        const $iframe = jQuery(this);
        const iframeId = $iframe.attr('id') || '';
        
        // Bỏ qua iframe ẩn hoặc có kích thước 0
        if ($iframe.is(':hidden') || $iframe.width() === 0 || $iframe.height() === 0) {
            return;
        }
        
        // Kiểm tra xem iframe có trong danh sách đã tag không
        const iframeSelector = 'iframe' + (iframeId ? '#' + iframeId : '');
        const isAlreadyTagged = taggedSelectors.some(selector => 
            selector === iframeSelector || 
            (iframeId && selector.includes('#' + iframeId))
        );
        
        if (isAlreadyTagged) {

            $iframe.attr('data-designer-editable', 'true');
            $iframe.attr('data-overlay-applied', 'true');
            $iframe.addClass('designer-highlight');
            return;
        }
        
        // Kiểm tra kỹ hơn nếu iframe đã được tagged
        if ($iframe.attr('data-designer-editable') || 
            $iframe.attr('data-overlay-applied') || 
            $iframe.hasClass('designer-highlight') ||
            $iframe.parent().find('.tagged-media-overlay').length > 0) {

            return;
        }
        
        $iframe.addClass('auto-detect-tag');
        
        // Thêm ID duy nhất cho iframe nếu chưa có
        if (!iframeId) {
            $iframe.attr('id', 'auto-iframe-' + index);

        }
        
        // Đảm bảo parent có position relative
        const $parent = $iframe.parent();
        if ($parent.css('position') !== 'relative' && 
            $parent.css('position') !== 'absolute' && 
            $parent.css('position') !== 'fixed') {
            $parent.css('position', 'relative');
        }
        
        // Thêm một div phủ lên trên iframe để ngăn tương tác
        if ($parent.find('.iframe-overlay[data-iframe-index="' + index + '"]').length === 0) {
            const $overlay = jQuery('<div class="iframe-overlay auto-detect-tag-overlay"></div>');
            $overlay.attr('data-iframe-index', index);
            $overlay.attr('data-target-id', $iframe.attr('id'));
            
            // Tính toán vị trí và kích thước chính xác
            const iframeOffset = $iframe.position();
            let width = $iframe.outerWidth();
            let height = $iframe.outerHeight();
            
            // Đảm bảo kích thước hợp lệ
            if (width <= 0) width = $iframe.width();
            if (height <= 0) height = $iframe.height();
            
            // Nếu vẫn không có kích thước hợp lệ, thử lấy từ style
            if (width <= 0) width = parseInt($iframe.css('width')) || 300;
            if (height <= 0) height = parseInt($iframe.css('height')) || 150;
            

            
            $overlay.css({
                position: 'absolute',
                top: iframeOffset.top,
                left: iframeOffset.left,
                width: width,
                height: height,
                background: 'transparent',
                zIndex: 99999, // Z-index cao nhất
                pointerEvents: 'all', // Chặn tương tác với phần tử bên dưới
                cursor: 'pointer' // Thêm cursor pointer để người dùng biết có thể click
            });
            
            // Lưu tham chiếu đến iframe gốc
            $overlay.data('target-element', $iframe[0]);
            
            $parent.append($overlay);
        }
    });
    
    // Tìm tất cả video tags trên trang - tương tự như iframe
    jQuery('video').each(function(index) {
        const $video = jQuery(this);
        const videoId = $video.attr('id') || '';
        
        // Bỏ qua video ẩn hoặc có kích thước 0
        if ($video.is(':hidden') || $video.width() === 0 || $video.height() === 0) {
            return;
        }
        
        // Kiểm tra xem video có trong danh sách đã tag không
        const videoSelector = 'video' + (videoId ? '#' + videoId : '');
        const isAlreadyTagged = taggedSelectors.some(selector => 
            selector === videoSelector || 
            (videoId && selector.includes('#' + videoId))
        );
        
        if (isAlreadyTagged) {

            $video.attr('data-designer-editable', 'true');
            $video.attr('data-overlay-applied', 'true');
            $video.addClass('designer-highlight');
            return;
        }
        
        // Bỏ qua video đã được tagged hoặc đã có overlay
        if ($video.attr('data-designer-editable') || 
            $video.attr('data-overlay-applied') || 
            $video.hasClass('designer-highlight') ||
            $video.parent().find('.tagged-media-overlay').length > 0) {

            return;
        }
        
        $video.addClass('auto-detect-tag');
        
        // Thêm ID duy nhất cho video nếu chưa có
        if (!videoId) {
            $video.attr('id', 'auto-video-' + index);

        }
        
        // Phần còn lại tương tự như iframe
        // ...
    });
    
    // Sau khi quét xong, thêm overlay cho các phần tử đã được tag
    addOverlaysToTaggedElements();
}

// Thêm hàm mới để thêm overlay cho các phần tử đã được tag
function addOverlaysToTaggedElements() {
    if (typeof designerTagging === 'undefined' || !designerTagging.tagged_fields) {
        return;
    }
    
    try {
        const taggedFields = JSON.parse(designerTagging.tagged_fields);
        
        taggedFields.forEach(field => {
            const selector = field.selector;
            const $element = jQuery(selector);
            
            if ($element.length && ($element.is('iframe') || $element.is('video'))) {
                // Đánh dấu phần tử đã được tag
                $element.attr('data-designer-editable', field.label || 'true');
                $element.attr('data-overlay-applied', 'true');
                $element.addClass('designer-highlight');
                
                // Xóa overlay auto-detect nếu có
                const $parent = $element.parent();
                $parent.find('.iframe-overlay, .video-overlay').remove();
                
                // Kiểm tra xem đã có overlay tagged chưa
                if ($parent.find('.tagged-media-overlay[data-target-id="' + $element.attr('id') + '"]').length === 0) {
                    // Tính toán vị trí và kích thước chính xác
                    const elOffset = $element.position();
                    let width = $element.outerWidth();
                    let height = $element.outerHeight();
                    
                    // Đảm bảo kích thước hợp lệ
                    if (width <= 0) width = $element.width();
                    if (height <= 0) height = $element.height();
                    
                    // Nếu vẫn không có kích thước hợp lệ, thử lấy từ style
                    if (width <= 0) width = parseInt($element.css('width')) || 300;
                    if (height <= 0) height = parseInt($element.css('height')) || 150;
                    
                    // Tạo overlay cho phần tử đã được tag
                    const $taggedOverlay = jQuery('<div class="tagged-media-overlay"></div>');
                    $taggedOverlay.attr('data-target-id', $element.attr('id'));
                    
                    $taggedOverlay.css({
                        position: 'absolute',
                        top: elOffset.top,
                        left: elOffset.left,
                        width: width,
                        height: height,
                        background: 'rgba(76, 175, 80, 0.3)', // Màu xanh lá nhạt
                        border: '2px solid #4CAF50', // Viền xanh lá
                        zIndex: 99999, // Z-index cao nhất
                        pointerEvents: 'all', // Chặn tương tác với phần tử bên dưới
                        cursor: 'pointer' // Thêm cursor pointer để người dùng biết có thể click
                    });
                    
                    // Thêm nhãn hiển thị
                    const $tagLabel = jQuery('<div class="tagged-media-label"></div>');
                    $tagLabel.css({
                        position: 'absolute',
                        top: '5px',
                        left: '5px',
                        background: '#4CAF50',
                        color: 'white',
                        padding: '2px 8px',
                        borderRadius: '3px',
                        fontSize: '12px',
                        fontWeight: 'bold',
                        maxWidth: '90%',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap'
                    });
                    
                    // Hiển thị nhãn và giá trị
                    let labelText = field.label;
                    if (field.value) {
                        // Giới hạn độ dài của giá trị hiển thị
                        const maxValueLength = 20;
                        let displayValue = field.value;
                        if (displayValue.length > maxValueLength) {
                            displayValue = displayValue.substring(0, maxValueLength) + '...';
                        }
                        labelText += ': ' + displayValue;
                    }
                    
                    $tagLabel.text(labelText);
                    $taggedOverlay.append($tagLabel);
                    $parent.append($taggedOverlay);
                    
                    // Lưu tham chiếu đến phần tử gốc và ID
                    $taggedOverlay.data('target-element', $element[0]);
                    if ($element.attr('id')) {
                        $taggedOverlay.attr('data-target-id', $element.attr('id'));
                    }
                }
            }
        });
    } catch (e) {
    }
}

// Thêm hàm để quét lại các phần tử media
function rescanMediaElements() {
    // Disable media rescan in approval mode
    if (typeof window.isApprovalMode !== 'undefined' && window.isApprovalMode === true) {
        return;
    }

    // Disable media rescan in customer edit mode - customers only edit existing tagged content
    if (typeof designerTagging !== 'undefined' && designerTagging.customer_edit_mode === true) {
        return;
    }

    
    // Xóa tất cả overlay auto-detect
    jQuery('.auto-detect-tag-overlay').remove();
    
    // Xóa class auto-detect-tag
    jQuery('.auto-detect-tag').removeClass('auto-detect-tag');
    
    // Quét lại
    detectMediaElements();
    
    // Kiểm tra và sửa các overlay đã tagged
    jQuery('.tagged-media-overlay').each(function() {
        const $overlay = jQuery(this);
        const targetId = $overlay.attr('data-target-id');
        let $target;
        
        if (targetId) {
            $target = jQuery('#' + targetId);
        } else {
            $target = jQuery($overlay.data('target-element'));
        }
        
        if ($target.length) {
            // Đánh dấu phần tử đã được tag để tránh quét lại
            $target.attr('data-overlay-applied', 'true');
            $target.addClass('designer-highlight');
            
            // Cập nhật lại vị trí và kích thước
            const elOffset = $target.position();
            let width = $target.outerWidth();
            let height = $target.outerHeight();
            
            // Đảm bảo kích thước hợp lệ
            if (width <= 0) width = $target.width();
            if (height <= 0) height = $target.height();
            
            // Nếu vẫn không có kích thước hợp lệ, thử lấy từ style
            if (width <= 0) width = parseInt($target.css('width')) || 300;
            if (height <= 0) height = parseInt($target.css('height')) || 150;
            
            $overlay.css({
                top: elOffset.top,
                left: elOffset.left,
                width: width,
                height: height,
                zIndex: 99999 // Z-index cao nhất
            });
        } else {
            // Nếu không tìm thấy target, xóa overlay
            $overlay.remove();
        }
    });
}

jQuery(function($) {
    // Kiểm tra xem có đang ở trong Elementor Editor không
    if (typeof designerTagging !== 'undefined' && designerTagging.is_elementor_editor === true) {
        return; // Không khởi tạo chức năng tagging trong Elementor Editor
    }
    
    let currentElem = null;
    
    // Gọi hàm quét phần tử media khi trang đã tải xong
    detectMediaElements();
    
    // Thêm nút để quét lại nếu cần
    // $('#plugin-topbar .p-2:last').prepend(
    //     '<button type="button" id="detect-media-btn" class="btn btn-info text-white me-2">Scan Iframe/Video</button>'
    // );
    
    // Xử lý sự kiện click cho nút quét media
    $(document).on('click', '#detect-media-btn', function() {
        rescanMediaElements();
    });
    
    // Quét lại sau khi trang đã tải hoàn toàn
    $(window).on('load', function() {
        setTimeout(rescanMediaElements, 1000);
    });
    
    // Sử dụng event delegation để xử lý hover cho tất cả các overlay
    $(document).on('mouseenter', '.auto-detect-tag-overlay', function() {
        // Disable media overlay interactions in approval mode
        if (typeof window.isApprovalMode !== 'undefined' && window.isApprovalMode === true) {
            return;
        }

        const $overlay = $(this);
        // Lưu tham chiếu đến overlay hiện tại vào một biến toàn cục
        window.currentOverlay = $overlay;
        
        // Lấy target element từ data attribute
        let targetElement;
        const targetId = $overlay.attr('data-target-id');
        
        if (targetId) {
            // Nếu có target ID, lấy phần tử theo ID (cách đáng tin cậy hơn)
            targetElement = document.getElementById(targetId);
        } else {
            // Nếu không có ID, sử dụng cách cũ
            targetElement = $overlay.data('target-element');
        }
        
        if (!targetElement) {
            return;
        }
        
        // Lưu lại target element vào overlay để đảm bảo luôn có tham chiếu mới nhất
        $overlay.data('target-element', targetElement);
        
        const elementType = targetElement.tagName.toLowerCase();
        
        const offset = $overlay.offset();
        const width = $overlay.outerWidth();
        const height = $overlay.outerHeight();
        
        // Kiểm tra xem tag overlay đã tồn tại chưa
        let $tagOverlay = $('#media-tag-overlay');
        if ($tagOverlay.length === 0) {
            // Xác định text cho button dựa trên element type
            const buttonText = elementType === 'iframe' ? 'Add Iframe Tag' : 'Add Video Tag';

            $tagOverlay = $(
                '<div id="media-tag-overlay" class="media-tag-overlay">' +
                    '<div class="media-tag-content">' +
                        '<span class="media-tag-type">' + elementType.toUpperCase() + '</span>' +
                        '<button type="button" class="media-tag-btn btn btn-primary">' + buttonText + '</button>' +
                    '</div>' +
                '</div>'
            );
            $('body').append($tagOverlay);
            
            // Thêm sự kiện click cho nút Add Tag
            $tagOverlay.find('.media-tag-btn').on('click', function(e) {
                e.stopPropagation(); // Ngăn sự kiện click lan ra ngoài
                
                // Lấy phần tử hiện tại từ overlay hiện tại
                const currentOverlay = window.currentOverlay;
                if (!currentOverlay) {
                    return;
                }
                
                // Lấy target element từ overlay hiện tại
                let targetElement;
                const targetId = currentOverlay.attr('data-target-id');
                
                if (targetId) {
                    // Nếu có target ID, lấy phần tử theo ID
                    targetElement = document.getElementById(targetId);
                } else {
                    // Nếu không có ID, sử dụng cách cũ
                    targetElement = currentOverlay.data('target-element');
                }
                
                if (!targetElement) {
                    return;
                }
                
                // Lưu phần tử hiện tại vào biến toàn cục
                currentElem = targetElement;
                
                
                // Xác định loại tag dựa trên loại phần tử
                const elementType = currentElem.tagName.toLowerCase();
                const tagType = (elementType === 'iframe') ? 'iframe' : 'video';
                
                // Lấy vị trí của button để hiển thị context menu gần đó
                const buttonPos = $(this).offset();
                const buttonWidth = $(this).outerWidth();
                const buttonHeight = $(this).outerHeight();
                
                // Cập nhật master tags để chỉ hiển thị tags có type phù hợp
                updateMasterTagsForType(tagType);

                // Cập nhật select box designer-type để chỉ hiển thị option phù hợp
                updateDesignerTypeSelect(tagType);

                // Hiển thị context menu ngay dưới button
                $('#designer-context-menu')
                    .css({
                        top: buttonPos.top + buttonHeight + 5, // Hiển thị ngay dưới button
                        left: buttonPos.left, // Căn lề trái với button
                        display: 'block'
                    })
                    .data('current-element', currentElem) // Lưu phần tử hiện tại vào context menu
                    .data('element-type', tagType); // Lưu loại phần tử
                
                // Ẩn overlay
                $('#media-tag-overlay').hide();
            });
        }
        
        // Cập nhật button text dựa trên element type
        const buttonText = elementType === 'iframe' ? 'Add Iframe Tag' : 'Add Video Tag';
        $tagOverlay.find('.media-tag-btn').text(buttonText);

        // Cập nhật element type text
        $tagOverlay.find('.media-tag-type').text(elementType.toUpperCase());

        // Cập nhật vị trí và kích thước của tag overlay
        $tagOverlay.css({
            top: offset.top,
            left: offset.left,
            width: width,
            height: height,
            display: 'flex',
            zIndex: 100001 // Z-index cao hơn overlay
        });

        // Lưu tham chiếu đến phần tử target vào tag overlay
        $tagOverlay.data('target-element', targetElement);

        // Thêm class để đánh dấu overlay đang active
        $overlay.addClass('overlay-active');
    }).on('mouseleave', '.auto-detect-tag-overlay', function(e) {
        const $overlay = $(this);
        
        // Kiểm tra xem chuột có đang di chuyển vào media-tag-overlay không
        const $tagOverlay = $('#media-tag-overlay');
        if ($tagOverlay.length) {
            const tagOverlayPos = $tagOverlay.offset();
            const tagOverlayWidth = $tagOverlay.outerWidth();
            const tagOverlayHeight = $tagOverlay.outerHeight();
            
            // Nếu chuột đang di chuyển vào khu vực của media-tag-overlay, không ẩn
            if (e.pageX >= tagOverlayPos.left && 
                e.pageX <= tagOverlayPos.left + tagOverlayWidth && 
                e.pageY >= tagOverlayPos.top && 
                e.pageY <= tagOverlayPos.top + tagOverlayHeight) {
                return;
            }
        }
        
        // Xóa class active
        $overlay.removeClass('overlay-active');
        
        // Đặt timeout để tránh ẩn ngay lập tức
        setTimeout(function() {
            // Chỉ ẩn nếu không có overlay nào đang active và không có context menu đang hiển thị
            if ($('.overlay-active').length === 0 && !$('#designer-context-menu').is(':visible')) {
                $('#media-tag-overlay').hide();
            }
        }, 100);
    });

    // Thêm sự kiện hover cho media-tag-overlay để giữ nó hiển thị
    $(document).on('mouseenter', '#media-tag-overlay', function() {
        $(this).data('hover', true);
    }).on('mouseleave', '#media-tag-overlay', function(e) {
        const $tagOverlay = $(this);
        $tagOverlay.data('hover', false);
        
        // Kiểm tra xem chuột có đang di chuyển vào auto-detect-tag-overlay không
        const $overlays = $('.auto-detect-tag-overlay');
        let movingToOverlay = false;
        
        $overlays.each(function() {
            const $overlay = $(this);
            const overlayPos = $overlay.offset();
            const overlayWidth = $overlay.outerWidth();
            const overlayHeight = $overlay.outerHeight();
            
            if (e.pageX >= overlayPos.left && 
                e.pageX <= overlayPos.left + overlayWidth && 
                e.pageY >= overlayPos.top && 
                e.pageY <= overlayPos.top + overlayHeight) {
                movingToOverlay = true;
                return false; // break the loop
            }
        });
        
        if (!movingToOverlay) {
            // Đặt timeout để tránh ẩn ngay lập tức
            setTimeout(function() {
                // Chỉ ẩn nếu không có overlay nào đang active và không có context menu đang hiển thị
                if (!$tagOverlay.data('hover') && $('.overlay-active').length === 0 && !$('#designer-context-menu').is(':visible')) {
                    $tagOverlay.hide();
                }
            }, 100);
        }
    });
    
    // Cập nhật context menu để thêm các loại tag mới
    //  <div class="template-tag-item mb-2 cursor-pointer hover:bg-gray-100 p-2 rounded" data-tag-type="text" data-tag-name="Heading">
    //     Heading
    // </div>





    // Hàm cập nhật master tags dựa trên element hiện tại
    function updateMasterTagsForElement(element) {
        if (!element) {
            updateMasterTagsForType(null); // Hiển thị tất cả nếu không có element
            return;
        }

        // Lấy valid types cho element này
        const validation = validateElementType(element, 'text'); // dummy type để lấy valid types
        const validTypes = validation.validTypes;

        // Tạo HTML cho master tags, chỉ hiển thị những tag có type hợp lệ
        let tagsHtml = '';

        for (const tagName in masterTags) {
            const tagType = masterTags[tagName];

            // Chỉ hiển thị tag nếu type của nó hợp lệ cho element hiện tại
            if (!validTypes.includes(tagType)) {
                continue;
            }

            // Kiểm tra xem tag đã được sử dụng chưa
            if (isTagNameAlreadyUsed(tagName)) {
                // Nếu đã được sử dụng, hiển thị với style disabled
                tagsHtml += `<div class="master-tag-item mb-2 p-2 rounded master-tag-disabled"
                                 data-tag-type="${tagType}" data-tag-name="${tagName}" title="This tag is already used">
                                ${tagName} <span class="tag-used-indicator">(Used)</span>
                            </div>`;
            } else {
                // Nếu chưa được sử dụng, hiển thị bình thường
                tagsHtml += `<div class="master-tag-item mb-2 cursor-pointer hover:bg-gray-100 p-2 rounded"
                                 data-tag-type="${tagType}" data-tag-name="${tagName}">
                                ${tagName}
                            </div>`;
            }
        }

        // Nếu không có tag nào hợp lệ, hiển thị thông báo
        if (!tagsHtml) {
            const elementTag = element.tagName.toLowerCase();
            tagsHtml = `<div class="p-2 text-center text-gray-500">
                            No master tags available for &lt;${elementTag}&gt; element
                        </div>`;
        }

        $('#master-tagging-submenu').html(tagsHtml);
    }

    // Hàm cập nhật select box designer-type
    function updateDesignerTypeSelect(elementType) {
        const $select = $('#designer-type');
        $select.empty();

        // Thêm option duy nhất cho element type
        const typeLabels = {
            'iframe': 'Iframe',
            'video': 'Video',
            'text': 'Text',
            'image': 'Image',
            'button': 'Button',
            'link': 'Link',
            'progress': 'Progress Bar'
        };

        const label = typeLabels[elementType] || elementType.charAt(0).toUpperCase() + elementType.slice(1);
        $select.append(`<option value="${elementType}">${label}</option>`);
        $select.val(elementType);
    }

    // Hàm auto-suggest type dựa trên element
    function autoSuggestTypeForElement(element) {
        if (!element) return null;

        const tagName = element.tagName.toLowerCase();
        const elementType = element.type ? element.type.toLowerCase() : '';

        // Auto-suggest type dựa trên element
        const suggestions = {
            'img': 'image',
            'a': 'link',
            'button': 'button',
            'input': elementType === 'button' ? 'button' : 'text',
            'textarea': 'text',
            'video': 'video',
            'iframe': 'iframe',
            'p': 'text',
            'h1': 'text', 'h2': 'text', 'h3': 'text', 'h4': 'text', 'h5': 'text', 'h6': 'text',
            'span': 'text'
        };

        return suggestions[tagName] || 'text'; // fallback to text
    }

    // Hàm cập nhật select box với valid options cho element hiện tại
    function updateDesignerTypeSelectForElement(element) {
        const $select = $('#designer-type');
        $select.empty();

        if (!element) {
            resetDesignerTypeSelect();
            return;
        }

        const validation = validateElementType(element, 'text'); // dummy type để lấy valid types
        const validTypes = validation.validTypes;
        const suggestedType = autoSuggestTypeForElement(element);

        const typeLabels = {
            'text': 'Text',
            'image': 'Image',
            'button': 'Button',
            'link': 'Link',
            'iframe': 'Iframe',
            'video': 'Video',
            'progress': 'Progress Bar',
            'default': 'Default'
        };

        // Thêm chỉ các options hợp lệ
        validTypes.forEach(type => {
            const label = typeLabels[type] || type.charAt(0).toUpperCase() + type.slice(1);
            const isSelected = type === suggestedType ? 'selected' : '';
            $select.append(`<option value="${type}" ${isSelected}>${label}</option>`);
        });

        // Set suggested type
        $select.val(suggestedType);
    }

    // Hàm reset select box designer-type về tất cả options
    function resetDesignerTypeSelect() {
        const $select = $('#designer-type');
        $select.empty();

        // Thêm tất cả options
        $select.append(`
            <option value="text">Text</option>
            <option value="image">Image</option>
            <option value="button">Button</option>
            <option value="link">Link</option>
            <option value="iframe">Iframe</option>
            <option value="video">Video</option>
            <option value="progress">Progress Bar</option>
        `);
    }

    // Tạo HTML cho tất cả master tags (mặc định)
    let tagsHtml = createMasterTagsHtml();

    $('body').append(`
        <div id="designer-context-menu" style="display:none;position:absolute;z-index:999999;background:#fff;border:1px solid #ccc;padding:12px 16px;border-radius:4px;box-shadow:0 4px 8px rgba(0,0,0,0.2);">
            <div class="template-tags-list">
                <h5 class="mb-2 font-bold">Add Tag</h5>
                <div class="mb-2">
                    <label class="block text-xs mb-1">Section</label>
                    <div class="d-flex">
                        <select id="designer-section" class="border px-2 py-1 w-full">
                            <!-- Section options will be loaded dynamically -->
                        </select>
                        <button id="add-section-btn" class="btn btn-sm btn-outline-primary ms-2" title="Add New Section">
                            <i class="fa fa-plus"></i>
                        </button>
                    </div>
                </div>
               
                <hr class="my-3">
                <div id="master-tagging-item" class="mb-2 cursor-pointer p-2 rounded position-relative hover-item">
                    <span><i class="fa fa-star mr-2"></i> Master Tagging <i class="fa fa-chevron-right float-right mt-1"></i></span>
                </div>
                <div id="add-new-tag-btn" class="mb-2 cursor-pointer p-2 rounded hover-item">
                    <i class="fa fa-plus-circle mr-2"></i> Add Custom Tag
                </div>
            </div>
        </div>
        
        <div id="master-tagging-submenu" style="display:none;position:absolute;z-index:1000000;background:#fff;border:1px solid #ccc;padding:8px 12px;border-radius:4px;box-shadow:0 4px 6px rgba(0,0,0,0.1);max-height:250px;overflow-y:auto;">
           ${tagsHtml}
        </div>
    `);
    
    // Thêm popup cho form tạo tag mới
    $('body').append(`
        <div id="new-tag-popup" style="display:none;position:absolute;z-index:1000000;background:#fff;border:1px solid #ccc;padding:12px 16px;border-radius:4px;box-shadow:0 4px 6px rgba(0,0,0,0.1);">
            <div class="mb-2">
                <label class="block text-xs mb-1">Tag Name</label>
                <input type="text" id="designer-label" placeholder="Heading Title,..." class="border px-2 py-1 w-full" />
            </div>
            <div class="mb-2">
                <label class="block text-xs mb-1">Tag Type</label>
                <select id="designer-type" class="border px-2 py-1 w-full">
                    <option value="text">Text</option>
                    <option value="image">Image</option>
                    <option value="button">Button</option>
                    <option value="link">Link</option>
                    <option value="iframe">Iframe</option>
                    <option value="progress">Progress Bar</option>
                </select>
                <div class="validation-message" id="type-validation-message"></div>
            </div>
            <div class="flex justify-between">
                <button id="designer-cancel-btn" class="btn btn-secondary btn-sm">Cancel</button>
                <button id="designer-save-btn" class="btn btn-primary  btn-sm">Add Tag</button>
            </div>
        </div>
    `);



    // Real-time validation khi thay đổi type
    $(document).on('change', '#designer-type', function() {
        const selectedType = $(this).val();
        const $validationMsg = $('#type-validation-message');
        const $select = $(this);

        if (currentElem) {
            const validation = validateElementType(currentElem, selectedType);

            if (!validation.isValid) {
                const validTypesText = validation.validTypes.join(', ');
                $select.addClass('designer-type-error');
                $validationMsg.text(`Invalid for <${validation.elementTag}>. Valid: ${validTypesText}`).show();
            } else {
                $select.removeClass('designer-type-error');
                $validationMsg.hide();
            }
        }
    });

    // Thêm popup cho form tạo section mới
    $('body').append(`
        <div id="new-section-popup" style="display:none;position:absolute;z-index:1000000;background:#fff;border:1px solid #ccc;padding:12px 16px;border-radius:4px;box-shadow:0 4px 6px rgba(0,0,0,0.1);">
            <div class="mb-2">
                <label class="block text-xs mb-1">Section Name</label>
                <input type="text" id="section-name" placeholder="Enter section name" class="border px-2 py-1 w-full" />
            </div>
            <div class="flex justify-between mt-3">
                <button id="section-cancel-btn" class="btn btn-sm btn-secondary">Cancel</button>
                <button id="section-save-btn" class="btn btn-sm btn-primary">Add Section</button>
            </div>
        </div>
    `);



    $(document).on('contextmenu', function(e) {
        return true;

        // Always disable custom context menu in customer edit mode - show default browser menu
        if (typeof designerTagging !== 'undefined' &&
            designerTagging.customer_edit_mode === true) {
            return true; // Always allow default browser context menu
        }

        // Check if we're in approval mode - disable tagging
        if (typeof window.isApprovalMode !== 'undefined' && window.isApprovalMode === true) {
            return true; // Allow default context menu
        }


        // Nếu click phải vào sidebar hoặc context menu thì bỏ qua
        if (
            $(e.target).closest('#plugin-topbar').length ||
            $(e.target).closest('#main-sidebar').length ||
            $(e.target).closest('.designer-highlight').length
        ) {
            return; // Không hiện context menu custom
        }
        e.preventDefault();
        currentElem = e.target;

        // Cập nhật master tags và select box dựa trên element hiện tại
        updateMasterTagsForElement(currentElem); // Filter master tags cho element
        updateDesignerTypeSelectForElement(currentElem); // Auto-suggest type cho element

        // Ẩn popup nếu đang hiển thị
        $('#new-tag-popup').hide();

        // Hiển thị context menu
        $('#designer-context-menu')
            .css({top: e.pageY, left: e.pageX})
            .show()
            .removeData('element-type'); // Xóa element-type data
    });

    // Nút save tổng

    
    $(document).on('click', '#tagging_save', function(e) {
        e.stopPropagation();
        showToast("Template saved successfully!", 'success');

    });

    // Function to send data to 3rd party API
    function sendToThirdPartyAPI(templateData, metaKey) {
        // Get API configuration from WordPress settings
        const API_ENDPOINT = designerTagging.api_endpoint;
        const API_KEY = designerTagging.api_key;

        // Skip if API not configured
        if (!API_ENDPOINT || !API_KEY) {
            return;
        }

        // Get the serialized meta_value from WordPress (we need to get it via AJAX)
        getSerializedMetaValue(metaKey, function(serializedValue) {
            if (!serializedValue) {
                return;
            }

            // Prepare data with serialized meta_value (same as in database)
            const apiData = {
                meta_key: metaKey,
                meta_value: serializedValue, // Serialized string like in database
                site_url: window.location.origin,
                timestamp: new Date().toISOString()
            };

            sendApiRequest(apiData);
        });
    }

    // Function to get serialized meta value from WordPress
    function getSerializedMetaValue(metaKey, callback) {
        $.post(designerTagging.ajax_url, {
            action: 'get_serialized_meta_value',
            nonce: designerTagging.nonce,
            post_id: designerTagging.post_id,
            meta_key: metaKey
        }, function(response) {
            if (response.success) {
                callback(response.data.serialized_value);
            } else {
                callback(null);
            }
        }).fail(function() {
            callback(null);
        });
    }

    // Function to send API request to 3rd party
    function sendApiRequest(apiData) {
        const API_ENDPOINT = designerTagging.api_endpoint;
        const API_KEY = designerTagging.api_key;

        $.ajax({
            url: API_ENDPOINT,
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + API_KEY,
                'X-Site-Origin': window.location.origin
            },
            data: JSON.stringify(apiData),
            timeout: 10000, // 10 seconds timeout
            success: function(response) {
                showToast('Template synced to external system', 'success');
            },
            error: function(xhr, status, error) {
            }
        });
    }

    // Cache Clear Toggle functionality
    function updateCacheStatus() {
        $.post(designerTagging.ajax_url, {
            action: 'designer_get_cache_status',
            nonce: designerTagging.nonce
        }, function(response) {
            if (response.success) {
                const enabled = response.data.enabled;
                const $button = $('#toggle_cache_clear');
                const $text = $('#cache_status_text');

                if (enabled) {
                    $text.text('Cache: ON');
                    $button.removeClass('btn-secondary').addClass('btn-success');
                } else {
                    $text.text('Cache: OFF');
                    $button.removeClass('btn-success').addClass('btn-secondary');
                }
            }
        });
    }

    // Toggle cache clear button handler
    $(document).on('click', '#toggle_cache_clear', function(e) {
        e.stopPropagation();

        const $button = $(this);
        const $text = $('#cache_status_text');
        const originalText = $text.text();

        $text.text('Toggling...');
        $button.prop('disabled', true);

        $.post(designerTagging.ajax_url, {
            action: 'designer_toggle_cache_clear',
            nonce: designerTagging.nonce
        }, function(response) {
            if (response.success) {
                const enabled = response.data.enabled;

                if (enabled) {
                    $text.text('Cache: ON');
                    $button.removeClass('btn-secondary').addClass('btn-success');
                    showToast('Cache clearing enabled - Assets will always reload', 'success');
                } else {
                    $text.text('Cache: OFF');
                    $button.removeClass('btn-success').addClass('btn-secondary');
                    showToast('Cache clearing disabled - Normal caching resumed', 'info');
                }
            } else {
                $text.text(originalText);
                showToast('Failed to toggle cache setting', 'error');
            }
        }).fail(function() {
            $text.text(originalText);
            showToast('Error toggling cache setting', 'error');
        }).always(function() {
            $button.prop('disabled', false);
        });
    });

    // Load cache status on page load
    $(document).ready(function() {
        setTimeout(updateCacheStatus, 500);
    });

    // Xử lý click vào nút "Add New Tag"
    $(document).on('click', '#add-new-tag-btn', function(e) {
        e.stopPropagation();

        // Kiểm tra xem có element type được lưu trong context menu không
        const elementType = $('#designer-context-menu').data('element-type');

        // Cập nhật select box designer-type
        if (elementType) {
            // Nếu có element type cụ thể, chỉ hiển thị option đó
            updateDesignerTypeSelect(elementType);
        } else {
            // Nếu không có element type cụ thể, hiển thị options phù hợp với element hiện tại
            updateDesignerTypeSelectForElement(currentElem);
        }

        // Lấy vị trí của context menu
        const contextMenuPos = $('#designer-context-menu').position();
        const contextMenuWidth = $('#designer-context-menu').outerWidth();

        // Hiển thị popup bên phải context menu
        $('#new-tag-popup')
            .css({
                top: contextMenuPos.top,
                left: contextMenuPos.left + contextMenuWidth + 5
            })
            .show();

        // Focus vào input
        $('#designer-label').focus();
    });

    // Ẩn menu khi click ra ngoài
    $(document).on('click', function(e) {
        // Kiểm tra xem click có phải là bên trong context menu hoặc các popup liên quan không
        const isContextMenuClick = $(e.target).closest('#designer-context-menu').length > 0;
        const isNewTagPopupClick = $(e.target).closest('#new-tag-popup').length > 0;
        const isMasterTaggingSubmenuClick = $(e.target).closest('#master-tagging-submenu').length > 0;
        const isNewSectionPopupClick = $(e.target).closest('#new-section-popup').length > 0;
        
        // Nếu click bên ngoài tất cả các menu và popup
        if (!isContextMenuClick && !isNewTagPopupClick && !isMasterTaggingSubmenuClick && !isNewSectionPopupClick) {
            $('#designer-context-menu').hide();
            $('#new-tag-popup').hide();
            $('#master-tagging-submenu').hide();
            $('#new-section-popup').hide();
        } else {
            // Nếu click vào một trong các popup, chỉ ẩn các popup khác nếu cần
            if (!isNewTagPopupClick) $('#new-tag-popup').hide();
            if (!isMasterTaggingSubmenuClick) $('#master-tagging-submenu').hide();
            if (!isNewSectionPopupClick) $('#new-section-popup').hide();
            
            // Luôn giữ context menu mở
            $('#designer-context-menu').show();
        }
    });

    // Xử lý click vào nút Cancel
    $(document).on('click', '#designer-cancel-btn', function(e) {
        e.preventDefault();
        $('#new-tag-popup').hide();
    });

    // Xử lý click vào template tag
    $(document).on('click', '.template-tag-item', function() {
        const tagName = $(this).data('tag-name');
        const tagType = $(this).data('tag-type');
        const section = $('#designer-section').val();

        // Lưu trực tiếp
        saveDesignerTag(tagName, tagType, section);
    });

    // Xử lý click vào master tag item
    $(document).on('click', '.master-tag-item', function() {
        // Kiểm tra xem tag đã được sử dụng chưa
        if ($(this).hasClass('master-tag-disabled')) {
            showToast('This tag is already used. Please choose a different tag.', 'warning');
            return;
        }

        const tagName = $(this).data('tag-name');
        const tagType = $(this).data('tag-type');
        const section = $('#designer-section').val();

        // Lưu trực tiếp
        saveDesignerTag(tagName, tagType, section);
    });

    // Lưu vùng chỉnh sửa
    $('#designer-save-btn').on('click', function(e) {
        e.preventDefault();
        const section = $('#designer-section').val();
        saveDesignerTag(null, null, section);
        $('#new-tag-popup').hide();
    });
    
    // Hàm kiểm tra xem tag name đã được sử dụng chưa
    function isTagNameAlreadyUsed(tagName) {
        // Lấy danh sách tất cả tag đã lưu từ designerTagging.tagged_fields
        if (typeof designerTagging !== 'undefined' && designerTagging.tagged_fields) {
            try {
                const taggedFields = JSON.parse(designerTagging.tagged_fields);
                return taggedFields.some(field => field.label === tagName);
            } catch (e) {
                return false;
            }
        }
        return false;
    }

    // Hàm cập nhật designerTagging.tagged_fields với tag mới
    function updateTaggedFieldsData(label, type, section, selector, value, additionalData) {
        try {
            let taggedFields = [];

            // Lấy dữ liệu hiện tại
            if (typeof designerTagging !== 'undefined' && designerTagging.tagged_fields) {
                taggedFields = JSON.parse(designerTagging.tagged_fields);
            }

            // Thêm tag mới
            const newTag = {
                label: label,
                type: type,
                section: section,
                selector: selector,
                value: value,
                additional_data: JSON.stringify(additionalData)
            };

            taggedFields.push(newTag);

            // Cập nhật lại designerTagging.tagged_fields
            designerTagging.tagged_fields = JSON.stringify(taggedFields);

        } catch (e) {
        }
    }

    // Hàm xóa tag khỏi designerTagging.tagged_fields
    function removeTagFromData(tagLabel) {
        try {
            let taggedFields = [];

            // Lấy dữ liệu hiện tại
            if (typeof designerTagging !== 'undefined' && designerTagging.tagged_fields) {
                taggedFields = JSON.parse(designerTagging.tagged_fields);
            }

            // Lọc bỏ tag có label trùng
            taggedFields = taggedFields.filter(field => field.label !== tagLabel);

            // Cập nhật lại designerTagging.tagged_fields
            designerTagging.tagged_fields = JSON.stringify(taggedFields);

        } catch (e) {
        }
    }

    // Hàm kiểm tra type có phù hợp với element không
    function validateElementType(element, selectedType) {
        const tagName = element.tagName.toLowerCase();
        const elementType = element.type ? element.type.toLowerCase() : '';

        // Check for special image cases
        const $elem = $(element);
        const hasElementorIcon = $elem.hasClass('elementor-icon') || $elem.hasClass('elementor-social-icon');
        const hasSvgChild = $elem.find('svg').length > 0;
        const hasImgChild = $elem.find('img').length > 0;
        const isFigureWithImg = tagName === 'figure' && hasImgChild;

        // Định nghĩa mapping giữa element và type phù hợp
        const validMappings = {
            'img': ['image'],
            'svg': ['image'], // SVG elements can be tagged as image
            'a': ['link', 'button', 'image'], // Links can contain images/icons
            'button': ['button'],
            'input': elementType === 'button' ? ['button'] : ['text'],
            'textarea': ['text'],
            'p': ['text'],
            'h1': ['text'], 'h2': ['text'], 'h3': ['text'], 'h4': ['text'], 'h5': ['text'], 'h6': ['text'],
            'span': ['text', 'image'], // Spans can contain icons
            'div': ['text', 'button', 'progress', 'image'], // div có thể là nhiều loại including images
            'video': ['video'],
            'iframe': ['iframe'],
            'section': ['text', 'button', 'progress'],
            'article': ['text'],
            'aside': ['text'],
            'nav': ['text', 'button'],
            'figure': ['image'], // Figure elements typically contain images
            'i': ['image'] // Icon elements (font icons)
        };

        // Get base valid types for this element
        let validTypes = validMappings[tagName] || ['text', 'default'];

        // Add image type for special cases
        if (hasElementorIcon || hasSvgChild || hasImgChild || isFigureWithImg) {
            if (!validTypes.includes('image')) {
                validTypes.push('image');
            }
        }

        return {
            isValid: validTypes.includes(selectedType),
            validTypes: validTypes,
            elementTag: tagName
        };
    }

    // Function to detect image type and extract relevant data
    function detectImageType(element) {
        const $elem = $(element);
        const tagName = element.tagName.toLowerCase();


        let imageData = {
            type: 'unknown',
            src: '',
            elementTag: tagName,
            classes: $elem.attr('class') || '',
            altText: '',
            svgContent: ''
        };

        // Case 1: SVG element directly
        if (tagName === 'svg') {
            imageData.type = 'svg';
            imageData.svgContent = element.outerHTML;
            imageData.src = 'data:svg'; // Placeholder since SVG doesn't have src
            imageData.altText = $elem.find('title').text() || '';
        }

        // Case 2: IMG element
        else if (tagName === 'img') {
            imageData.type = 'img';
            imageData.src = $elem.attr('src') || '';
            imageData.altText = $elem.attr('alt') || '';
        }

        // Case 3: Element containing SVG (like elementor-icon, elementor-social-icon)
        else if ($elem.hasClass('elementor-icon') || $elem.hasClass('elementor-social-icon') || $elem.find('svg').length > 0) {
            const $svg = $elem.find('svg').first();
            if ($svg.length) {
                imageData.type = 'svg_container';
                imageData.svgContent = $svg[0].outerHTML;
                imageData.src = 'data:svg'; // Placeholder
                imageData.altText = $elem.find('.elementor-screen-only').text() || $svg.find('title').text() || '';
            }
        }

        // Case 4: Figure element containing image
        else if (tagName === 'figure' || $elem.hasClass('elementor-image-box-img')) {
            const $img = $elem.find('img').first();
            if ($img.length) {
                imageData.type = 'figure_img';
                imageData.src = $img.attr('src') || '';
                imageData.altText = $img.attr('alt') || '';
                imageData.elementTag = 'figure > img';
            }
        }

        // Case 5: Parent element that might contain image/svg
        else {
            // Check for child IMG
            const $childImg = $elem.find('img').first();
            if ($childImg.length) {
                imageData.type = 'container_img';
                imageData.src = $childImg.attr('src') || '';
                imageData.altText = $childImg.attr('alt') || '';
                imageData.elementTag = tagName + ' > img';
            }
            // Check for child SVG
            else {
                const $childSvg = $elem.find('svg').first();
                if ($childSvg.length) {
                    imageData.type = 'container_svg';
                    imageData.svgContent = $childSvg[0].outerHTML;
                    imageData.src = 'data:svg';
                    imageData.altText = $elem.find('.elementor-screen-only').text() || $childSvg.find('title').text() || '';
                    imageData.elementTag = tagName + ' > svg';
                }
            }
        }

        return imageData;
    }

    // Hàm lưu tag riêng biệt để có thể gọi từ nhiều nơi
    function saveDesignerTag(customLabel, customType, customSection) {
        let label = customLabel || $('#designer-label').val();
        let type = customType || $('#designer-type').val();
        let section = customSection || $('#designer-section').val();

        if (!label) {
            showToast('Please enter tag name!', 'error');
            return;
        }

        // Kiểm tra xem tag name đã được sử dụng chưa
        if (isTagNameAlreadyUsed(label)) {
            showToast('Tag name "' + label + '" is already used. Please choose a different name.', 'error');
            return;
        }

        // Validate element type
        if (currentElem) {
            const validation = validateElementType(currentElem, type);
            if (!validation.isValid) {
                const validTypesText = validation.validTypes.join(', ');
                showToast(`Invalid type "${type}" for <${validation.elementTag}> element. Valid types: ${validTypesText}`, 'error');
                return;
            }
        }
        
        // Lấy phần tử hiện tại từ context menu nếu có
        const contextMenuElement = $('#designer-context-menu').data('current-element');
        if (contextMenuElement) {
            currentElem = contextMenuElement;
        }
        
        if (!currentElem) {
            showToast('No element selected!', 'error');
            return;
        }
        
        // Lấy ID của phần tử nếu có
        const elementId = currentElem.id || '';
        
        // Xác định loại phần tử
        const elementType = currentElem.tagName.toLowerCase();
        
        // Nếu là iframe hoặc video, tự động đặt type phù hợp
        if (elementType === 'iframe') {
            type = 'iframe';
        } else if (elementType === 'video') {
            type = 'video';
        }
        
        // Sử dụng ID trong selector nếu có
        let selector;
        if (elementId) {
            selector = elementType + '#' + elementId;
        } else {
            selector = getUniqueSelector(currentElem);
        }
        
        
        // Lấy giá trị (value) dựa trên loại phần tử
        let value = '';
        let additionalData = {};
        
        switch (type) {
            case 'text':
                value = $(currentElem).text().trim();
                break;
            case 'link':
                value = $(currentElem).attr('href') || '';
                break;
            case 'iframe':
                value = $(currentElem).attr('src') || '';
                break;
            case 'video':
                value = $(currentElem).attr('src') || '';
                break;
            case 'image':
                // Handle different image types: SVG, IMG, Figure
                const imageData = detectImageType(currentElem);
                value = imageData.src;
                additionalData = {
                    ...additionalData,
                    image_type: imageData.type,
                    element_tag: imageData.elementTag,
                    classes: imageData.classes,
                    alt_text: imageData.altText,
                    svg_content: imageData.svgContent
                };
                break;
            case 'button':
                value = $(currentElem).text().trim();
                additionalData.url = $(currentElem).attr('href') || '';
                break;
            case 'progress':
                // Xử lý đặc biệt cho progress bar
                // Kiểm tra xem phần tử hiện tại có phải là progress wrapper không
                if ($(currentElem).hasClass('elementor-progress-wrapper')) {
                    // Lấy progress bar bên trong
                    const $progressBar = $(currentElem).find('.elementor-progress-bar');
                    
                    // Lấy label từ elementor-progress-text
                    const progressText = $progressBar.find('.elementor-progress-text').text().trim();
                    
                    // Lấy phần trăm từ data-max hoặc từ elementor-progress-percentage
                    let percent = $progressBar.data('max');
                    if (!percent) {
                        const percentText = $progressBar.find('.elementor-progress-percentage').text().trim();
                        percent = parseInt(percentText) || 0;
                    }
                    
                    // Cập nhật label và value
                    label = progressText || label;
                    value = percent.toString();
                    
                    // Lưu thêm thông tin vào additionalData
                    additionalData.percent = percent;
                    additionalData.label = progressText;
                } else if ($(currentElem).hasClass('elementor-progress-bar')) {
                    // Nếu click trực tiếp vào progress bar
                    const $progressBar = $(currentElem);
                    
                    // Lấy label từ elementor-progress-text
                    const progressText = $progressBar.find('.elementor-progress-text').text().trim();
                    
                    // Lấy phần trăm từ data-max hoặc từ elementor-progress-percentage
                    let percent = $progressBar.data('max');
                    if (!percent) {
                        const percentText = $progressBar.find('.elementor-progress-percentage').text().trim();
                        percent = parseInt(percentText) || 0;
                    }
                    
                    // Cập nhật label và value
                    label = progressText || label;
                    value = percent.toString();
                    
                    // Lưu thêm thông tin vào additionalData
                    additionalData.percent = percent;
                    additionalData.label = progressText;
                } else {
                    // Tìm progress wrapper gần nhất
                    const $wrapper = $(currentElem).closest('.elementor-progress-wrapper');
                    if ($wrapper.length) {
                        // Lấy progress bar bên trong
                        const $progressBar = $wrapper.find('.elementor-progress-bar');
                        
                        // Lấy label từ elementor-progress-text
                        const progressText = $progressBar.find('.elementor-progress-text').text().trim();
                        
                        // Lấy phần trăm từ data-max hoặc từ elementor-progress-percentage
                        let percent = $progressBar.data('max');
                        if (!percent) {
                            const percentText = $progressBar.find('.elementor-progress-percentage').text().trim();
                            percent = parseInt(percentText) || 0;
                        }
                        
                        // Cập nhật label và value
                        label = progressText || label;
                        value = percent.toString();
                        
                        // Lưu thêm thông tin vào additionalData
                        additionalData.percent = percent;
                        additionalData.label = progressText;
                    }
                }
                break;
            default:
                value = $(currentElem).text().trim();
        }
        
        
        // Gửi AJAX
        $.post(designerTagging.ajax_url, {
            action: 'save_designer_tag',
            nonce: designerTagging.nonce,
            post_id: designerTagging.post_id,
            selector: selector,
            label: label,
            type: type,
            section: section,
            value: value,
            additional_data: JSON.stringify(additionalData)
        }, function(res) {
            if (res.success) {
                showToast('Tag saved successfully!', 'success');

                // Cập nhật designerTagging.tagged_fields với tag mới
                updateTaggedFieldsData(label, type, section, selector, value, additionalData);

                // Đánh dấu phần tử đã được tag
                $(currentElem).attr('data-designer-editable', label);
                $(currentElem).attr('data-overlay-applied', 'true');
                $(currentElem).addClass('designer-highlight');
                
                // Kiểm tra nếu là video hoặc iframe thì mới thêm overlay
                if (elementType === 'iframe' || elementType === 'video') {
                    // Xóa overlay auto-detect nếu có
                    const $parent = $(currentElem).parent();
                    $parent.find('.iframe-overlay, .video-overlay').remove();
                    
                    // Thêm overlay tagged mới
                    const $taggedOverlay = $('<div class="tagged-media-overlay"></div>');
                    
                    // Tính toán vị trí và kích thước
                    const elOffset = $(currentElem).position();
                    let width = $(currentElem).outerWidth();
                    let height = $(currentElem).outerHeight();
                    
                    // Đảm bảo kích thước hợp lệ
                    if (width <= 0) width = $(currentElem).width();
                    if (height <= 0) height = $(currentElem).height();
                    
                    $taggedOverlay.css({
                        position: 'absolute',
                        top: elOffset.top,
                        left: elOffset.left,
                        width: width,
                        height: height,
                        background: 'rgba(37, 99, 235, 0.2)',
                        border: '2px solid #2563eb',
                        zIndex: 9990,
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        pointerEvents: 'all' // Chặn tương tác với phần tử bên dưới
                    });
                    
                    // Thêm label "Already Tagged"
                    const $tagLabel = $('<div class="tagged-label"></div>').text('Already Tagged');
                    $tagLabel.css({
                        background: 'rgba(0, 0, 0, 0.7)',
                        color: 'white',
                        padding: '5px 10px',
                        borderRadius: '4px',
                        fontWeight: 'bold'
                    });
                    
                    $taggedOverlay.append($tagLabel);
                    $parent.append($taggedOverlay);
                    
                    // Lưu tham chiếu đến phần tử gốc và ID
                    $taggedOverlay.data('target-element', currentElem);
                    if (elementId) {
                        $taggedOverlay.attr('data-target-id', elementId);
                    }
                }
                
                // Cập nhật nội dung của #tagged_fields
                if (res.data && res.data.tagged_fields_html) {
                    $('.content-form').html(res.data.tagged_fields_html);
                    
                    // Khởi tạo lại accordion events sau khi cập nhật nội dung
                    setTimeout(function() {
                        initAccordionEvents();
                    }, 100);
                }
                
                // Xóa class auto-detect-tag
                $(currentElem).removeClass('auto-detect-tag');
                
                // Ẩn tất cả các menu và popup sau khi lưu thành công
                $('#designer-context-menu').hide();
                $('#new-tag-popup').hide();
                $('#master-tagging-submenu').hide();
                $('#new-section-popup').hide();
                
                // Quét lại các phần tử media để cập nhật trạng thái
                setTimeout(function() {
                    rescanMediaElements();
                }, 500);
            } else {
                showToast('Error: ' + (res.data || 'Unknown error'), 'error');
            }
        });
    }

    // Hàm lấy selector duy nhất cho phần tử
    function getUniqueSelector(el) {
        if (!el) return '';
        let path = [];
        while (el.nodeType === 1 && el !== document.body) {
            let selector = el.nodeName.toLowerCase();
            if (el.id) {
                selector += '#' + el.id;
                path.unshift(selector);
                break;
            } else {
                let sib = el, nth = 1;
                while (sib = sib.previousElementSibling) {
                    if (sib.nodeName.toLowerCase() == selector)
                        nth++;
                }
                selector += ":nth-of-type(" + nth + ")";
            }
            path.unshift(selector);
            el = el.parentNode;
        }
        return path.join(" > ");
    }

    // Sử dụng event delegation cho accordion events

    // Hàm reset tất cả panel về trạng thái đóng
    function resetAllPanels() {
        $('.panel').removeClass('active').data('panel-state', 'closed');
        $('.accordion').removeClass('active');
        $('.panel').css({
            'max-height': '',
            'opacity': ''
        });
    }

    // Enable auto-reset để đảm bảo trạng thái ban đầu
    $(document).ready(function() {
        setTimeout(resetAllPanels, 100);
    });

    $(document).ajaxComplete(function(event, xhr, settings) {
        if (settings && settings.url && settings.url.indexOf('designer') !== -1) {
            setTimeout(resetAllPanels, 100);
        }
    });

    // Xử lý click vào accordion title
    $(document).on('click', '.accordion-title', function(e) {
        e.stopPropagation();

        // Lấy phần tử accordion cha
        var accordion = this.parentElement;

        // Scroll đến phần tử tương ứng
        var selector = accordion.getAttribute('data-selector');
        if (selector) {
            scrollToElement(selector);
        }
    });

    // Xử lý click vào nút mũi tên
    $(document).on('click', '.accordion-arrow', function(e) {
        e.preventDefault();
        e.stopPropagation();


        // Sử dụng jQuery để đảm bảo tính nhất quán
        var $accordion = $(this).closest('.accordion');
        var $panel = $accordion.next('.panel');


        if ($panel.length) {
            // Clear inline styles trước
            $panel.css({
                'max-height': '',
                'opacity': ''
            });

            // Lưu trạng thái hiện tại vào data attribute để track
            var currentState = $panel.data('panel-state') || 'closed';

            if (currentState === 'open') {
                // Đóng panel
                $accordion.removeClass('active');
                $panel.removeClass('active');
                $panel.data('panel-state', 'closed');
            } else {
                // Mở panel
                $accordion.addClass('active');
                $panel.addClass('active');
                $panel.data('panel-state', 'open');

                // Kiểm tra ngay sau khi thêm class
                setTimeout(function() {
                }, 50);
            }

        } else {
        }
    });

    // Xử lý click vào nút xóa
    $(document).on('click', '.accordion-delete', function(e) {
        e.preventDefault();
        e.stopPropagation();

        // Lấy phần tử accordion cha
        var accordion = this.closest('.accordion');
        var index = accordion.getAttribute('data-index');
        var selector = accordion.getAttribute('data-selector');

        if (confirm('Are you sure you want to delete this tag?')) {
            // Gửi AJAX để xóa tag
            jQuery.post(designerTagging.ajax_url, {
                action: 'designer_remove_editable',
                nonce: designerTagging.nonce,
                post_id: designerTagging.post_id,
                index: index
            }, function(response) {
                if (response.success) {
                    // Xóa accordion và panel khỏi DOM
                    var panel = accordion.nextElementSibling;
                    accordion.remove();
                    panel.remove();

                    // Xóa highlight và overlay trên phần tử
                    try {
                        var $el = jQuery(selector);
                        if ($el.length) {
                            $el.removeClass('designer-highlight');
                            $el.removeAttr('data-designer-editable');

                            // Chỉ xóa overlay nếu là iframe hoặc video
                            if ($el.is('iframe') || $el.is('video')) {
                                $el.removeAttr('data-overlay-applied');
                                // Xóa overlay nếu có
                                var $parent = $el.parent();
                                $parent.find('.tagged-media-overlay').remove();
                            }
                        }
                    } catch(e) {
                    }

                    // Lấy label của tag đã xóa để cập nhật data
                    var tagLabel = accordion.querySelector('.accordion-title').textContent.split(' (')[0];
                    removeTagFromData(tagLabel);

                    showToast('Tag deleted successfully!', 'success');
                } else {
                    showToast('Error: ' + (response.data || 'Unable to delete tag'), 'error');
                }
            });
        }
    });

    // Xử lý hiển thị submenu Master Tagging
    $(document).on('mouseenter', '#master-tagging-item', function() {
        const $item = $(this);
        const $menu = $('#master-tagging-submenu');

        // Kiểm tra xem có element type được lưu trong context menu không
        const elementType = $('#designer-context-menu').data('element-type');

        // Cập nhật master tags dựa trên element type hoặc element hiện tại
        if (elementType) {
            // Nếu có element type cụ thể (từ media button), chỉ hiển thị tags có type đó
            updateMasterTagsForType(elementType);
        } else {
            // Nếu không có element type cụ thể, filter dựa trên element hiện tại
            updateMasterTagsForElement(currentElem);
        }

        // Tính toán vị trí để hiển thị bên phải
        const itemPos = $item.offset();
        const itemWidth = $item.outerWidth();

        $menu.css({
            top: itemPos.top,
            left: itemPos.left + itemWidth,
            display: 'block'
        });
    });
    
    // Ẩn submenu khi rời khỏi cả item và submenu
    $(document).on('mouseleave', '#master-tagging-item', function(e) {
        // Kiểm tra xem chuột có đang di chuyển vào submenu không
        const $submenu = $('#master-tagging-submenu');
        const submenuPos = $submenu.offset();
        const submenuWidth = $submenu.outerWidth();
        const submenuHeight = $submenu.outerHeight();
        
        if (
            e.pageX < submenuPos.left || 
            e.pageX > submenuPos.left + submenuWidth || 
            e.pageY < submenuPos.top || 
            e.pageY > submenuPos.top + submenuHeight
        ) {
            // Nếu chuột không di chuyển vào submenu, ẩn submenu
            setTimeout(function() {
                if (!$submenu.is(':hover')) {
                    $submenu.hide();
                }
            }, 100);
        }
    });
    
    $(document).on('mouseleave', '#master-tagging-submenu', function() {
        $(this).hide();
    });
    
    // Xử lý click vào master tag item
    $(document).on('click', '.master-tag-item', function() {
        const tagName = $(this).data('tag-name');
        const tagType = $(this).data('tag-type');
        const section = $('#designer-section').val();
        
        // Lưu trực tiếp với loại master
        saveDesignerTag(tagName, tagType, section);
        
        // Ẩn các menu
        $('#designer-context-menu').hide();
        $('#master-tagging-submenu').hide();
    });

    // Hàm tải danh sách section
    function loadSections() {
        $.post(designerTagging.ajax_url, {
            action: 'get_designer_sections',
            nonce: designerTagging.nonce,
            post_id: designerTagging.post_id
        }, function(res) {
            if (res.success && res.data.sections_html) {
                $('#designer-section').html(res.data.sections_html);
            }
        });
    }
    
    // Tải danh sách section khi trang tải xong
    loadSections();

    // Initialize customer edit functionality if in customer edit mode
    if (typeof designerTagging !== 'undefined' &&
        designerTagging.customer_edit_mode === true) {
        initCustomerEditMode();
    }
    
    // Xử lý click vào nút thêm section
    $(document).on('click', '#add-section-btn', function(e) {
        e.stopPropagation();
        
        // Lấy vị trí của context menu
        const contextMenuPos = $('#designer-context-menu').position();
        
        // Hiển thị popup bên cạnh nút
        $('#new-section-popup')
            .css({
                top: contextMenuPos.top,
                left: contextMenuPos.left + $('#designer-context-menu').outerWidth() + 5
            })
            .show();
        
        // Focus vào input
        $('#section-name').focus();
    });
    
    // Xử lý click vào nút hủy thêm section
    $('#section-cancel-btn').on('click', function() {
        $('#new-section-popup').hide();
    });
    
    // Xử lý click vào nút lưu section
    $('#section-save-btn').on('click', function() {
        const sectionName = $('#section-name').val();
        
        if (!sectionName) {
            showToast('Please enter section name!', 'error');
            return;
        }
        
        // Gửi AJAX để thêm section
        $.post(designerTagging.ajax_url, {
            action: 'add_designer_section',
            nonce: designerTagging.nonce,
            post_id: designerTagging.post_id,
            section_name: sectionName
        }, function(res) {
            if (res.success) {
                showToast('Section added successfully!', 'success');
                
                // Cập nhật dropdown section
                if (res.data && res.data.sections_html) {
                    $('#designer-section').html(res.data.sections_html);
                }
                
                // Chỉ ẩn popup section, giữ context menu mở
                $('#new-section-popup').hide();
                $('#section-name').val('');
                
                // Đảm bảo context menu vẫn hiển thị
                $('#designer-context-menu').show();
                
                // Cập nhật vị trí context menu nếu cần
                // const contextMenuPos = $('#designer-context-menu').position();
                // $('#designer-context-menu').css({
                //     top: contextMenuPos.top,
                //     left: contextMenuPos.left
                // });
            } else {
                showToast('Error: ' + (res.data || 'Unknown error'), 'error');
            }
        });
    });
});

// Hàm hiển thị toast message
function showToast(message, type = 'success') {
    // Xóa toast cũ nếu có
    jQuery('.designer-toast').remove();
    
    // Tạo toast mới
    const toast = jQuery('<div class="designer-toast designer-toast-' + type + '"></div>');
    toast.text(message);
    
    // Thêm vào body
    jQuery('body').append(toast);
    
    // Hiển thị toast
    setTimeout(() => {
        toast.addClass('show');
        
        // Tự động ẩn sau 3 giây
        setTimeout(() => {
            toast.removeClass('show');
            setTimeout(() => {
                toast.remove();
            }, 300); // Đợi animation kết thúc
        }, 3000);
    }, 10);
}

// Khởi tạo khi trang đã tải xong - với jQuery safety check
function initDesignerTagging() {
    // Đảm bảo rằng designerTagging đã được định nghĩa
    if (typeof designerTagging !== 'undefined') {

        // Initialize master tags from GraphQL API
        initializeMasterTags();

        // Quét các phần tử media
        setTimeout(function() {
            detectMediaElements();
        }, 500); // Đợi 500ms để đảm bảo trang đã tải xong

        // Thiết lập interval để quét lại các phần tử media định kỳ
        // Hữu ích khi có nội dung động được thêm vào trang
        setInterval(function() {
            rescanMediaElements();
        }, 5000); // Quét lại mỗi 5 giây

        // Initialize window resize handler
        initWindowResizeHandler();
    }
}

// Thêm sự kiện để quét lại khi cửa sổ thay đổi kích thước - sẽ được gọi trong initDesignerTagging
function initWindowResizeHandler() {
    if (typeof jQuery !== 'undefined') {
        jQuery(window).on('resize', function() {
            // Sử dụng debounce để tránh gọi quá nhiều lần
            clearTimeout(window.resizeTimer);
            window.resizeTimer = setTimeout(function() {
                rescanMediaElements();
            }, 250);
        });
    }
}

// Thêm sự kiện để quét lại khi DOM thay đổi (sử dụng MutationObserver)
function setupMutationObserver() {
    // Kiểm tra xem trình duyệt có hỗ trợ MutationObserver không
    if (!window.MutationObserver) {
        return;
    }
    
    // Tạo một observer để theo dõi thay đổi DOM
    const observer = new MutationObserver(function(mutations) {
        let shouldRescan = false;
        
        // Kiểm tra xem có thay đổi nào liên quan đến iframe hoặc video không
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeName && (node.nodeName.toLowerCase() === 'iframe' || 
                                         node.nodeName.toLowerCase() === 'video' ||
                                         (node.querySelector && (node.querySelector('iframe') || node.querySelector('video'))))) {
                        shouldRescan = true;
                    }
                });
            }
        });
        
        // Nếu có thay đổi liên quan, quét lại
        if (shouldRescan) {
            rescanMediaElements();
        }
    });
    
    // Bắt đầu theo dõi thay đổi DOM
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
}

// Thiết lập MutationObserver sau khi trang đã tải xong
$(document).ready(function() {
    setTimeout(setupMutationObserver, 1000);
});

// Xử lý click vào nút "Clear All Tags"
$(document).on('click', '#clear_all_tags', function(e) {
    e.preventDefault();
    
    if (confirm('Are you sure you want to clear all tags? This action cannot be undone.')) {
        // Gửi AJAX để xóa tất cả tag
        $.post(designerTagging.ajax_url, {
            action: 'designer_clear_all_tags',
            nonce: designerTagging.nonce,
            post_id: designerTagging.post_id
        }, function(response) {
            if (response.success) {
                // Reset designerTagging.tagged_fields
                if (typeof designerTagging !== 'undefined') {
                    designerTagging.tagged_fields = '[]';
                }

                // Cập nhật nội dung của #tagged_fields
                $('.content-form').html(response.data.tagged_fields_html);

                // Xóa tất cả highlight và overlay trên trang
                $('.designer-highlight').removeClass('designer-highlight');
                $('[data-designer-editable]').removeAttr('data-designer-editable');
                $('[data-overlay-applied]').removeAttr('data-overlay-applied');
                $('.tagged-media-overlay').remove();

                // Quét lại các phần tử media
                rescanMediaElements();

                showToast('All tags cleared successfully!', 'success');
            } else {
                showToast('Error: ' + (response.data || 'Unknown error'), 'error');
            }
        });
    }
});

/**
 * Initialize customer edit mode functionality
 */
function initCustomerEditMode() {

    // Handle changes to customer-editable fields
    $(document).on('input change', '.customer-editable-field', function() {
        const $field = $(this);
        const selector = $field.data('selector');
        const fieldType = $field.data('field-type');
        const fieldSubtype = $field.data('field-subtype');
        const newValue = $field.val();

        
        // Update the actual element on the page
        updatePageElement(selector, fieldType, fieldSubtype, newValue);

        // Mark as changed for saving
        $field.addClass('customer-field-changed');
    });

    // Handle customer save button
    $(document).on('click', '#customer_save', function(e) {
        e.preventDefault();
        saveCustomerChanges();
    });

}

/**
 * Update the actual element on the page with new value
 */
function updatePageElement(selector, fieldType, fieldSubtype, newValue) {
    try {
        const $element = $(selector);
        if (!$element.length) {
            return;
        }

        switch (fieldType) {
            case 'text':
                $element.text(newValue);
                break;

            case 'image':
                if ($element.is('img')) {
                    $element.attr('src', newValue);
                } else {
                    $element.css('background-image', 'url(' + newValue + ')');
                }
                break;

            case 'link':
                if ($element.is('a')) {
                    $element.attr('href', newValue);
                } else {
                    $element.find('a').attr('href', newValue);
                }
                break;

            case 'button':
                if (fieldSubtype === 'label') {
                    $element.text(newValue);
                } else if (fieldSubtype === 'url') {
                    if ($element.is('a')) {
                        $element.attr('href', newValue);
                    } else {
                        $element.find('a').attr('href', newValue);
                    }
                }
                break;

            case 'video':
            case 'iframe':
                if ($element.is('iframe')) {
                    $element.attr('src', newValue);
                } else if ($element.is('video')) {
                    $element.find('source').attr('src', newValue);
                    $element[0].load(); // Reload video
                }
                break;

            default:
        }


    } catch (error) {
    }
}

/**
 * Save all customer changes
 */
function saveCustomerChanges() {
    const changedFields = $('.customer-field-changed');

    if (changedFields.length === 0) {
        showToast('No changes to save', 'info');
        return;
    }

    const changes = [];
    changedFields.each(function() {
        const $field = $(this);
        changes.push({
            selector: $field.data('selector'),
            fieldType: $field.data('field-type'),
            fieldSubtype: $field.data('field-subtype'),
            newValue: $field.val()
        });
    });


    // Send AJAX request to save changes
    $.post(designerTagging.ajax_url, {
        action: 'save_customer_changes',
        nonce: designerTagging.nonce,
        post_id: designerTagging.post_id,
        changes: JSON.stringify(changes)
    }, function(response) {
        if (response.success) {
            showToast('Changes saved successfully!', 'success');
            // Remove changed class from all fields
            changedFields.removeClass('customer-field-changed');
        } else {
            showToast('Error saving changes: ' + (response.data || 'Unknown error'), 'error');
        }
    }).fail(function() {
        showToast('Error saving changes: Network error', 'error');
    });
}

// Xử lý click vào nút "Clear All Sections"
$(document).on('click', '#clear_all_sections', function(e) {
    e.preventDefault();
    
    if (confirm('Are you sure you want to clear all sections? All tags will be moved to the default section. This action cannot be undone.')) {
        // Gửi AJAX để xóa tất cả section
        $.post(designerTagging.ajax_url, {
            action: 'designer_clear_all_sections',
            nonce: designerTagging.nonce,
            post_id: designerTagging.post_id
        }, function(response) {
            if (response.success) {
                // Cập nhật dropdown section
                if (response.data && response.data.sections_html) {
                    $('#designer-section').html(response.data.sections_html);
                }
                
                // Tải lại danh sách tag để hiển thị với section mới
                $.post(designerTagging.ajax_url, {
                    action: 'designer_get_tagged_fields',
                    nonce: designerTagging.nonce,
                    post_id: designerTagging.post_id
                }, function(response) {
                    if (response.success && response.data.tagged_fields_html) {
                        $('.content-form').html(response.data.tagged_fields_html);
                        
                        // Event delegation sẽ tự động áp dụng cho content mới
                    }
                });
                
                showToast('All sections cleared successfully! All tags moved to default section.', 'success');
            } else {
                showToast('Error: ' + (response.data || 'Unknown error'), 'error');
            }
        });
    }
});

// jQuery Safety Wrapper - Initialize when jQuery is available
function safeInitDesignerTagging() {
    if (typeof jQuery !== 'undefined') {
        jQuery(document).ready(initDesignerTagging);
    } else if (typeof $ !== 'undefined') {
        $(document).ready(initDesignerTagging);
    } else {
        setTimeout(function() {
            if (typeof jQuery !== 'undefined') {
                jQuery(document).ready(initDesignerTagging);
            } else if (typeof $ !== 'undefined') {
                $(document).ready(initDesignerTagging);
            } else {
            }
        }, 1000);
    }
}

// Initialize immediately
safeInitDesignerTagging();
