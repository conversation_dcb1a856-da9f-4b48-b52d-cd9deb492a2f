/* Site Theme CSS Variables and Styles */

:root {
  /* Default theme colors */
  --theme-primary: #ff6b35;
  --theme-secondary: #f7931e;
  --theme-accent: #c5a3ff;
  --theme-text: #333333;
  --theme-background: #ffffff;
  --theme-highlight: #10b981;
}

/* Apply theme colors to elements */
.theme-primary {
  color: var(--theme-primary) !important;
}

.theme-secondary {
  color: var(--theme-secondary) !important;
}

.theme-accent {
  color: var(--theme-accent) !important;
}

.theme-text {
  color: var(--theme-text) !important;
}

.theme-background {
  background-color: var(--theme-background) !important;
}

.theme-highlight {
  color: var(--theme-highlight) !important;
}

/* Background color classes */
.theme-primary-bg {
  background-color: var(--theme-primary) !important;
}

.theme-secondary-bg {
  background-color: var(--theme-secondary) !important;
}

.theme-accent-bg {
  background-color: var(--theme-accent) !important;
}

.theme-highlight-bg {
  background-color: var(--theme-highlight) !important;
}

/* Border color classes */
.theme-primary-border {
  border-color: var(--theme-primary) !important;
}

.theme-secondary-border {
  border-color: var(--theme-secondary) !important;
}

.theme-accent-border {
  border-color: var(--theme-accent) !important;
}

/* Button styles with theme colors */
.theme-button {
  background-color: var(--theme-primary);
  color: var(--theme-background);
  border: 1px solid var(--theme-primary);
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.theme-button:hover {
  background-color: var(--theme-secondary);
  border-color: var(--theme-secondary);
}

.theme-button-outline {
  background-color: transparent;
  color: var(--theme-primary);
  border: 1px solid var(--theme-primary);
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.theme-button-outline:hover {
  background-color: var(--theme-primary);
  color: var(--theme-background);
}

/* Link styles with theme colors */
a.theme-link {
  color: var(--theme-primary);
  text-decoration: none;
  transition: color 0.3s ease;
}

a.theme-link:hover {
  color: var(--theme-secondary);
}

/* Heading styles with theme colors */
h1.theme-heading,
h2.theme-heading,
h3.theme-heading,
h4.theme-heading,
h5.theme-heading,
h6.theme-heading {
  color: var(--theme-text);
}

/* Card styles with theme colors */
.theme-card {
  background-color: var(--theme-background);
  border: 1px solid var(--theme-accent);
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Input styles with theme colors */
.theme-input {
  border: 1px solid var(--theme-accent);
  border-radius: 4px;
  padding: 8px 12px;
  background-color: var(--theme-background);
  color: var(--theme-text);
  transition: border-color 0.3s ease;
}

.theme-input:focus {
  border-color: var(--theme-primary);
  outline: none;
  box-shadow: 0 0 0 2px rgba(var(--theme-primary), 0.2);
}

/* Alert/notification styles */
.theme-alert {
  padding: 12px 16px;
  border-radius: 4px;
  margin: 8px 0;
}

.theme-alert-primary {
  background-color: var(--theme-primary);
  color: var(--theme-background);
}

.theme-alert-secondary {
  background-color: var(--theme-secondary);
  color: var(--theme-background);
}

.theme-alert-accent {
  background-color: var(--theme-accent);
  color: var(--theme-text);
}

/* Navigation styles */
.theme-nav {
  background-color: var(--theme-background);
  border-bottom: 1px solid var(--theme-accent);
}

.theme-nav-item {
  color: var(--theme-text);
  padding: 8px 16px;
  text-decoration: none;
  transition: all 0.3s ease;
}

.theme-nav-item:hover {
  background-color: var(--theme-primary);
  color: var(--theme-background);
}

.theme-nav-item.active {
  background-color: var(--theme-secondary);
  color: var(--theme-background);
}

/* Footer styles */
.theme-footer {
  background-color: var(--theme-text);
  color: var(--theme-background);
  padding: 24px 0;
}

/* Responsive design helpers */
@media (max-width: 768px) {
  .theme-button,
  .theme-button-outline {
    padding: 6px 12px;
    font-size: 14px;
  }
  
  .theme-card {
    padding: 12px;
  }
}

/* Animation for theme transitions */
* {
  transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease;
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
  :root {
    --theme-background: #1a1a1a;
    --theme-text: #ffffff;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .theme-button,
  .theme-button-outline {
    border-width: 2px;
  }
  
  .theme-input {
    border-width: 2px;
  }
}

/* Print styles */
@media print {
  .theme-button,
  .theme-button-outline {
    background: none !important;
    color: black !important;
    border: 1px solid black !important;
  }
}

/* Color Theme Sidebar Styles */
.color-option {
  transition: all 0.3s ease;
}

.color-option:hover .color-preview {
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.color-option.selected-base .color-preview,
.accent-color-option.selected-accent .color-preview {
  border: 2px solid #00bcd4 !important;
  box-shadow: 0 0 0 2px rgba(0, 188, 212, 0.2);
}

.accent-color-option {
  transition: all 0.3s ease;
}

.accent-color-option:hover .color-preview {
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Custom slider styles */
#baseColorSlider {
  -webkit-appearance: none;
  appearance: none;
  height: 8px;
  border-radius: 4px;
  outline: none;
  cursor: pointer;
}

#baseColorSlider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #00bcd4;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

#baseColorSlider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #00bcd4;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Search input styles */
#colorThemeSearch {
  transition: all 0.3s ease;
}

#colorThemeSearch:focus {
  border-color: #00bcd4 !important;
  box-shadow: 0 0 0 2px rgba(0, 188, 212, 0.2);
}

/* Flex utilities for older browsers */
.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.mb-2 {
  margin-bottom: 8px;
}

/* Color preview animation */
.color-preview {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.color-preview::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.color-option:hover .color-preview::before,
.accent-color-option:hover .color-preview::before {
  left: 100%;
}

/* Info icon styles */
.fa-info-circle {
  cursor: help;
  transition: color 0.3s ease;
}

.fa-info-circle:hover {
  color: #00bcd4 !important;
}

/* Advanced Settings Sidebar Styles */
.collapsible-section {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
}

.section-header {
  background-color: #f5f5f5 !important;
  border: none !important;
  padding: 12px 16px !important;
  transition: background-color 0.3s ease;
}

.section-header:hover {
  background-color: #e9ecef !important;
}

.section-content {
  background-color: #ffffff;
  border-top: 1px solid #e0e0e0;
}

.color-picker-item {
  transition: background-color 0.3s ease;
}

.color-picker-item:hover {
  background-color: #f8f9fa;
}

.color-picker-box {
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.color-picker-box:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.section-arrow {
  transition: transform 0.3s ease;
}

.section-arrow.rotated {
  transform: rotate(180deg);
}

/* Button Tab Styles */
.button-tabs {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
}

.tab-button {
  transition: all 0.3s ease;
  font-weight: 500;
}

.tab-button:hover {
  background-color: #e9ecef !important;
}

.tab-button.active {
  background-color: #f5f5f5 !important;
  font-weight: 600;
}

.tab-content {
  padding-top: 8px;
}

/* Advanced Settings Form Elements */
#advancedSidebar .w3-select,
#advancedSidebar .w3-input {
  transition: all 0.3s ease;
}

#advancedSidebar .w3-select:focus,
#advancedSidebar .w3-input:focus {
  border-color: #00bcd4 !important;
  box-shadow: 0 0 0 2px rgba(0, 188, 212, 0.2);
}

/* Advanced Settings Search */
#advancedSearch {
  transition: all 0.3s ease;
}

#advancedSearch:focus {
  border-color: #00bcd4 !important;
  box-shadow: 0 0 0 2px rgba(0, 188, 212, 0.2);
}

/* Advanced Settings Apply/Reset Buttons */
#advancedSidebar .w3-button {
  transition: all 0.3s ease;
}

#advancedSidebar .w3-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Advanced Settings CSS Variables */
:root {
  --advanced-primary-bg: #e3f2fd;
  --advanced-secondary-bg: #f5f5f5;
  --advanced-divider-color: #e0e0e0;
  --advanced-title-color: #000000;
  --advanced-subtitle-color: #666666;
  --advanced-body-text-color: #333333;
  --advanced-secondary-text-color: #2e7d32;
  --advanced-link-action-color: #4dd0e1;
  --advanced-button-fill-color: #2196f3;
  --advanced-button-border-color: #2196f3;
  --advanced-button-text-color: #2196f3;
  --advanced-button-hover-fill-color: #e3f2fd;
  --advanced-button-hover-border-color: #e3f2fd;
  --advanced-button-hover-text-color: #e3f2fd;
  --advanced-heading-font: arial;
  --advanced-paragraph-font: arial;
}

/* Advanced Settings Applied Styles */
.advanced-primary-bg {
  background-color: var(--advanced-primary-bg) !important;
}

.advanced-secondary-bg {
  background-color: var(--advanced-secondary-bg) !important;
}

.advanced-divider {
  border-color: var(--advanced-divider-color) !important;
}

.advanced-title {
  color: var(--advanced-title-color) !important;
  font-family: var(--advanced-heading-font) !important;
}

.advanced-subtitle {
  color: var(--advanced-subtitle-color) !important;
  font-family: var(--advanced-heading-font) !important;
}

.advanced-body-text {
  color: var(--advanced-body-text-color) !important;
  font-family: var(--advanced-paragraph-font) !important;
}

.advanced-secondary-text {
  color: var(--advanced-secondary-text-color) !important;
}

.advanced-link {
  color: var(--advanced-link-action-color) !important;
}

.advanced-button {
  background-color: var(--advanced-button-fill-color) !important;
  border-color: var(--advanced-button-border-color) !important;
  color: var(--advanced-button-text-color) !important;
}

.advanced-button:hover {
  background-color: var(--advanced-button-hover-fill-color) !important;
  border-color: var(--advanced-button-hover-border-color) !important;
  color: var(--advanced-button-hover-text-color) !important;
}

/* Responsive adjustments for Advanced Settings */
@media (max-width: 768px) {
  .color-picker-box {
    width: 25px !important;
    height: 18px !important;
  }

  .tab-button {
    padding: 6px !important;
    font-size: 14px;
  }

  .section-header {
    padding: 10px 12px !important;
  }
}
